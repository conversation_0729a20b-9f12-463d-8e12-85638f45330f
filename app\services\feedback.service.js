const Feedback = require('../models/feedback.model');

/**
 * Create Feedback
 *
 * @param {*} requestData
 * @returns
 */
exports.createFeedback = async requestData => await Feedback.create(requestData);

/**
 * Filter Feedback Data
 *
 * @param {*} filter
 * @param {*} page
 * @param {*} perPage
 * @param {*} sort
 * @returns
 */
exports.filterFeedback = async (filter, page, perPage, sort) => {
  return Feedback.find(filter, {
    deletedAt: 0,
    deletedBy: 0,
    __v: 0,
  })
    .sort({ createdAt: sort })
    .limit(perPage)
    .skip(page * perPage)
    .populate([
      {
        path: 'account',
        select: { _id: 1, name: 1 },
        strictPopulate: false,
      },
      {
        path: 'createdBy user',
        select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
        strictPopulate: false,
      },
      {
        path: 'updatedBy user',
        select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
        strictPopulate: false,
      },
    ]);
};

/**
 * Update Feedback
 *
 * @param {*} id
 * @param {*} requestData
 * @returns
 */
exports.updateFeedback = async (id, requestData) => {
  return Feedback.findByIdAndUpdate(id, { $set: requestData }, { new: true }).populate([
    {
      path: 'account',
      select: { _id: 1, name: 1 },
      strictPopulate: false,
    },
    {
      path: 'createdBy user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      strictPopulate: false,
    },
    {
      path: 'updatedBy user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      strictPopulate: false,
    },
  ]);
};

/**
 * Get Feedback By Id
 *
 * @param {*} id
 * @returns
 */
exports.getFeedbackById = async id => {
  return Feedback.findById(id).populate([
    {
      path: 'account',
      select: { _id: 1, name: 1 },
      strictPopulate: false,
    },
    {
      path: 'createdBy user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      strictPopulate: false,
    },
    {
      path: 'updatedBy user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      strictPopulate: false,
    },
  ]);
};
