const mongoose = require('mongoose');

const ReturnOrderHistory = new mongoose.Schema(
  {
    equipment: {
      type: mongoose.Types.ObjectId,
      ref: 'equipment',
    },
    returnOrder: {
      type: mongoose.Types.ObjectId,
      ref: 'return-order',
      default: null,
    },
    status: {
      type: String,
      enum: ['', 'pre-return', 'return', 'in-stock', 'rejected', 'partially-returned'],
      default: '',
    },
    subStatus: {
      type: String,
      enum: ['', 'ok', 'transfer', 'received', 'quarantine', 'write-off'],
      default: '',
    },
    checkinReasonStatus: {
      type: String,
      enum: ['', 'repair-required', 'certification-required', 'damaged', 'missing', 'other'],
      default: '',
    },
    checkinReason: {
      type: String,
      default: null,
    },
    pmReceivedQuantity: {
      type: Number,
      default: null,
    },
    pmDispatchQuantity: {
      type: Number,
      default: null,
    },
    wmReceivedQuantity: {
      type: Number,
      default: null,
    },
    pmComment: {
      type: String,
      default: null,
    },
    wmComment: [
      {
        user: {
          type: mongoose.Types.ObjectId,
          ref: 'user',
        },
        time: {
          type: Date,
          default: null,
        },
        status: {
          type: String,
          default: '',
        },
        comment: {
          type: String,
          default: '',
        },
      },
    ],
    project: {
      type: mongoose.Types.ObjectId,
      ref: 'project',
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('return-order-history', ReturnOrderHistory);
