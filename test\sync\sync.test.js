const request = require('supertest');
const app = require('../../app/server');

// create sync up
describe('POST /api/sync/up', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.I513fTkTfDgrekZVxyysoPEtU5Rmtr3nM-7HB7Hj7hE';
  const syncData = {
    type: 'HSECARD',
    data: [
      {
        project: '64119ddc6c1d88fde480fd47',
        location: 'Ahmedabad',
        description: 'Description',
        account: '63f34946ccf1c13060d353ee',
        type: '6417eb8eaee3ff61d5b961ab',
        severity: '',
        likelihood: '',
        actionsTaken: 'eqweqw',
        stage: 'Open',
        images: [],
        dynamicFields: [],
        createdBy: '641170af8e0d16ff25a1b0d0',
      },
      {
        project: '64119ddc6c1d88fde480fd47',
        location: 'Ahmedabad',
        description: 'Description2',
        account: '63f34946ccf1c13060d353ee',
        type: '6417eb8eaee3ff61d5b961ab',
        severity: '',
        likelihood: '',
        actionsTaken: 'eqweqw2',
        stage: 'Open2',
        images: [],
        dynamicFields: [],
        createdBy: '641170af8e0d16ff25a1b0d0',
      },
    ],
  };

  it('returns 200 and message Safety Card has been created successfully', async () => {
    const response = await request(app)
      .post('/api/sync/up')
      .set('Authorization', `Bearer ${token}`)
      .send(syncData);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Safety Card has been created successfully',
      status: true,
    });
  });

  it('returns 422 with message Required field cannot be empty.', async () => {
    const response = await request(app)
      .post('/api/sync/up')
      .set('Authorization', `Bearer ${token}`)
      .send(syncData);
    expect(response.status).toBe(422);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Required field cannot be empty.',
      status: false,
    });
  });

  it('returns 404 with message Safety Card already exist', async () => {
    const response = await request(app)
      .post('/api/sync/up')
      .set('Authorization', `Bearer ${token}`)
      .send(syncData);
    expect(response.status).toBe(404);
    expect(response.body).toEqual({
      message: 'Safety Card already exist',
      status: false,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).post('/api/sync/up').send(syncData);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});
