// library
const express = require('express');
const routes = express.Router();

// middleware
const { verifyToken, deletedAt, authAccount } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const assetController = require('../controllers/asset.controller');

// Validator
const validator = require('../validators/asset.validator');

// create asset
routes.post(
  '',
  verifyToken,
  authAccount,
  validator.assetValidationRule(),
  validate,
  assetController.createAsset
);

// update asset
routes.patch(
  '/:id',
  verifyToken,
  validator.updateAssetValidationRule(),
  validate,
  assetController.updateAsset
);

// delete asset
routes.delete('/:id', verifyToken, deletedAt, validate, assetController.deleteAsset);

module.exports = routes;
