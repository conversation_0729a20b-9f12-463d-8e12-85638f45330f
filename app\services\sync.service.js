const reportService = require('../services/report.service');
const authService = require('../services/auth.service');
const commonUtils = require('../utils/common.utils');
const constantUtils = require('../utils/constants.utils');
const commonFunction = require('../utils/common-function.utils');

// Service
const safetyCardService = require('../services/safety-card.service');
const shiftService = require('../services/shift.service');
const shiftActivityService = require('../services/shift-activity.service');
const memberService = require('../services/member.service');
const teamMemberService = require('../services/team-member.service');
const jobService = require('../services/job.service');
const projectService = require('../services/project.service');
const modifyDefaultDataUtils = require('../utils/modify-default-data.utils');
const userReportService = require('../services/user-report.service');
const userReportAnswerService = require('../services/user-report-answer.service');
const toolboxTalkService = require('../services/toolbox-talk.service');
const shiftActvityServices = require('../services/shift-activity.service');
const userProjectReportService = require('../services/user-project-report.service');
const notInListService = require('../services/not-in-list.service');
const projectDocumentService = require('../services/project-document.service');

/**
 * Create Card
 *
 * @param {*} cardData
 * @param {*} tokenData
 * @returns
 */
exports.createCard = async (cardData, tokenData) => {
  let reqData = cardData;
  reqData.project = cardData.project !== '' ? cardData.project : null;
  reqData.severity = cardData.severity !== '' ? cardData.severity : null;
  reqData.likelihood = cardData.likelihood !== '' ? cardData.likelihood : null;
  reqData.category = cardData.category !== '' ? cardData.category : null;
  reqData.type = cardData.type !== '' ? cardData.type : null;
  reqData.item = cardData.item !== '' ? cardData.item : '';
  reqData.actionsTaken = cardData.actionsTaken !== '' ? cardData.actionsTaken : '';
  reqData.correctiveAction = cardData.correctiveAction !== '' ? cardData.correctiveAction : '';
  reqData.preventiveAction = cardData.preventiveAction !== '' ? cardData.preventiveAction : '';
  reqData.estimatedDelayCost =
    cardData.estimatedDelayCost !== '' ? cardData.estimatedDelayCost : '';
  reqData.correctiveAction = cardData.correctiveAction !== '' ? cardData.correctiveAction : '';
  reqData.stage = cardData.stage ? cardData.stage : 'Open';
  reqData.cardType = cardData.cardType;
  reqData.account = tokenData.account;
  reqData.createdBy = tokenData.id;
  return reqData;
};

/**
 * Report Sync
 *
 * @param {*} data
 * @param {*} req
 * @param {*} subId
 * @param {*} jobData
 * @returns
 */
exports.reportSync = async (data, req, subId, jobData) => {
  for (let report of data) {
    try {
      let reportId =
        Object.keys(report).indexOf('_id') != -1 && commonUtils.isValidId(report._id)
          ? report._id
          : '';
      let exist = reportId !== '' ? await reportService.getReportById(reportId) : false;
      if (reportId !== '' && exist) {
        await reportService.updateReport(reportId, report);
        subId.push({ id: reportId, status: 'success' });
      } else {
        const account = await authService.getAuthAccountId(req.headers.authorization);
        report.account = account;
        report.createdBy = req.userData.id;

        const createdReport = await reportService.createReport(report);
        subId.push({ id: createdReport._id, status: 'success' });
      }
    } catch (error) {
      subId.push({
        id: error.keyValue?._id || error.errors?._id?.value || error?.value,
        status: 'failed',
        comment: error.message,
      });
      continue;
    }
  }
  await jobService.updateJob(jobData._id, { status: 'success', subId });
  // eslint-disable-next-line no-case-declarations
  const response = await jobService.getJobById(jobData._id);
  const modifiedResponse = response.subId.map(report => {
    // eslint-disable-next-line no-unused-vars
    const { _id, ...rest } = report.toObject();
    return rest;
  });

  return modifiedResponse;
};

/**
 * Manage Report Sync
 *
 * @param {*} data
 * @param {*} req
 * @param {*} subId
 * @param {*} jobData
 * @param {*} tokenData
 * @returns
 */
exports.manageReportSync = async (data, req, subId, jobData, tokenData) => {
  for (let reportData of data) {
    try {
      let userId = tokenData.id;
      let {
        project,
        report,
        location,
        asset,
        reportQuestions,
        _id,
        isDeleted,
        signature,
        signatureBy,
        reportStatus,
      } = reportData;
      if (!commonUtils.isValidId(_id)) {
        throw new Error(constantUtils.INVALID_ID);
      }
      let filter = {
        project,
        report,
        location,
        ...(asset && { asset }),
        ...(signature && { signature }),
        ...(reportStatus && { status: reportStatus }),
      };

      if (signature) {
        filter.signatureBy = signatureBy || userId;
      }
      let existReport = await userReportService.getUserReportById(_id);
      if (existReport) {
        //Update user report
        filter.updatedBy = userId;
        filter.updatedAt = new Date();
        await userReportService.updateUserReportById(_id, filter);
        // Delete user report
        if (isDeleted && isDeleted === true) {
          let updateDeleteData = {
            deletedBy: userId,
            deletedAt: new Date(),
          };
          let updateResponse = await userReportService.updateUserReportById(
            existReport._id,
            updateDeleteData
          );

          if (updateResponse) {
            await userReportAnswerService.updateUserReportAnswerManyByFilter(
              {
                userReport: existReport._id,
                deletedAt: null,
              },
              updateDeleteData
            );
          }
          subId.push({ id: existReport._id, status: 'success' });
          continue;
        }
        for (let reportQuestion of reportQuestions) {
          for (let element of reportQuestion.userAnswers) {
            let filterAnswer = {
              userReport: existReport._id,
              reportQuestion: reportQuestion.questionId,
              reportQuestionAnswer: element.answerId,
              report,
              account: tokenData.account,
              createdBy: userId,
            };

            let existAnswer = await userReportAnswerService.getSingleUserReportAnswer(filterAnswer);
            if (existAnswer) {
              // update user answer
              await userReportAnswerService.updateUserReportAnswer(existAnswer._id, {
                answers: element.answers,
                updatedBy: req.userData._id,
                updatedAt: new Date(),
              });
            } else {
              // create user answer
              filterAnswer.createdAt = new Date();
              filterAnswer.answers = element.answers;
              await userReportAnswerService.createUserReportAnswer(filterAnswer);
            }
          }
        }
        subId.push({ id: existReport._id, status: 'success' });
      } else if (isDeleted && isDeleted === true) {
        subId.push({ id: _id, status: 'success' });
      } else {
        // create user report
        filter.createdBy = userId;
        filter.createdAt = new Date();
        filter.account = tokenData.account;
        // check user report exist
        const reportExist = await userReportService.searchUserReport({ _id });
        if (!reportExist) {
          filter._id = _id;
        }

        // create user project report
        const userProjectReport = await userProjectReportService.checkAndCreateUserProjectReport(
          {
            project,
            report,
            location,
            ...(asset && { asset }),
          },
          tokenData.account,
          userId
        );
        filter.userProjectReport = userProjectReport._id || null;

        // create user report
        const createdReport = await userReportService.createUserReport(filter);
        if (createdReport) {
          for (let reportQuestion of reportQuestions) {
            for (let element of reportQuestion.userAnswers) {
              let createUserAnswer = {
                userReport: createdReport._id,
                reportQuestion: reportQuestion.questionId,
                reportQuestionAnswer: element.answerId,
                report,
                answers: element.answers,
                account: tokenData.account,
                createdBy: userId,
                createdAt: new Date(),
              };
              await userReportAnswerService.createUserReportAnswer(createUserAnswer);
            }
          }
        }
        subId.push({ id: createdReport._id, status: 'success' });
      }
    } catch (error) {
      subId.push({
        id: error.keyValue?._id || error.errors?._id?.value || error?.value,
        status: 'failed',
        comment: error.message,
      });
      continue;
    }
  }
  await jobService.updateJob(jobData._id, { status: 'success', subId });
  // eslint-disable-next-line no-case-declarations
  const response = await jobService.getJobById(jobData._id);
  const modifiedResponse = response.subId.map(report => {
    // eslint-disable-next-line no-unused-vars
    const { _id, ...rest } = report.toObject();
    return rest;
  });

  return modifiedResponse;
};

/**
 * Shift Sync
 *
 * @param {*} data
 * @param {*} req
 * @param {*} subId
 * @param {*} jobData
 * @returns
 */
exports.shiftSync = async (data, req, subId, jobData) => {
  for (const shiftData of req.body.data) {
    try {
      shiftData.account = req.userData.account;
      const shift = await shiftService.getShiftById(shiftData._id);
      if (shift) {
        if ('member' in shiftData) {
          const teamMembers = await teamMemberService.getTeamMemberByShiftId(shift._id);
          if (teamMembers.length > 0) {
            teamMembers.forEach(async member => {
              await teamMemberService.deleteTeamMember(member._id, req.deletedAt);
            });
          }
          const projectName = await projectService.getProjectById(
            shiftData.project,
            req.userData.account
          );
          // If default project is selected
          if (shiftData.isDeleted) {
            shiftData.project = shift.project._id;
            shiftData.defaultProject = shift.defaultProject;
            shiftData.team = shift.team._id;
            delete shiftData.member;
            shiftData.updatedBy = req.userData.id;
            await shiftService.updateShift(shiftData._id, shiftData);
            subId.push({ id: shiftData._id, status: 'success' });
          } else {
            if (projectName.defaultIdentifier == global.constant.DEFAULT_DATA_IDENTIFIER) {
              shiftData.defaultProject = await safetyCardService.getSyncDefaultProject(
                shiftData,
                req.userData.account
              );
              shiftData.isDefault = true;
              shiftData.defaultIdentifier = 'default';
            } else {
              shiftData.defaultProject = null;
              shiftData.isDefault = false;
            }

            // member
            if (projectName.defaultIdentifier == global.constant.DEFAULT_DATA_IDENTIFIER) {
              shiftData.member = await modifyDefaultDataUtils.getDefaultUpdateMember(
                shiftData.member,
                projectName._id,
                req.userData.account
              );
            }
            // member

            // team
            if (projectName.defaultIdentifier == global.constant.DEFAULT_DATA_IDENTIFIER) {
              shiftData.team = await modifyDefaultDataUtils.getSyncDefaultTeam(
                shiftData,
                req.userData.account
              );
              shiftData.isDefault = true;
            }
            // team
            // End of if default project is selected
            for (const member of shiftData.member) {
              try {
                const filerData = {
                  _id: commonUtils.toObjectId(member),
                  account: req.userData.account,
                };
                const memberD = await memberService.getMemberById(filerData);

                const memberReq = {
                  member: memberD._id,
                  function: memberD.function,
                  shift: shiftData._id,
                  createdBy: req.userData.id,
                  updatedBy: req.userData.id,
                  deletedBy:
                    'isDeleted' in shiftData && shiftData.isDeleted ? req.userData.id : null,
                  deletedAt: 'isDeleted' in shiftData && shiftData.isDeleted ? new Date() : null,
                };
                await teamMemberService.createTeamMembers(memberReq);
              } catch (error) {
                subId.push({
                  id: shiftData._id,
                  status: 'failed',
                  comment: 'Team member has invalid id',
                });
                continue;
              }
            }
          }
          shiftData.updatedBy = req.userData.id;

          await shiftService.updateShift(shiftData._id, shiftData);
          subId.push({ id: shiftData._id, status: 'success' });
        }

        if ('notInList' in shiftData) {
          await notInListService.deleteNotInListByShiftId(shiftData._id);

          if (shiftData.notInList && shiftData.notInList.length) {
            for (const element of shiftData.notInList) {
              await notInListService.addMemberAndFunctionToNotInList({
                ...element,
                shift: shift._id,
                project: shiftData.project,
                account: req.userData.account,
                createdBy: req.userData._id,
                updatedBy: req.userData._id,
              });
            }
          }
        }
      } else {
        const projectName = await projectService.getProjectById(
          shiftData.project,
          req.userData.account
        );
        // If default project is selected
        if (projectName.defaultIdentifier == global.constant.DEFAULT_DATA_IDENTIFIER) {
          shiftData.defaultProject = await safetyCardService.getSyncDefaultProject(
            shiftData,
            req.userData.account
          );
          shiftData.isDefault = true;
          shiftData.defaultIdentifier = 'default';
        } else {
          shiftData.defaultProject = null;
          shiftData.isDefault = false;
        }

        // team
        if (projectName.defaultIdentifier == global.constant.DEFAULT_DATA_IDENTIFIER) {
          shiftData.team = await modifyDefaultDataUtils.getSyncDefaultTeam(
            shiftData,
            req.userData.account
          );
          shiftData.isDefault = true;
        }
        // team

        // member
        if (projectName.defaultIdentifier == global.constant.DEFAULT_DATA_IDENTIFIER) {
          shiftData.member = await modifyDefaultDataUtils.getDefaultMember(
            shiftData.member,
            projectName._id,
            req.userData.account
          );
        }
        // member
        // End of if default project is selected
        shiftData.createdBy = req.userData.id;
        shiftData.updatedBy = req.userData.id;
        const createdShift = await shiftService.createShift(shiftData);
        for (const member of shiftData.member) {
          try {
            const filerData = {
              _id: commonUtils.toObjectId(member),
              account: req.userData.account,
            };
            const memberD = await memberService.getMemberById(filerData);
            await teamMemberService.deleteTeamMember(memberD._id, req.deletedAt);

            const memberReq = {
              member: memberD._id,
              function: memberD.function,
              shift: shiftData._id,
              createdBy: req.userData.id,
              updatedBy: req.userData.id,
              deletedBy: 'isDeleted' in shiftData && shiftData.isDeleted ? req.userData.id : null,
              deletedAt: 'isDeleted' in shiftData && shiftData.isDeleted ? new Date() : null,
            };
            await teamMemberService.createTeamMembers(memberReq);
          } catch (error) {
            subId.push({
              id: error.keyValue?._id || error.errors?._id?.value || error?.value,
              status: 'failed',
              comment: error.message,
            });
            continue;
          }
        }
        if (shiftData.notInList && shiftData.notInList.length) {
          for (const element of shiftData.notInList) {
            await notInListService.addMemberAndFunctionToNotInList({
              ...element,
              shift: createdShift._id,
              project: shiftData.project,
              account: req.userData.account,
              createdBy: req.userData._id,
              updatedBy: req.userData._id,
            });
          }
        }
        subId.push({ id: createdShift._id, status: 'success' });
      }
    } catch (error) {
      subId.push({
        id: error.keyValue?._id || error.errors?._id?.value || error?.value,
        status: 'failed',
        comment: error.message,
      });
      continue;
    }
  }
  await jobService.updateJob(jobData._id, { status: 'success', subId });
  // eslint-disable-next-line no-case-declarations
  const response = await jobService.getJobById(jobData._id);
  const modifiedResponse = response.subId.map(shift => {
    // eslint-disable-next-line no-unused-vars
    const { _id, ...rest } = shift.toObject();
    return rest;
  });

  return modifiedResponse;
};

/**
 * Shift Sync
 *
 * @param {*} data
 * @param {*} req
 * @param {*} subId
 * @param {*} jobData
 * @returns
 */
exports.shiftSyncV2 = async (data, req, subId, jobData) => {
  for (const shiftData of req.body.data) {
    try {
      shiftData.account = req.userData.account;
      const shift = await shiftService.getShiftById(shiftData._id);
      if (shift) {
        if ('member' in shiftData) {
          const teamMembers = await teamMemberService.getTeamMemberByShiftId(shift._id);
          if (teamMembers.length > 0) {
            teamMembers.forEach(async member => {
              await teamMemberService.deleteTeamMember(member._id, req.deletedAt);
            });
          }
          const projectName = await projectService.getProjectById(
            shiftData.project,
            req.userData.account
          );
          // If default project is selected
          if (shiftData.isDeleted) {
            shiftData.project = shift.project._id;
            shiftData.defaultProject = shift.defaultProject;
            shiftData.team = shift.team._id;
            delete shiftData.member;
            shiftData.updatedBy = req.userData.id;
            await shiftService.updateShift(shiftData._id, shiftData);
            subId.push({ id: shiftData._id, status: 'success' });
          } else {
            if (projectName.defaultIdentifier == global.constant.DEFAULT_DATA_IDENTIFIER) {
              shiftData.defaultProject = await safetyCardService.getSyncDefaultProject(
                shiftData,
                req.userData.account
              );
              shiftData.isDefault = true;
              shiftData.defaultIdentifier = 'default';
            } else {
              shiftData.defaultProject = null;
              shiftData.isDefault = false;
            }

            // member
            if (projectName.defaultIdentifier == global.constant.DEFAULT_DATA_IDENTIFIER) {
              shiftData.member = await modifyDefaultDataUtils.getDefaultUpdateMember(
                shiftData.member,
                projectName._id,
                req.userData.account
              );
            }
            // member

            // team
            if (projectName.defaultIdentifier == global.constant.DEFAULT_DATA_IDENTIFIER) {
              shiftData.team = await modifyDefaultDataUtils.getSyncDefaultTeam(
                shiftData,
                req.userData.account
              );
              shiftData.isDefault = true;
            }
            // team
            // End of if default project is selected
            for (const member of shiftData.member) {
              try {
                const filerData = {
                  _id: commonUtils.toObjectId(member),
                  account: req.userData.account,
                };
                const memberD = await memberService.getMemberById(filerData);

                const memberReq = {
                  member: memberD._id,
                  function: memberD.function,
                  shift: shiftData._id,
                  createdBy: req.userData.id,
                  updatedBy: req.userData.id,
                  deletedBy:
                    'isDeleted' in shiftData && shiftData.isDeleted ? req.userData.id : null,
                  deletedAt: 'isDeleted' in shiftData && shiftData.isDeleted ? new Date() : null,
                };
                await teamMemberService.createTeamMembers(memberReq);
              } catch (error) {
                subId.push({
                  id: shiftData._id,
                  status: 'failed',
                  comment: 'Team member has invalid id',
                });
                continue;
              }
            }
          }
          shiftData.updatedBy = req.userData.id;

          await shiftService.updateShift(shiftData._id, shiftData);
          subId.push({ id: shiftData._id, status: 'success' });
        }

        if ('notInList' in shiftData) {
          await notInListService.deleteNotInListByShiftId(shiftData._id);

          if (shiftData.notInList && shiftData.notInList.length) {
            for (const element of shiftData.notInList) {
              await notInListService.addMemberAndFunctionToNotInList({
                ...element,
                shift: shift._id,
                project: shiftData.project,
                account: req.userData.account,
                createdBy: req.userData._id,
                updatedBy: req.userData._id,
              });
            }
          }
        }
      } else {
        const projectName = await projectService.getProjectById(
          shiftData.project,
          req.userData.account
        );
        // If default project is selected
        if (projectName.defaultIdentifier == global.constant.DEFAULT_DATA_IDENTIFIER) {
          shiftData.defaultProject = await safetyCardService.getSyncDefaultProject(
            shiftData,
            req.userData.account
          );
          shiftData.isDefault = true;
          shiftData.defaultIdentifier = 'default';
        } else {
          shiftData.defaultProject = null;
          shiftData.isDefault = false;
        }

        // team
        if (projectName.defaultIdentifier == global.constant.DEFAULT_DATA_IDENTIFIER) {
          shiftData.team = await modifyDefaultDataUtils.getSyncDefaultTeam(
            shiftData,
            req.userData.account
          );
          shiftData.isDefault = true;
        }
        // team

        // member
        if (projectName.defaultIdentifier == global.constant.DEFAULT_DATA_IDENTIFIER) {
          shiftData.member = await modifyDefaultDataUtils.getDefaultMember(
            shiftData.member,
            projectName._id,
            req.userData.account
          );
        }
        // member
        // End of if default project is selected
        shiftData.createdBy = req.userData.id;
        shiftData.updatedBy = req.userData.id;
        const createdShift = await shiftService.createShift(shiftData);
        for (const member of shiftData.member) {
          try {
            const filerData = {
              _id: commonUtils.toObjectId(member),
              account: req.userData.account,
            };
            const memberD = await memberService.getMemberById(filerData);
            await teamMemberService.deleteTeamMember(memberD._id, req.deletedAt);

            const memberReq = {
              member: memberD._id,
              function: memberD.function,
              shift: shiftData._id,
              createdBy: req.userData.id,
              updatedBy: req.userData.id,
              deletedBy: 'isDeleted' in shiftData && shiftData.isDeleted ? req.userData.id : null,
              deletedAt: 'isDeleted' in shiftData && shiftData.isDeleted ? new Date() : null,
            };
            await teamMemberService.createTeamMembers(memberReq);
          } catch (error) {
            subId.push({
              id: error.keyValue?._id || error.errors?._id?.value || error?.value,
              status: 'failed',
              comment: error.message,
            });
            continue;
          }
        }
        if (shiftData.notInList && shiftData.notInList.length) {
          for (const element of shiftData.notInList) {
            await notInListService.addMemberAndFunctionToNotInList({
              ...element,
              shift: createdShift._id,
              project: shiftData.project,
              account: req.userData.account,
              createdBy: req.userData._id,
              updatedBy: req.userData._id,
            });
          }
        }
        subId.push({ id: createdShift._id, status: 'success' });
      }
    } catch (error) {
      subId.push({
        id: error.keyValue?._id || error.errors?._id?.value || error?.value,
        status: 'failed',
        comment: error.message,
      });
      continue;
    }
  }
  await jobService.updateJob(jobData._id, { status: 'success', subId });
  // eslint-disable-next-line no-case-declarations
  const response = await jobService.getJobById(jobData._id);
  const modifiedResponse = response.subId.map(shift => {
    // eslint-disable-next-line no-unused-vars
    const { _id, ...rest } = shift.toObject();
    return rest;
  });

  return modifiedResponse;
};

/**
 * Shift Activity Sync
 *
 * @param {*} data
 * @param {*} req
 * @param {*} subId
 * @param {*} jobData
 * @returns
 */
exports.shiftActivitySync = async (data, req, subId, jobData) => {
  for (let shiftActivityData of req.body.data) {
    try {
      let filter = { _id: shiftActivityData._id };
      if (shiftActivityData.isDeleted) {
        shiftActivityData.deletedBy = req.userData.id;
      } else {
        shiftActivityData.deletedBy = null;
      }
      const shiftActivity = await shiftActivityService.getSingleRecord(filter);
      const shiftData = await shiftService.getShiftById(shiftActivityData.shift);

      if (shiftActivity) {
        // other project
        shiftActivityData = await modifyDefaultDataUtils.changeShiftActivity(
          shiftActivityData,
          shiftData,
          req
        );
        // end other project
        await shiftActivityService.updateShiftActivity(shiftActivityData._id, shiftActivityData);

        let shiftActivities = await shiftActvityServices.getShiftActivityDataByFilter({
          shift: shiftData._id,
          deletedAt: null,
        });

        const activityUpdateResult = await commonFunction.calculateActivityDuration(
          shiftActivities,
          shiftData
        );

        if (!activityUpdateResult) {
          subId.push({
            id: shiftActivities._id,
            status: 'failed',
            comment: 'failed to calculate duration in update shift activity',
          });
          continue;
        }
        subId.push({ id: shiftActivityData._id, status: 'success' });
      } else {
        // other project
        shiftActivityData = await modifyDefaultDataUtils.changeShiftActivity(
          shiftActivityData,
          shiftData,
          req
        );
        // // end asset
        // end other project
        const shiftActivity = await shiftActivityService.createShiftActivity(shiftActivityData);

        let shiftActivities = await shiftActvityServices.getShiftActivityDataByFilter({
          shift: shiftData._id,
          deletedAt: null,
        });

        const activityUpdateResult = await commonFunction.calculateActivityDuration(
          shiftActivities,
          shiftData
        );

        if (!activityUpdateResult) {
          subId.push({
            id: shiftActivities._id,
            status: 'failed',
            comment: 'failed to calculate duration in create shift activity',
          });
          continue;
        }
        subId.push({ id: shiftActivity._id, status: 'success' });
      }
    } catch (error) {
      subId.push({
        id: error.keyValue?._id || error.errors?._id?.value || error?.value,
        status: 'failed',
        comment: error.message,
      });
      continue;
    }
  }
  await jobService.updateJob(jobData._id, { status: 'success', subId });
  // eslint-disable-next-line no-case-declarations
  const response = await jobService.getJobById(jobData._id);
  const modifiedResponse = response.subId.map(shiftActivity => {
    // eslint-disable-next-line no-unused-vars
    const { _id, ...rest } = shiftActivity.toObject();
    return rest;
  });

  return modifiedResponse;
};

/**
 * Hsc Card Sync
 *
 * @param {*} data
 * @param {*} req
 * @param {*} subId
 * @param {*} jobData
 * @param {*} tokenData
 * @returns
 */
exports.hscCardSync = async (data, req, subId, jobData, tokenData) => {
  for (const cardData of data) {
    const cardExist = await safetyCardService.getSafetyCardById(cardData._id);
    const project = await projectService.getProjectById(cardData.project, req.userData.account);

    // If default project is selected
    if (project.defaultIdentifier == global.constant.DEFAULT_DATA_IDENTIFIER) {
      cardData.defaultProject = await safetyCardService.getDefaultSyncUpProject(
        cardData,
        req.userData.account
      );
      cardData.isDefault = true;
    } else {
      cardData.defaultProject = null;
      cardData.isDefault = false;
    }
    if (!commonUtils.isValidId(cardData.location)) {
      cardData.location = await safetyCardService.getSyncUplocation(cardData, req.userData.account);
      cardData.isDefault = true;
    }
    // End of if default project is selected
    if ('severity' in cardData && 'likelihood' in cardData) {
      const result = await safetyCardService.calculateSyncRiskFactor(cardData);
      cardData.riskFactor = result;
    } else {
      cardData.riskFactor = null;
    }

    let reqData = await this.createCard(cardData, tokenData);

    try {
      if (cardExist) {
        if ('severity' in cardData && 'likelihood' in cardData) {
          const result = await safetyCardService.calculateSyncRiskFactor(cardData);
          cardData.riskFactor = result;
        } else {
          cardData.riskFactor = null;
        }
        cardData.updatedBy = req.userData.id;
        reqData.updatedBy = req.userData.id;
        let data = await safetyCardService.update(cardExist._id, reqData);

        if (data) {
          await safetyCardService.prepareCardLogs(data, 'update', req.userData._id, cardExist._id);
        }
        subId.push({ id: cardExist._id, status: 'success' });
      } else {
        reqData.createdBy = req.userData.id;
        reqData.updatedBy = req.userData.id;
        reqData = await safetyCardService.prepareCardLogs(reqData, 'create', req.userData._id);
        const createdCard = await safetyCardService.create(reqData);

        subId.push({ id: createdCard._id, status: 'success' });
      }
    } catch (error) {
      // eslint-disable-next-line no-undef
      if (!cardExist) {
        subId.push({
          id: error.keyValue?._id || error.errors?._id?.value || error?.value,
          status: 'failed',
          comment: error.message,
        });
      }
      continue;
    }
  }
  await jobService.updateJob(jobData._id, { status: 'success', subId });
  // eslint-disable-next-line no-case-declarations
  const response = await jobService.getJobById(jobData._id);
  const modifiedResponse = response.subId.map(card => {
    // eslint-disable-next-line no-unused-vars
    const { _id, ...rest } = card.toObject();
    return rest;
  });

  return modifiedResponse;
};

/**
 * Toolbox Talk Sync
 *
 * @param {*} data
 * @param {*} req
 * @param {*} subId
 * @param {*} jobData
 * @returns
 */
exports.toolboxTalkSync = async (data, req, subId, jobData) => {
  for (let toolboxTalkData of data) {
    try {
      toolboxTalkData.account = req.userData.account;
      if (toolboxTalkData.isDeleted) {
        toolboxTalkData.deletedBy = req.userData.id;
      }
      let filter = {
        _id: commonUtils.toObjectId(toolboxTalkData._id),
        account: req.userData.account,
      };
      const toolboxtalk = await toolboxTalkService.getToolboxTalkById(filter);

      if (toolboxtalk) {
        toolboxTalkData.updatedBy = req.userData.id;
        await toolboxTalkService.updateToolboxTalk(toolboxTalkData._id, toolboxTalkData);
        subId.push({ id: toolboxTalkData._id, status: 'success' });
      } else {
        toolboxTalkData.createdBy = req.userData.id;
        const toolboxTalk = await toolboxTalkService.createToolboxTalk(toolboxTalkData);
        subId.push({ id: toolboxTalk._id, status: 'success' });
      }
    } catch (error) {
      subId.push({
        id: error.keyValue?._id || error.errors?._id?.value || error?.value,
        status: 'failed',
        comment: error.message,
      });
    }
  }
  await jobService.updateJob(jobData._id, { status: 'success', subId });
  // eslint-disable-next-line no-case-declarations
  const response = await jobService.getJobById(jobData._id);
  const modifiedResponse = response.subId.map(toolboxTalkData => {
    // eslint-disable-next-line no-unused-vars

    const rest = toolboxTalkData.toObject();
    rest._id = undefined;
    return rest;
  });

  return modifiedResponse;
};

/**
 * Check Pending Safety Notifications
 *
 * @param {*} userId
 * @param {*} accountId
 * @returns {boolean}
 */
exports.checkPendingSafetyNotifications = async (userId, accountId) => {
  const pendingDocs = await projectDocumentService.getNonAcknowledgedDocuments({
    account: accountId,
    userId: userId,
  });
  return pendingDocs && pendingDocs.length > 0;
};
