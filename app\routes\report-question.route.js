// library
const express = require('express');
const routes = express.Router();

// Validator
const validator = require('../validators/report-question.validator');

// middleware
const { verifyToken, authAccount } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const reportQuestionController = require('../controllers/report-question.controller');
routes.patch(
  '/db-migration',
  verifyToken,
  authAccount,
  validate,
  reportQuestionController.updateReportQuestionDb
);

routes.patch(
  '/db-question-answer-migration',
  verifyToken,
  authAccount,
  validate,
  reportQuestionController.updateReportQuestionAnswerDb
);

// Create Setup report question
routes.post(
  '',
  verifyToken,
  authAccount,
  validator.reportQuestionValidationRule(),
  validate,
  reportQuestionController.createReportQuestion
);

routes.get(
  '/parameter-types',
  verifyToken,
  authAccount,
  validate,
  reportQuestionController.getParameterTypes
);

routes.get(
  '/:reportId',
  verifyToken,
  authAccount,
  validator.validateParamIds(),
  validate,
  reportQuestionController.getReportQuestionAnswers
);

routes.put(
  '/:questionId',
  verifyToken,
  authAccount,
  validator.validateParamIds(),
  validate,
  reportQuestionController.updateReportQuestionAndAnswer
);

routes.delete(
  '/:questionId',
  verifyToken,
  authAccount,
  validator.validateParamIds(),
  validate,
  reportQuestionController.removeReportQuestion
);

module.exports = routes;
