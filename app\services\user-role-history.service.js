const UserRoleHistory = require('../models/user-role-history.model');

exports.CURRENT = 'current';
exports.PREVIOUS = 'previous';

/**
 * Create User Role History
 *
 * @param {*} requestData
 * @returns
 */
exports.createUserRoleHistory = async requestData => {
  return UserRoleHistory.create(requestData);
};

/**
 * Update user role history
 *
 * @param {*} id
 * @param {*} requestData
 * @returns
 */
exports.updateUserRoleHistory = async (id, requestData) => {
  return UserRoleHistory.findByIdAndUpdate(id, { $set: requestData }, { new: true });
};

/**
 * Get Single User Role History
 *
 * @param {*} filter
 * @returns
 */
exports.getSingleUserRoleHistory = async filter => {
  return UserRoleHistory.findOne(filter, { createdAt: 0, updatedAt: 0, __v: 0 });
};

/**
 * Get All User Role History
 *
 * @param {*} filter
 * @returns
 */
exports.getAllUserRoleHistory = async filter => {
  return UserRoleHistory.find(filter, { __v: 0 });
};
