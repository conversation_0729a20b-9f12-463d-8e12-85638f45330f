const axios = require('axios');

/* models */
const Report = require('../models/report.model');

/**
 * Create User
 *
 * @param {*} shift
 * @returns
 */
exports.createReport = async requestData => Report.create(requestData);

/**
 * Filter report
 *
 * @param {*} filter
 * @param {*} page
 * @param {*} perPage
 * @param {*} sort
 * @param {*} deletedBy
 * @returns
 */
exports.filterReport = async (
  filter,
  page = '',
  perPage = '',
  sort = global.constant.DESC_VAL,
  deletedBy = null
) => {
  filter.deletedBy = deletedBy;
  let aggregateFilter = [
    {
      $match: filter,
    },
    {
      $sort: { createdAt: sort },
    },
    {
      $lookup: {
        from: 'projects',
        localField: 'project',
        foreignField: '_id',
        pipeline: [{ $project: { title: 1, _id: 1, projectNumber: 1 } }],
        as: 'project',
      },
    },
    {
      $lookup: {
        from: 'report-types',
        localField: 'reportType',
        foreignField: '_id',
        pipeline: [{ $project: { _id: 1, terminationTypeName: 1 } }],
        as: 'reportType',
      },
    },
    {
      $lookup: {
        from: 'locations',
        localField: 'location',
        foreignField: '_id',
        pipeline: [{ $project: { title: 1, _id: 1 } }],
        as: 'location',
      },
    },
    {
      $lookup: {
        from: 'assets',
        localField: 'cable',
        foreignField: '_id',
        pipeline: [{ $project: { cableName: 1, _id: 1 } }],
        as: 'cable',
      },
    },
    {
      $lookup: {
        from: 'accounts',
        localField: 'account',
        foreignField: '_id',
        pipeline: [{ $project: { name: 1, _id: 1 } }],
        as: 'account',
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'createdBy',
        foreignField: '_id',
        pipeline: [
          { $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1, role: 1 } },
          {
            $lookup: {
              from: 'roles',
              localField: 'role',
              foreignField: '_id',
              pipeline: [
                {
                  $project: {
                    _id: 1,
                    title: 1,
                  },
                },
              ],
              as: 'role',
            },
          },
        ],
        as: 'createdBy',
      },
    },
    {
      $unwind: { path: '$project', preserveNullAndEmptyArrays: true },
    },
    {
      $unwind: { path: '$reportType', preserveNullAndEmptyArrays: true },
    },
    {
      $unwind: { path: '$location', preserveNullAndEmptyArrays: true },
    },
    {
      $unwind: { path: '$cable', preserveNullAndEmptyArrays: true },
    },
    {
      $unwind: { path: '$account', preserveNullAndEmptyArrays: true },
    },
    {
      $unwind: { path: '$createdBy', preserveNullAndEmptyArrays: true },
    },
    {
      $addFields: {
        project: { $ifNull: ['$project', {}] },
        reportType: { $ifNull: ['$reportType', {}] },
        location: { $ifNull: ['$location', {}] },
        cable: { $ifNull: ['$cable', {}] },
        account: { $ifNull: ['$account', {}] },
        createdBy: { $ifNull: ['$createdBy', {}] },
      },
    },
    {
      $unset: ['updatedBy', 'deletedBy', 'updatedAt', 'deletedAt', 'isDeleted', '__v'],
    },
  ];

  if (page == '' && perPage == '') {
    return Report.aggregate(aggregateFilter);
  }

  aggregateFilter.push(
    {
      $skip: parseInt(page) * parseInt(perPage),
    },
    {
      $limit: parseInt(perPage),
    }
  );

  return Report.aggregate(aggregateFilter);
};

/**
 * Get report by id
 *
 * @param {*} id
 * @returns
 */
exports.getReportById = async id => Report.findOne({ _id: id, deletedBy: null });

/**
 * Update report status
 *
 * @param {*} id
 * @param {*} status
 * @returns
 */
exports.changeReportStatus = async (id, status) => {
  return Report.findByIdAndUpdate(id, { status }, { new: true });
};

/**
 * Update report details - param
 *
 * @param {*} reportId
 * @param {*} detailId
 * @param {*} reqData
 * @returns
 */
exports.updateReportParams = async (reportId, detailId, reqData) => {
  return Report.updateOne({ _id: reportId, 'details._id': detailId }, { $set: reqData });
};

/**
 * Get report detail - param by id
 *
 * @param {*} reportId
 * @param {*} detailId
 * @returns
 */
exports.getReportParam = async (reportId, detailId) => {
  return Report.findById(reportId, { details: { $elemMatch: { _id: detailId } } });
};

/**
 * Update report
 *
 * @param {*} id
 * @param {*} requestData
 * @returns
 */
exports.updateReport = async (id, requestData) => {
  return Report.findByIdAndUpdate(id, { $set: requestData }, { new: true });
};

/**
 * Remove report detail - param
 *
 * @param {*} id
 * @param {*} detailId
 * @returns
 */
exports.removeReportParamDetail = async (id, detailId) => {
  return Report.updateOne({ _id: id }, { $pull: { details: { _id: detailId } } });
};

/**
 * Delete Report
 *
 * @param {*} id
 * @param {*} requestData
 * @returns
 */
exports.deleteReport = async (id, requestData) => {
  return Report.findByIdAndUpdate(id, { $set: requestData }, { new: true });
};

/**
 * Delete All Project's Report Type
 *
 * @param {*} id
 * @param {*} deletedAt
 * @returns
 */
exports.deleteAllReport = async (projectId, deletedAt) => {
  return Report.updateMany(
    { project: projectId },
    {
      $set: deletedAt,
    }
  );
};

exports.createDataForReportPDF = async requestData => {
  let tableData = {};
  let headerColor = global.constant.PDF_HEADER_COLOR;
  let headerOpacity = 1;
  let counter = 3;

  let table1 = {
    headers: [
      { label: 'Project', property: 'project', headerColor, headerOpacity },
      { label: 'Location', property: 'location', headerColor, headerOpacity },
    ],
    datas: [{ project: requestData.project.title, location: requestData.location.title }],
  };

  let table2 = {
    headers: [{ label: 'Cable ID', property: 'cable', headerColor, headerOpacity }],
    datas: [{ cable: requestData.cable.cableName }],
  };

  tableData.table1 = table1;
  tableData.table2 = table2;

  for (let element of requestData.details) {
    tableData[`table${counter}`] = {
      headers: [{ label: element.name, property: 'name', headerColor, headerOpacity }],
    };

    if (['image', 'signature'].includes(element.type)) {
      if (!element.hasThreePhase && element.inputValue !== '') {
        let imgLogo = await axios.get(element.inputValue, {
          responseType: 'arraybuffer',
        });
        tableData[`table${counter}`].datas = [
          { name: imgLogo.data, hasThreePhase: element.hasThreePhase, hasImage: true },
        ];
      } else if (
        element.hasThreePhase &&
        element.l1Value !== '' &&
        element.l2alue !== '' &&
        element.l3Value !== ''
      ) {
        let imgL1 = await axios.get(element.l1Value, {
          responseType: 'arraybuffer',
        });
        let imgL2 = await axios.get(element.l2Value, {
          responseType: 'arraybuffer',
        });
        let imgL3 = await axios.get(element.l3Value, {
          responseType: 'arraybuffer',
        });
        tableData[`table${counter}`].datas = [
          {
            name: {
              l1: imgL1.data,
              l2: imgL2.data,
              l3: imgL3.data,
            },
            hasThreePhase: element.hasThreePhase,
            hasImage: true,
          },
        ];
      }
    } else if (!element.hasThreePhase) {
      tableData[`table${counter}`].datas = [
        { name: element.inputValue, hasThreePhase: element.hasThreePhase, hasImage: false },
      ];
    } else {
      tableData[`table${counter}`].datas = [
        {
          name: {
            l1: element.l1Value,
            l2: element.l2Value,
            l3: element.l3Value,
          },
          hasThreePhase: element.hasThreePhase,
          hasImage: false,
        },
      ];
    }

    counter++;
  }

  return tableData;
};

/**
 * Push new report params object
 *
 * @param {*} id
 * @param {*} requestedData
 * @returns
 */
exports.addReportParamDetail = async (id, requestedData) => {
  return Report.updateOne({ _id: id }, { $push: { details: requestedData } });
};

/**
 * Get all reports by report id
 * @param {*} filter
 * @returns
 */
exports.getAllReportsByReportId = async filter => {
  return Report.find(filter);
};
