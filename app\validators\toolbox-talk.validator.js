const { body, validateParamIds, constantUtils } = require('./parent.validator');

const createToolboxTalkValidationRule = () => {
  return [
    body('project').notEmpty().withMessage(constantUtils.PROJECT_REQUIRED),
    body('location').notEmpty().withMessage(constantUtils.LOCATION_REQUIRED),
    body('team').notEmpty().withMessage(constantUtils.TEAM_REQUIRED),
    body('note').notEmpty().withMessage(constantUtils.NOTE_REQUIRED),
    body('hostedBy').notEmpty().withMessage(constantUtils.HOST_REQUIRED),
    body('createdBySignature').notEmpty().withMessage(constantUtils.CREATED_BY_SIGNATURE_REQUIRED),
  ];
};

module.exports = {
  createToolboxTalkValidationRule,
  validateParamIds,
};
