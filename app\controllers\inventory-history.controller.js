// Services
const inventoryHistoryService = require('../services/inventory-history.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const { toObjectId } = require('../utils/common.utils');

/**
 * Get inventory history
 *
 * @param {*} req
 * @param {*} res
 */
exports.getInventoryHistory = async (req, res) => {
  try {
    const filter = {
      account: req.userData.account,
      equipment: toObjectId(req.params.equipment),
      deletedAt: null,
    };

    let page = parseInt(req.query.page) || 0;
    let perPage = parseInt(req.query.perPage) || 10;

    const inventoryList = await inventoryHistoryService.getInventoryHistoryByFilter(
      filter,
      page,
      perPage
    );

    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.GET_INVENTORY_HISTORY, inventoryList));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};
