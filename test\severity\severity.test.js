const request = require('supertest');
const app = require('../../app/server');

// create severities
describe('POST /api/severities', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.6O0ROXpENOlmf3PIz45n_UBxGoCb0DMSKVKKCMPLP6w';
  const severitiesData = {
    title: 'Negligible1',
  };
  it('returns 200 and message Severity has been created successfully', async () => {
    const response = await request(app)
      .post('/api/severities')
      .set('Authorization', `Bearer ${token}`)
      .send(severitiesData);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Severity has been created successfully',
      status: true,
    });
  });

  it('returns 422 with message Required field cannot be empty.', async () => {
    const response = await request(app)
      .post('/api/severities')
      .set('Authorization', `Bearer ${token}`)
      .send(severitiesData);
    expect(response.status).toBe(422);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Required field cannot be empty.',
      status: false,
    });
  });

  it('returns 400 with message Severity already exist', async () => {
    const response = await request(app)
      .post('/api/severities')
      .set('Authorization', `Bearer ${token}`)
      .send(severitiesData);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Severity already exist',
      status: false,
    });
  });
  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).post('/api/severities').send(severitiesData);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});

// getAll severities
describe('GET /api/severities', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.6O0ROXpENOlmf3PIz45n_UBxGoCb0DMSKVKKCMPLP6w';

  it('returns 200 and message Severity list was retireved successfully', async () => {
    const response = await request(app)
      .get('/api/severities')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: [{}],
      message: 'Severity list was retireved successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).get('/api/severities');
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});

//  update severities
describe('PATCH /api/severities/:id', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.6O0ROXpENOlmf3PIz45n_UBxGoCb0DMSKVKKCMPLP6w';
  const id = '64144236ccde2f54f075cce1';
  const updateData = {
    title: 'Negligible2',
  };
  it('returns 200 and message update severities', async () => {
    const response = await request(app)
      .patch(`/api/severities/${id}`)
      .set('Authorization', `Bearer ${token}`)
      .send(updateData);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Severity has been updated successfully',
      status: true,
    });
  });

  it('returns 422 with message Required field cannot be empty.', async () => {
    const response = await request(app)
      .post(`/api/severities/${id}`)
      .set('Authorization', `Bearer ${token}`)
      .send();
    expect(response.status).toBe(422);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Required field cannot be empty.',
      status: false,
    });
  });

  it('returns 404 with message severities does not exist', async () => {
    const response = await request(app)
      .post('/api/severities/6412a388268e9b25cef57a30')
      .set('Authorization', `Bearer ${token}`)
      .send(updateData);
    expect(response.status).toBe(404);
    expect(response.body).toMatchObject({
      message: 'Severity does not exist',
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).patch(`/api/severities/${id}`).send(updateData);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});

//  Get severitiesById
describe('GET /api/severities/:id', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.6O0ROXpENOlmf3PIz45n_UBxGoCb0DMSKVKKCMPLP6w';
  const id = '64144236ccde2f54f075cce1';

  it('returns 200 and message Severity fetched successfully', async () => {
    const response = await request(app)
      .get(`/api/severities/${id}`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Severity fetched successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).get(`/api/severities/${id}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });

  it('returns 404 with message Severity does not exist', async () => {
    const response = await request(app)
      .get('/api/severities/64119ddc6c1d88fde480fd40')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(404);
    expect(response.body).toEqual({
      message: 'Severity does not exist',
      status: false,
    });
  });
});

//  Delete severitiesById
describe('DELETE /api/severities/:id', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.6O0ROXpENOlmf3PIz45n_UBxGoCb0DMSKVKKCMPLP6w';
  const id = '641447923bebb097a1361c5f';

  it('returns 200 and message Severity has been deleted successfully', async () => {
    const response = await request(app)
      .delete(`/api/severities/${id}`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Severity has been deleted successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).delete(`/api/severities/${id}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });

  it('returns 400 with message Severity does not exist', async () => {
    const response = await request(app)
      .delete('/api/severities/6412b7a6644aa8e810eb73fb')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Severity does not exist',
      status: false,
    });
  });
});
