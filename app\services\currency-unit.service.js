const CurrencyUnit = require('../models/currency-unit.model');

/**
 * Create CurrencyUnit
 *
 * @param {*} requestData
 * @returns
 */
exports.createCurrencyUnit = async requestData => {
  return await CurrencyUnit.create(requestData);
};

/**
 * Filter CurrencyUnits
 *
 * @param {*} filter
 * @param {*} perPage
 * @param {*} page
 * @param {*} sort
 * @returns
 */
exports.getCurrencyUnit = async (filter, page, perPage, sort) => {
  return await CurrencyUnit.find(filter, {
    deletedAt: 0,
    deletedBy: 0,
    __v: 0,
  })
    .sort({ createdAt: sort ?? -1 })
    .limit(perPage)
    .skip(page * perPage)
    .populate([
      {
        path: 'account',
        select: { _id: 1, name: 1 },
        strictPopulate: false,
      },
      {
        path: 'createdBy',
        model: 'user',
        select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      },
      {
        path: 'updatedBy',
        model: 'user',
        select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      },
      {
        path: 'deletedBy',
        model: 'user',
        select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      },
    ]);
};

/**
 * Get CurrencyUnit by Id
 *
 * @param {*} id
 * @returns
 */
exports.getCurrencyUnitById = async id => {
  return await CurrencyUnit.findOne({ _id: id, deletedAt: null, isActive: true }).populate([
    {
      path: 'account',
      select: { _id: 1, name: 1 },
      strictPopulate: false,
    },
    {
      path: 'createdBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
    {
      path: 'updatedBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
    {
      path: 'deletedBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
  ]);
};

/**
 * Update CurrencyUnit
 *
 * @param {*} id
 * @param {*} requestData
 * @returns
 */
exports.updateCurrencyUnit = async (id, requestData) => {
  return await CurrencyUnit.findByIdAndUpdate(id, { $set: requestData }, { new: true });
};

/**
 * Delete CurrencyUnit
 *
 * @param {*} id
 * @param {*} deletedAt
 * @returns
 */
exports.deleteCurrencyUnit = async (id, deletedAt) => {
  return await CurrencyUnit.findByIdAndUpdate(id, { $set: deletedAt }, { new: true });
};
