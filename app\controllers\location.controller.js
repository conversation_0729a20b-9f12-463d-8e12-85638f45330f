// Responses
const { successResponse, errorResponse } = require('../utils/response.utils');

// constants
const constants = require('../utils/constants.utils');

// Services
const locationService = require('../services/location.service');
const assetService = require('../services/asset.service');
const userReportService = require('../services/user-report.service');
const pdfTemplateService = require('../services/pdf-template.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const commonUtils = require('../utils/common.utils');
const commonFunction = require('../utils/common-function.utils');

/**
 * Create New Location
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.createLocation = async (req, res) => {
  try {
    const { title, project, longitude, latitude } = req.body;
    let reqData = {};

    const newReq = {
      ...reqData,
      title,
      ...(longitude && { longitude: Number(longitude) }),
      ...(latitude && { latitude: Number(latitude) }),
      account: req.userData.account,
      project: req.body.project,
    };
    const filterData = {
      title,
      project,
      account: req.userData.account.toString(),
    };

    const location = await locationService.getLocationByName(filterData);
    if (location != null) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.LOCATION_EXIST));
    }
    const createdLocation = await locationService.createLocation(newReq);

    await this.commonUpdateSyncApiManage(req.userData.account);

    return res.status(200).json(successResponse(constantUtils.CREATE_LOCATION, createdLocation));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Get All Locations
 *
 * @param {*} req
 * @param {*} res
 */
exports.getAllLocations = async (req, res) => {
  try {
    let project = req.query.project;

    const locationsList = await locationService.getAllLocation(req.userData.account, project);

    res.status(200).json(successResponse(constants.LIST_LOCATION, locationsList));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Update Location
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateLocation = async (req, res) => {
  try {
    let id = req.params.id;
    const exist = await locationService.getLocation(id);

    if (!exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_LOCATION));
    }
    if (req.body.title) {
      const filterData = {
        _id: { $ne: id },
        title: req.body.title,
        project: req.body.project,
        account: req.userData.account.toString(),
      };

      const location = await locationService.getLocationByName(filterData);
      if (location != null) {
        return res.status(400).json(responseUtils.errorResponse(constantUtils.LOCATION_EXIST));
      }
    }

    const response = await locationService.updateLocation(id, req.body);
    // update sync api manage data

    await this.commonUpdateSyncApiManage(req.userData.account);

    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.UPDATE_LOCATION, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Delete Location
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.deleteLocation = async (req, res) => {
  try {
    let id = req.params.id;
    const exist = await locationService.getLocation(id);

    if (!exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_LOCATION));
    }
    const response = await locationService.deleteLocation(id, req.deletedAt);
    // update sync api manage data

    await this.commonUpdateSyncApiManage(req.userData.account);

    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.DELETE_LOCATION, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

exports.getAllData = async (req, res) => {
  try {
    let { account } = req.userData;

    let filterData = { account };

    // check assign projects
    if (
      ![global.constant.ADMIN_ROLE, global.constant.SUPER_ADMIN_ROLE].includes(
        req.userData.role.title
      )
    ) {
      let searchData = {
        ...(req.userData.role.isAssignAllProjects ? {} : { user: req.userData._id }),
        account: req.userData.account,
        deletedAt: null,
      };

      const projectList = await commonUtils.getAssignedProjectList(
        req.userData.role.isAssignAllProjects,
        searchData
      );

      if (projectList.length > 0) {
        filterData.project = { $in: projectList };
      }
    }

    const locationsList = await locationService.getAllData(filterData);

    res.status(200).json(successResponse(constants.LIST_LOCATION, locationsList));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Get Location progress by location id
 *
 * @param {*} req
 * @param {*} res
 */
exports.getLocationProgress = async (req, res) => {
  try {
    const locationId = req.params.id;
    const account = req.userData.account;

    if (!commonUtils.isValidId(locationId)) {
      return res.status(400).json(errorResponse(constantUtils.INVALID_LOCATION_ID));
    }

    const locationProgress = await this.locationProgress(locationId, account);

    res.status(200).json(successResponse(constants.LOCATION_PROGRESS, locationProgress));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Get Location progress pdf
 *
 * @param {*} req
 * @param {*} res
 */

exports.getLocationProgressPDF = async (req, res) => {
  try {
    const locationId = req.params.id;
    const account = req.userData.account;

    if (!commonUtils.isValidId(locationId)) {
      return res.status(400).json(errorResponse(constantUtils.INVALID_LOCATION_ID));
    }

    const locationProgress = await this.locationProgress(locationId, account);

    return await pdfTemplateService.exportLocationProgressPDF(locationProgress, res);
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Get Location progress By locationId
 *
 * @param {*} locationId
 * @param {*} account
 */
exports.locationProgress = async (locationId, account) => {
  // Get location by id
  const [location] = await locationService.getLocationsReport(locationId);

  // Get reports for location type reports
  const reportIds = location.reports.map(rt => rt._id);
  const locationTypeFilter = {
    location: commonUtils.toObjectId(locationId),
    asset: [],
    report: { $in: reportIds },
    account,
    deletedAt: null,
  };
  const locationTypeReports = await userReportService.getReportsForCompletion(
    locationTypeFilter,
    null,
    null,
    -1
  );

  let totalReportsDurations = 0;
  let totalReportCompletedDurations = 0;

  const createProgressFormat = (reportData, rt, location, asset) => {
    const temp = {
      report: reportData?.report || { _id: rt._id, title: rt.title, type: rt.type },
      location: reportData?.location || { _id: location._id, title: location.title },
      asset:
        reportData?.asset?.[0] ||
        (asset
          ? {
              _id: asset._id,
              title: asset.cableName,
              fromLocation: asset.fromLocation,
              toLocation: asset.toLocation,
            }
          : null),
      scope: reportData?.scopes || rt.scopes[0] || rt.scopes,
      status: reportData?.status || null,
      completionDate: reportData?.updatedAt || null,
      totalDuration: reportData?.totalDuration || rt.totalDuration,
      completedDuration: reportData?.totalAnsweredDuration || 0,
      userProjectReport: reportData?.userProjectReport || null,
    };

    totalReportsDurations += +temp.totalDuration;
    totalReportCompletedDurations += temp.completedDuration;
    return temp;
  };

  // check location type report
  let locationTypeProgress = [];
  location.reports.map(rt => {
    if (rt.type === 'location') {
      const reportData = locationTypeReports.find(
        val => val.report._id.toString() === rt._id.toString()
      );
      const locPogressFormat = createProgressFormat(reportData, rt, location);
      locationTypeProgress.push(locPogressFormat);
    }
  });

  // Get all assets by location
  const assets = await assetService.getAssetByLocation(locationId);

  // asset per location
  const assetPerLocationReportsPromises = assets.map(async asset => {
    const assetReportFilter = {
      location: commonUtils.toObjectId(locationId),
      'asset.asset': commonUtils.toObjectId(asset._id),
      report: { $in: asset.reports.map(rt => rt._id) },
      account,
      deletedAt: null,
    };
    return userReportService.getReportsForCompletion(assetReportFilter, null, null, -1);
  });

  // eslint-disable-next-line no-undef
  const assetsPerLocationReports = await Promise.all(assetPerLocationReportsPromises);

  let AssetPerLocationSetupReports = [];
  assets.forEach((asset, index) => {
    const assetReports = assetsPerLocationReports[index];

    asset.reports.forEach(report => {
      const reportIdStr = report._id.toString();

      // Asset per location report
      if (report.type === 'asset_per_location') {
        // Check if location is required for this report
        const isLocationRequired = location.reports.some(rpt => rpt._id.toString() === reportIdStr);
        if (!isLocationRequired) return;

        const reportData = assetReports.find(val => val.report._id.toString() === reportIdStr);
        const tempProgressFormat = createProgressFormat(reportData, report, location, asset);

        AssetPerLocationSetupReports.push(tempProgressFormat);
      }
    });
  });

  // Multiple asset report
  let multipleAssetLocationReports = [];
  const mulitpleAssetReportsPromises = assets.map(async asset => {
    const assetReportFilter = {
      'asset.asset': commonUtils.toObjectId(asset._id),
      report: { $in: asset.reports.map(rt => rt._id) },
      account,
      deletedAt: null,
    };
    return userReportService.getReportsForCompletion(assetReportFilter, null, null, -1);
  });

  // eslint-disable-next-line no-undef
  const mulitpleAssetsReports = await Promise.all(mulitpleAssetReportsPromises);
  assets.forEach((asset, index) => {
    const assetReports = mulitpleAssetsReports[index];

    asset.reports.forEach(report => {
      const reportIdStr = report._id.toString();
      // Asset per location report
      if (report.type === 'multiple_assets') {
        const reportData = assetReports.find(val => val.report._id.toString() === reportIdStr);
        const tempProgressFormat = createProgressFormat(reportData, report, location, asset);
        multipleAssetLocationReports.push(tempProgressFormat);
      }
    });
  });

  const locationProgress = {
    reports: [
      {
        reportData: locationTypeProgress,
        reportType: 'Location based report',
      },
      {
        reportData: AssetPerLocationSetupReports,
        reportType: 'Asset per location setup report',
      },
      {
        reportData: multipleAssetLocationReports,
        reportType: 'Multiple asset location setup report',
      },
    ],
    totalCompletion: (totalReportCompletedDurations / totalReportsDurations) * 100,
    locationTitle: location.title,
  };
  return locationProgress;
};

exports.commonUpdateSyncApiManage = async account => {
  await commonFunction.updateSyncApiManage({
    syncApis: ['toolboxConfig', 'reportConfig'],
    account,
  });
};
