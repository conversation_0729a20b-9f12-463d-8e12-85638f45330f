// Services
const certificateTypeService = require('../services/certificate-type.service');
const certificateService = require('../services/certificate.service');
const uploadCertificateService = require('../services/upload-certificate.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const commonFunction = require('../utils/common-function.utils');

/**
 * Create certificate
 *
 * @param {*} req
 * @param {*} res
 */
exports.createCertificateType = async (req, res) => {
  try {
    let { account } = req.userData;
    const requestData = req.body;
    const alreadyExist = await certificateTypeService.getByNameCertificateType(
      requestData.name,
      account
    );
    if (alreadyExist.length > 0) {
      return res
        .status(400)
        .json(responseUtils.successResponse(constantUtils.CERTIFICATE_TYPE_EXIST));
    }
    const response = await certificateTypeService.createCertificateType(requestData);
    if (response) {
      await this.commonUpdateSyncApiManage(account);
    }
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.CREATE_CERTIFICATE_TYPE, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get certificate
 *
 * @param {*} req
 * @param {*} res
 */
exports.getCertificateType = async (req, res) => {
  try {
    let { account } = req.userData;
    let page = req.query.page ? parseInt(req.query.page) : undefined;
    let perPage = req.query.perPage ? parseInt(req.query.perPage) : undefined;
    let sort = req.query.sort && req.query.sort === 'asc' ? 1 : -1;
    let validityDateFilter = req.query.validityDate;
    let filterData = {
      account: account,
      deletedAt: null,
    };
    if (validityDateFilter !== undefined && validityDateFilter !== 'all') {
      if (validityDateFilter === 'true' || validityDateFilter === 'false') {
        filterData.validityDate = validityDateFilter === 'true';
      } else {
        return res
          .status(400)
          .json(responseUtils.errorResponse(constantUtils.ERROR_CERTIFICATE_TYPE));
      }
    }

    const response = await certificateTypeService.getCertificateType(
      filterData,
      page,
      perPage,
      sort
    );
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.CERTIFICATE_TYPE_LIST, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get certificate by Id
 *
 * @param {*} req
 * @param {*} res
 */
exports.getCertificateTypeById = async (req, res) => {
  try {
    let { id } = req.params;
    const response = await certificateTypeService.getCertificateTypeById(id);
    if (!response) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.CERTIFICATE_TYPE_NOT_EXIST));
    }
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.CERTIFICATE_TYPE_LIST, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Update certificate
 *
 * @param {*} req
 * @param {*} res
 */
exports.updateCertificateType = async (req, res) => {
  try {
    let { id } = req.params;
    const requestData = req.body;
    const response = await certificateTypeService.getCertificateTypeById(id);
    if (!response) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.CERTIFICATE_TYPE_NOT_EXIST));
    }

    const alreadyExist = await certificateTypeService.filterSingleCertificateType({
      _id: { $ne: id },
      name: requestData.name,
      account: req.userData.account,
      deletedAt: null,
    });

    if (alreadyExist) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.CERTIFICATE_TYPE_EXIST));
    }

    const responseUpdate = await certificateTypeService.updateCertificateType(id, requestData);
    if (responseUpdate) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.CERTIFICATE_TYPE_UPDATE, responseUpdate));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Delete certificate
 *
 * @param {*} req
 * @param {*} res
 */
exports.deleteCertificateType = async (req, res) => {
  try {
    let { id } = req.params;
    const response = await certificateTypeService.getCertificateTypeById(id);
    if (!response) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.CERTIFICATE_TYPE_NOT_EXIST));
    }
    await certificateService.deleteProjectCertificate(id);

    /** add inactive status if certificate type is deleted */
    await uploadCertificateService.updateCertificateByFilter(
      { certificateType: id },
      { isActive: false }
    );
    /** add inactive status if certificate type is deleted */

    const responseDelete = await certificateTypeService.deleteCertificateType(id, req.deletedAt);
    if (responseDelete) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.REMOVE_DOCUMENT, responseDelete));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

exports.commonUpdateSyncApiManage = async account => {
  await commonFunction.updateSyncApiManage({
    syncApis: ['upoloadCertificate'],
    account,
  });
};
