const EquipmentOrder = require('../models/equipment-order.model');
const ShoppingCart = require('../models/shopping-cart.model');
/**
 * Create Equipment Order Request
 *
 * @param {*} requestData
 * @returns
 */
exports.createEquipmentOrder = async requestData => {
  return await EquipmentOrder.create(requestData);
};

/**
 * Update Equipment Order
 *
 * @param {*} id
 * @param {*} requestData
 * @param {*} session
 * @returns
 */
exports.updateEquipmentOrder = async (id, requestData, session = null) => {
  const options = { new: true }; // Ensures it returns the updated document
  if (session) options.session = session; // Add session if provided

  return await EquipmentOrder.findByIdAndUpdate(id, { $set: requestData }, options);
};

/**
 * Update Equipment Order Increment PM Order Quantity
 *
 * @param {*} id
 * @param {*} requestData
 * @param {*} session
 * @returns
 */
exports.updateEquipmentOrderIncrementPmOrderQuantity = async (id, requestData, session = null) => {
  const options = { new: true }; // Ensures it returns the updated document
  if (session) options.session = session; // Add session if provided

  return await EquipmentOrder.findByIdAndUpdate(id, requestData, options);
};

/**
 * Get Equipment Order By Id
 *
 * @param {*} filter
 * @returns
 */
exports.getEquipmentOrder = async filter => {
  return await EquipmentOrder.findOne(filter).populate([
    {
      path: 'equipmentType',
      model: 'equipment-type',
      select: 'type',
      strictPopulate: false,
    },
    {
      path: 'project',
      model: 'project',
      select: 'title projectNumber',
      strictPopulate: false,
    },
    {
      path: 'account',
      model: 'account',
      select: 'name',
      strictPopulate: false,
    },
    {
      path: 'user',
      model: 'user',
      select: 'callingName firstName lastName',
      strictPopulate: false,
    },
  ]);
};

/**
 * List Equipment Order
 *
 * @param {*} filter
 * @returns
 */
exports.listEquipmentOrder = async (filter, status, search, page, perPage, sort) => {
  const pipeline = [
    {
      $match: filter,
    },
    {
      $lookup: {
        from: 'equipment-types',
        localField: 'equipmentType',
        foreignField: '_id',
        as: 'equipmentTypeDetails',
        pipeline: [
          ...(search
            ? [
                {
                  $match: {
                    type: {
                      $regex: search,
                      $options: 'i',
                    },
                    account: filter.account,
                  },
                },
              ]
            : []),
        ],
      },
    },
    {
      $unwind: '$equipmentTypeDetails',
    },
    {
      $lookup: {
        from: 'equipment-certificate-types',
        localField: 'equipmentTypeDetails.certificateTypes',
        foreignField: '_id',
        as: 'certificateTypeDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              title: 1,
              isValidityDate: 1,
            },
          },
        ],
      },
    },
    {
      $lookup: {
        from: 'equipment-categories',
        localField: 'equipmentTypeDetails.equipmentCategory',
        foreignField: '_id',
        as: 'equipmentCategoryDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              name: 1,
              abbreviation: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$equipmentCategoryDetails',
    },
    {
      $lookup: {
        from: 'equipment-units',
        localField: 'equipmentTypeDetails.equipmentUnit',
        foreignField: '_id',
        as: 'equipmentUnitDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              title: 1,
              abbreviation: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$equipmentUnitDetails',
    },
    {
      $lookup: {
        from: 'currency-units',
        localField: 'equipmentTypeDetails.currencyUnit',
        foreignField: '_id',
        as: 'currencyUnit',
        pipeline: [
          {
            $project: {
              name: 1,
              symbol: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$currencyUnit',
    },
    {
      $lookup: {
        from: 'equipment-quantity-types',
        localField: 'equipmentTypeDetails.quantityType',
        foreignField: '_id',
        as: 'quantityTypeDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              name: 1,
              priceType: 1,
              quantityType: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$quantityTypeDetails',
    },
    {
      $lookup: {
        from: 'hs-codes',
        localField: 'equipmentTypeDetails.hsCode',
        foreignField: '_id',
        as: 'hsCodeDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              name: 1,
              code: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$hsCodeDetails',
    },
    {
      $lookup: {
        from: 'projects',
        localField: 'project',
        foreignField: '_id',
        as: 'projectDetails',
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'user',
        foreignField: '_id',
        as: 'userDetails',
      },
    },
    {
      $unwind: '$userDetails',
    },
    {
      $lookup: {
        from: 'pm-orders',
        localField: 'pmOrderId',
        foreignField: '_id',
        as: 'pmOrderDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              status: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: { path: '$pmOrderDetails', preserveNullAndEmptyArrays: true },
    },
    {
      $lookup: {
        from: 'pm-order-manage-equipments',
        localField: 'pmOrderManageEquipment',
        foreignField: '_id',
        as: 'pmOrderManageDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              pmComments: 1,
              pmRequestedQuantity: 1,
              wmApprovedQuantity: 1,
              pmReceivedQuantity: 1,
              pmDispatchQuantity: 1,
              wmReceivedQuantity: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: { path: '$pmOrderManageDetails', preserveNullAndEmptyArrays: true },
    },
    {
      $match: {
        status,
      },
    },
    {
      $lookup: {
        from: 'equipment',
        localField: 'equipmentTypeDetails._id',
        foreignField: 'equipmentType',
        as: 'associatedEquipment',
        pipeline: [
          {
            $match: {
              equipmentImage: { $exists: true, $ne: [] },
            },
          },
          { $sort: { createdAt: -1 } },
          {
            $addFields: {
              lastEquipmentImage: { $arrayElemAt: ['$equipmentImage', 0] },
            },
          },
        ],
      },
    },
    {
      $lookup: {
        from: 'equipment-certificate-types',
        localField: 'equipmentTypeDetails.certificateTypes',
        foreignField: '_id',
        as: 'certificateTypeDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              title: 1,
              isValidityDate: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: { path: '$projectDetails', preserveNullAndEmptyArrays: true },
    },
    {
      $unwind: { path: '$associatedEquipment', preserveNullAndEmptyArrays: true },
    },
    {
      $lookup: {
        from: 'users',
        let: { engineerCommentUsers: { $ifNull: ['$engineerComment.user', []] } },
        pipeline: [
          { $match: { $expr: { $in: ['$_id', '$$engineerCommentUsers'] } } },
          { $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1 } },
        ],
        as: 'engineerCommentUserDetails',
      },
    },
    {
      $lookup: {
        from: 'users',
        let: { instructionUsers: { $ifNull: ['$instruction.user', []] } },
        pipeline: [
          { $match: { $expr: { $in: ['$_id', '$$instructionUsers'] } } },
          { $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1 } },
        ],
        as: 'instructionUserDetails',
      },
    },
    {
      $lookup: {
        from: 'users',
        let: { pmCommentUsers: { $ifNull: ['$pmOrderManageDetails.pmComments.user', []] } },
        pipeline: [
          { $match: { $expr: { $in: ['$_id', '$$pmCommentUsers'] } } },
          { $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1 } },
        ],
        as: 'pmCommentUserDetails',
      },
    },
    {
      $addFields: {
        engineerComment: {
          $map: {
            input: { $ifNull: ['$engineerComment', []] },
            as: 'comment',
            in: {
              user: {
                _id: '$$comment.user',
                callingName: {
                  $arrayElemAt: [
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$engineerCommentUserDetails',
                            as: 'userDetail',
                            cond: { $eq: ['$$userDetail._id', '$$comment.user'] },
                          },
                        },
                        as: 'detail',
                        in: '$$detail.callingName',
                      },
                    },
                    0,
                  ],
                },
                firstName: {
                  $arrayElemAt: [
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$engineerCommentUserDetails',
                            as: 'userDetail',
                            cond: { $eq: ['$$userDetail._id', '$$comment.user'] },
                          },
                        },
                        as: 'detail',
                        in: '$$detail.firstName',
                      },
                    },
                    0,
                  ],
                },
                lastName: {
                  $arrayElemAt: [
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$engineerCommentUserDetails',
                            as: 'userDetail',
                            cond: { $eq: ['$$userDetail._id', '$$comment.user'] },
                          },
                        },
                        as: 'detail',
                        in: '$$detail.lastName',
                      },
                    },
                    0,
                  ],
                },
              },
              time: '$$comment.time',
              status: '$$comment.status',
              comment: '$$comment.comment',
            },
          },
        },
        instruction: {
          $map: {
            input: { $cond: [{ $eq: ['$instruction', null] }, [], ['$instruction']] },
            as: 'instructionItem',
            in: {
              user: {
                _id: '$$instructionItem.user',
                callingName: {
                  $arrayElemAt: [
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$instructionUserDetails',
                            as: 'userDetail',
                            cond: { $eq: ['$$userDetail._id', '$$instructionItem.user'] },
                          },
                        },
                        as: 'detail',
                        in: '$$detail.callingName',
                      },
                    },
                    0,
                  ],
                },
                firstName: {
                  $arrayElemAt: [
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$instructionUserDetails',
                            as: 'userDetail',
                            cond: { $eq: ['$$userDetail._id', '$$instructionItem.user'] },
                          },
                        },
                        as: 'detail',
                        in: '$$detail.firstName',
                      },
                    },
                    0,
                  ],
                },
                lastName: {
                  $arrayElemAt: [
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$instructionUserDetails',
                            as: 'userDetail',
                            cond: { $eq: ['$$userDetail._id', '$$instructionItem.user'] },
                          },
                        },
                        as: 'detail',
                        in: '$$detail.lastName',
                      },
                    },
                    0,
                  ],
                },
              },
              time: '$$instructionItem.time',
              status: '$$instructionItem.status',
              comment: '$$instructionItem.comment',
            },
          },
        },
        'pmOrderManageDetails.pmComments': {
          $map: {
            input: { $ifNull: ['$pmOrderManageDetails.pmComments', []] },
            as: 'comment',
            in: {
              user: {
                _id: '$$comment.user',
                callingName: {
                  $arrayElemAt: [
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$pmCommentUserDetails',
                            as: 'userDetail',
                            cond: { $eq: ['$$userDetail._id', '$$comment.user'] },
                          },
                        },
                        as: 'detail',
                        in: '$$detail.callingName',
                      },
                    },
                    0,
                  ],
                },
                firstName: {
                  $arrayElemAt: [
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$pmCommentUserDetails',
                            as: 'userDetail',
                            cond: { $eq: ['$$userDetail._id', '$$comment.user'] },
                          },
                        },
                        as: 'detail',
                        in: '$$detail.firstName',
                      },
                    },
                    0,
                  ],
                },
                lastName: {
                  $arrayElemAt: [
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$pmCommentUserDetails',
                            as: 'userDetail',
                            cond: { $eq: ['$$userDetail._id', '$$comment.user'] },
                          },
                        },
                        as: 'detail',
                        in: '$$detail.lastName',
                      },
                    },
                    0,
                  ],
                },
              },
              time: '$$comment.time',
              status: '$$comment.status',
              comment: '$$comment.comment',
            },
          },
        },
      },
    },
    {
      $addFields: {
        'pmOrderManageDetails.comments': {
          pmComments: '$pmOrderManageDetails.pmComments',
          wmComments: [],
        },
      },
    },
    {
      $unset: 'pmOrderManageDetails.pmComments',
    },
    {
      $group: {
        _id: {
          projectId: '$projectDetails._id',
          equipmentTypeId: '$equipmentTypeDetails._id',
        },
        projectName: {
          $first: {
            $concat: ['$projectDetails.projectNumber', ' - ', '$projectDetails.title'],
          },
        },
        pmOrderId: { $first: '$pmOrderId' },
        equipmentTypes: {
          $addToSet: {
            equipmentTypeId: '$equipmentTypeDetails._id',
            type: '$equipmentTypeDetails.type',
            price: '$equipmentTypeDetails.price',
            certificateTypes: '$certificateTypeDetails',
            equipmentCategory: {
              _id: '$equipmentCategoryDetails._id',
              name: '$equipmentCategoryDetails.name',
            },
            equipmentUnit: {
              _id: '$equipmentUnitDetails._id',
              name: '$equipmentUnitDetails.name',
              abbreviation: '$equipmentUnitDetails.abbreviation',
            },
            currencyUnit: {
              _id: '$currencyUnit._id',
              name: '$currencyUnit.name',
              abbreviation: '$currencyUnit.symbol',
            },
            quantityType: {
              _id: '$quantityTypeDetails._id',
              name: '$quantityTypeDetails.name',
              priceType: '$quantityTypeDetails.priceType',
            },
            hsCode: {
              _id: '$hsCodeDetails._id',
              code: '$hsCodeDetails.code',
            },
          },
        },
        equipmentTypeImage: {
          $first: '$associatedEquipment.lastEquipmentImage',
        },
        pmOrderManageEquipment: { $first: '$pmOrderManageEquipment' },
        isTemporary: { $first: '$isTemporary' },
        users: {
          $addToSet: {
            equipmentRequestId: '$_id',
            userId: '$userDetails._id',
            callingName: '$userDetails.callingName',
            firstName: '$userDetails.firstName',
            lastName: '$userDetails.lastName',
            profileImage: '$userDetails.profileImage',
            engineerRequestedQuantity: '$engineerRequestedQuantity',
            pmApprovedQuantity: '$pmApprovedQuantity',
            engineerComment: '$engineerComment',
            totalAmount: '$totalAmount',
            fromDate: '$fromDate',
            toDate: '$toDate',
            rentalDays: {
              $ceil: {
                $divide: [{ $subtract: ['$toDate', '$fromDate'] }, global.constant.DAY_CONVERTOR],
              },
            },
            pricePerEquipment: '$equipmentTypeDetails.price',
            createdAt: '$createdAt',
            temporaryProductName: '$temporaryProductName',
          },
        },
        pmOrderManageDetails: {
          $first: '$pmOrderManageDetails',
        },
        maxcreatedAt: { $max: '$createdAt' },
        createdAt: {
          $first: '$createdAt',
        },
        updatedAt: {
          $first: '$updatedAt',
        },
      },
    },

    { $unwind: '$users' },
    { $sort: { 'users.createdAt': -1 } },
    {
      $group: {
        _id: {
          projectId: '$_id.projectId',
          equipmentTypeId: '$_id.equipmentTypeId',
        },
        projectName: { $first: '$projectName' },
        pmOrderId: { $first: '$pmOrderId' },
        equipmentTypes: { $first: '$equipmentTypes' },
        equipmentTypeImage: { $first: '$equipmentTypeImage' },
        pmOrderManageEquipment: { $first: '$pmOrderManageEquipment' },
        isTemporary: { $first: '$isTemporary' },
        users: { $push: '$users' },
        pmOrderManageDetails: { $first: '$pmOrderManageDetails' },
        maxcreatedAt: { $max: '$maxcreatedAt' },
        createdAt: { $first: '$createdAt' },
        updatedAt: { $first: '$updatedAt' },
      },
    },
    { $unwind: '$equipmentTypes' },

    { $sort: { maxcreatedAt: -1 } },
    {
      $group: {
        _id: '$_id.projectId',
        projectName: { $first: '$projectName' },
        pmOrderId: { $first: '$pmOrderId' },
        equipmentTypes: {
          $push: {
            _id: '$equipmentTypes.equipmentTypeId',
            type: '$equipmentTypes.type',
            price: '$equipmentTypes.price',
            certificateTypes: '$equipmentTypes.certificateTypes',
            equipmentCategory: '$equipmentTypes.equipmentCategory',
            equipmentUnit: '$equipmentTypes.equipmentUnit',
            currencyUnit: '$equipmentTypes.currencyUnit',
            hsCode: '$equipmentTypes.hsCode',
            quantityType: '$equipmentTypes.quantityType',
            equipmentTypeImage: '$equipmentTypeImage',
            pmOrderManageEquipment: { $ifNull: ['$pmOrderManageEquipment', null] },
            pmOrderManageDetails: { $ifNull: ['$pmOrderManageDetails', {}] },
            isTemporary: { $ifNull: ['$isTemporary', false] },
            users: '$users',
            maxCreatedAt: '$equipmentTypes.maxCreatedAt',
            totalEngineerRequestedQuantity: {
              $sum: {
                $ifNull: ['$users.engineerRequestedQuantity', 0],
              },
            },
          },
        },
        totalEquipmentType: { $sum: 1 },
        totalQuantity: {
          $sum: {
            $ifNull: ['$users.totalEngineerRequestedQuantity', 0],
          },
        },
        createdAt: { $first: '$createdAt' },
      },
    },
    {
      $sort: {
        createdAt: sort,
      },
    },
  ];

  if (page === '' && perPage === '') {
    return await EquipmentOrder.aggregate(pipeline);
  }

  pipeline.push(
    {
      $skip: parseInt(page) * parseInt(perPage),
    },
    {
      $limit: parseInt(perPage),
    }
  );

  return await EquipmentOrder.aggregate(pipeline);
};

exports.updateEquipmentOrderByFilter = async (filter, data) => {
  return await EquipmentOrder.updateMany(filter, { $set: data }, { new: true });
};

/**
 * Change Order Status
 *
 * @param {*} filter
 * @param {*} data
 * @returns
 */
exports.changeOrderStatus = async (filter, data) => {
  return await EquipmentOrder.updateMany(filter, { $set: data }, { new: true });
};

/**
 * Get All Equipment Orders
 *
 * @param {*} filter
 * @returns
 */
exports.getEquipmentOrders = async filter => {
  return await EquipmentOrder.find(filter);
};

exports.findOneAndUpdateEquipmentType = async (filter, data) => {
  return await EquipmentOrder.findOneAndUpdate(filter, {
    $set: data,
  });
};

/**
 * Get equipment order history
 *
 * @param {*} filter
 * @param {*} search
 * @param {*} sort
 * @param {*} page
 * @param {*} perPage
 * @returns
 */
exports.getEquipmentOrderHistory = async (filter, search, sort, page, perPage) => {
  let pipeline = [
    {
      $match: filter,
    },
  ];
  if (page !== '' && perPage !== '') {
    pipeline.push(
      {
        $skip: parseInt(page) * parseInt(perPage),
      },
      {
        $limit: parseInt(perPage),
      }
    );
  }
  pipeline.push(
    {
      $lookup: {
        from: 'projects',
        localField: 'project',
        foreignField: '_id',
        as: 'projects',
        pipeline: [
          {
            $project: {
              _id: 1,
              title: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$projects',
    },
    {
      $lookup: {
        from: 'equipment-types',
        localField: 'equipmentType',
        foreignField: '_id',
        as: 'equipmentTypeDetails',
        pipeline: [
          ...(search
            ? [
                {
                  $match: {
                    type: {
                      $regex: search,
                      $options: 'i',
                    },
                  },
                },
              ]
            : []),
          {
            $lookup: {
              from: 'equipment',
              localField: '_id',
              foreignField: 'equipmentType',
              as: 'equipment',
              pipeline: [
                {
                  $match: {
                    equipmentImage: { $exists: true, $ne: [] },
                  },
                },
                { $sort: { createdAt: -1 } },
                {
                  $addFields: {
                    image: { $arrayElemAt: ['$equipmentImage', 0] },
                  },
                },
                {
                  $project: {
                    image: 1,
                  },
                },
              ],
            },
          },
          {
            $addFields: {
              equipmentTypeImage: { $arrayElemAt: ['$equipment', 0] },
            },
          },
          {
            $project: {
              _id: 1,
              type: 1,
              equipmentTypeImage: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$equipmentTypeDetails',
    },
    {
      $set: {
        receivedAt: {
          $cond: {
            if: { $eq: ['$status', 'requested'] },
            then: '$updatedAt',
            else: '',
          },
        },
      },
    },
    {
      $sort: sort,
    },
    {
      $project: {
        engineerRequestedQuantity: 1,
        pmApprovedQuantity: 1,
        status: 1,
        createdAt: 1,
        equipmentTypeDetails: 1,
        projects: 1,
        receivedAt: 1,
        isTemporary: { $ifNull: ['$isTemporary', false] },
        temporaryProductName: { $ifNull: ['$temporaryProductName', ''] },
      },
    }
  );
  let coll = { collation: { locale: 'en' } };
  return await EquipmentOrder.aggregate(pipeline, coll);
};

/**
 * List project orders
 *
 * @param {*} filter
 * @param {*} status
 * @param {*} search
 * @param {*} page
 * @param {*} sort
 * @returns
 */
exports.listEquipmentRequestedOrders = async (filter, status, search, page, perPage, sort) => {
  const pipeline = [
    {
      $match: filter,
    },
    {
      $lookup: {
        from: 'equipment-types',
        localField: 'equipmentType',
        foreignField: '_id',
        as: 'equipmentTypeDetails',
        pipeline: [
          ...(search
            ? [
                {
                  $match: {
                    type: {
                      $regex: search,
                      $options: 'i',
                    },
                    account: filter.account,
                  },
                },
              ]
            : []),
        ],
      },
    },
    {
      $unwind: '$equipmentTypeDetails',
    },

    {
      $lookup: {
        from: 'projects',
        localField: 'project',
        foreignField: '_id',
        as: 'projectDetails',
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'user',
        foreignField: '_id',
        as: 'userDetails',
      },
    },
    {
      $unwind: '$userDetails',
    },
    {
      $match: {
        status,
      },
    },
    {
      $lookup: {
        from: 'equipment',
        localField: 'equipmentTypeDetails._id',
        foreignField: 'equipmentType',
        as: 'associatedEquipment',
        pipeline: [
          {
            $match: {
              equipmentImage: { $exists: true, $ne: [] },
            },
          },
          { $sort: { createdAt: -1 } },
          {
            $addFields: {
              lastEquipmentImage: { $arrayElemAt: ['$equipmentImage', 0] },
            },
          },
        ],
      },
    },
    {
      $unwind: { path: '$projectDetails', preserveNullAndEmptyArrays: true },
    },
    {
      $unwind: { path: '$associatedEquipment', preserveNullAndEmptyArrays: true },
    },
    {
      $group: {
        _id: {
          projectId: '$projectDetails._id',
          equipmentTypeId: '$equipmentTypeDetails._id',
        },
        projectName: {
          $first: {
            $concat: ['$projectDetails.projectNumber', ' - ', '$projectDetails.title'],
          },
        },
        equipmentTypes: {
          $addToSet: {
            equipmentTypeId: '$equipmentTypeDetails._id',
            type: '$equipmentTypeDetails.type',
            price: '$equipmentTypeDetails.price',
          },
        },
        equipmentTypeImage: {
          $first: '$associatedEquipment.lastEquipmentImage',
        },
        users: {
          $addToSet: {
            equipmentRequestId: '$_id',
            userId: '$userDetails._id',
            engineerRequestedQuantity: '$engineerRequestedQuantity',
          },
        },
        createdAt: { $first: '$createdAt' },
        updatedAt: { $first: '$updatedAt' },
      },
    },
    { $unwind: '$users' },
    {
      $group: {
        _id: '$_id',
        projectName: { $first: '$projectName' },
        equipmentTypes: { $first: '$equipmentTypes' },
        equipmentTypeImage: { $first: '$equipmentTypeImage' },
        totalEngineerRequestedQuantity: { $sum: '$users.engineerRequestedQuantity' },
        createdAt: { $first: '$createdAt' },
        updatedAt: { $first: '$updatedAt' },
      },
    },
    { $unwind: '$equipmentTypes' },
    { $sort: { createdAt: -1 } },
    {
      $group: {
        _id: '$_id.projectId',
        projectName: { $first: '$projectName' },
        equipmentTypes: {
          $push: {
            _id: '$equipmentTypes.equipmentTypeId',
            type: '$equipmentTypes.type',
            price: '$equipmentTypes.price',
            totalEngineerRequestedQuantity: '$totalEngineerRequestedQuantity',
            equipmentTypeImage: '$equipmentTypeImage',
          },
        },
        totalEquipmentType: { $sum: 1 },
        totalQuantity: {
          $sum: {
            $ifNull: ['$totalEngineerRequestedQuantity', 0],
          },
        },
        createdAt: { $first: '$createdAt' },
        updatedAt: { $first: '$updatedAt' },
      },
    },
    {
      $sort: {
        createdAt: sort,
      },
    },
  ];

  if (page === '' && perPage === '') {
    return await EquipmentOrder.aggregate(pipeline);
  }
  pipeline.push(
    {
      $skip: parseInt(page) * parseInt(perPage),
    },
    {
      $limit: parseInt(perPage),
    }
  );

  return await EquipmentOrder.aggregate(pipeline);
};

/**
 * get equipment orders by project
 *
 * @param {*} filter
 * @param {*} status
 * @param {*} filter
 * @param {*} perPage
 * @param {*} sort
 * @returns
 */
exports.getEquipmentOrderByProject = async (filter, status, page, perPage, sort) => {
  const pipeline = [
    {
      $match: filter,
    },
    {
      $lookup: {
        from: 'equipment-types',
        localField: 'equipmentType',
        foreignField: '_id',
        as: 'equipmentTypeDetails',
      },
    },
    {
      $unwind: '$equipmentTypeDetails',
    },
    {
      $lookup: {
        from: 'equipment-certificate-types',
        localField: 'equipmentTypeDetails.certificateTypes',
        foreignField: '_id',
        as: 'certificateTypeDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              title: 1,
              isValidityDate: 1,
            },
          },
        ],
      },
    },
    {
      $lookup: {
        from: 'equipment-categories',
        localField: 'equipmentTypeDetails.equipmentCategory',
        foreignField: '_id',
        as: 'equipmentCategoryDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              name: 1,
              abbreviation: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$equipmentCategoryDetails',
    },
    {
      $lookup: {
        from: 'equipment-units',
        localField: 'equipmentTypeDetails.equipmentUnit',
        foreignField: '_id',
        as: 'equipmentUnitDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              title: 1,
              abbreviation: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$equipmentUnitDetails',
    },
    {
      $lookup: {
        from: 'currency-units',
        localField: 'equipmentTypeDetails.currencyUnit',
        foreignField: '_id',
        as: 'currencyUnit',
        pipeline: [
          {
            $project: {
              name: 1,
              symbol: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$currencyUnit',
    },
    {
      $lookup: {
        from: 'equipment-quantity-types',
        localField: 'equipmentTypeDetails.quantityType',
        foreignField: '_id',
        as: 'quantityTypeDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              name: 1,
              priceType: 1,
              quantityType: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$quantityTypeDetails',
    },
    {
      $lookup: {
        from: 'hs-codes',
        localField: 'equipmentTypeDetails.hsCode',
        foreignField: '_id',
        as: 'hsCodeDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              name: 1,
              code: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$hsCodeDetails',
    },
    {
      $lookup: {
        from: 'projects',
        localField: 'project',
        foreignField: '_id',
        as: 'projectDetails',
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'user',
        foreignField: '_id',
        as: 'userDetails',
      },
    },
    {
      $unwind: '$userDetails',
    },
    {
      $lookup: {
        from: 'pm-orders',
        localField: 'pmOrderId',
        foreignField: '_id',
        as: 'pmOrderDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              status: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: { path: '$pmOrderDetails', preserveNullAndEmptyArrays: true },
    },
    {
      $lookup: {
        from: 'pm-order-manage-equipments',
        localField: 'pmOrderManageEquipment',
        foreignField: '_id',
        as: 'pmOrderManageDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              pmComments: 1,
              pmRequestedQuantity: 1,
              wmApprovedQuantity: 1,
              pmReceivedQuantity: 1,
              pmDispatchQuantity: 1,
              wmReceivedQuantity: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: { path: '$pmOrderManageDetails', preserveNullAndEmptyArrays: true },
    },
    {
      $match: {
        status,
      },
    },
    {
      $lookup: {
        from: 'equipment',
        localField: 'equipmentTypeDetails._id',
        foreignField: 'equipmentType',
        as: 'associatedEquipment',
        pipeline: [
          {
            $match: {
              equipmentImage: { $exists: true, $ne: [] },
            },
          },
          { $sort: { createdAt: -1 } },
          {
            $addFields: {
              lastEquipmentImage: { $arrayElemAt: ['$equipmentImage', 0] },
            },
          },
        ],
      },
    },
    {
      $lookup: {
        from: 'equipment-certificate-types',
        localField: 'equipmentTypeDetails.certificateTypes',
        foreignField: '_id',
        as: 'certificateTypeDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              title: 1,
              isValidityDate: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: { path: '$projectDetails', preserveNullAndEmptyArrays: true },
    },
    {
      $unwind: { path: '$associatedEquipment', preserveNullAndEmptyArrays: true },
    },
    {
      $lookup: {
        from: 'users',
        let: { engineerCommentUsers: { $ifNull: ['$engineerComment.user', []] } },
        pipeline: [
          { $match: { $expr: { $in: ['$_id', '$$engineerCommentUsers'] } } },
          { $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1 } },
        ],
        as: 'engineerCommentUserDetails',
      },
    },
    {
      $lookup: {
        from: 'users',
        let: { instructionUsers: { $ifNull: ['$instruction.user', []] } },
        pipeline: [
          { $match: { $expr: { $in: ['$_id', '$$instructionUsers'] } } },
          { $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1 } },
        ],
        as: 'instructionUserDetails',
      },
    },
    {
      $lookup: {
        from: 'users',
        let: { pmCommentUsers: { $ifNull: ['$pmOrderManageDetails.pmComments.user', []] } },
        pipeline: [
          { $match: { $expr: { $in: ['$_id', '$$pmCommentUsers'] } } },
          { $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1 } },
        ],
        as: 'pmCommentUserDetails',
      },
    },
    {
      $addFields: {
        engineerComment: {
          $map: {
            input: { $ifNull: ['$engineerComment', []] },
            as: 'comment',
            in: {
              user: {
                _id: '$$comment.user',
                callingName: {
                  $arrayElemAt: [
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$engineerCommentUserDetails',
                            as: 'userDetail',
                            cond: { $eq: ['$$userDetail._id', '$$comment.user'] },
                          },
                        },
                        as: 'detail',
                        in: '$$detail.callingName',
                      },
                    },
                    0,
                  ],
                },
                firstName: {
                  $arrayElemAt: [
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$engineerCommentUserDetails',
                            as: 'userDetail',
                            cond: { $eq: ['$$userDetail._id', '$$comment.user'] },
                          },
                        },
                        as: 'detail',
                        in: '$$detail.firstName',
                      },
                    },
                    0,
                  ],
                },
                lastName: {
                  $arrayElemAt: [
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$engineerCommentUserDetails',
                            as: 'userDetail',
                            cond: { $eq: ['$$userDetail._id', '$$comment.user'] },
                          },
                        },
                        as: 'detail',
                        in: '$$detail.lastName',
                      },
                    },
                    0,
                  ],
                },
              },
              time: '$$comment.time',
              status: '$$comment.status',
              comment: '$$comment.comment',
            },
          },
        },
        instruction: {
          $map: {
            input: { $cond: [{ $eq: ['$instruction', null] }, [], ['$instruction']] },
            as: 'instructionItem',
            in: {
              user: {
                _id: '$$instructionItem.user',
                callingName: {
                  $arrayElemAt: [
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$instructionUserDetails',
                            as: 'userDetail',
                            cond: { $eq: ['$$userDetail._id', '$$instructionItem.user'] },
                          },
                        },
                        as: 'detail',
                        in: '$$detail.callingName',
                      },
                    },
                    0,
                  ],
                },
                firstName: {
                  $arrayElemAt: [
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$instructionUserDetails',
                            as: 'userDetail',
                            cond: { $eq: ['$$userDetail._id', '$$instructionItem.user'] },
                          },
                        },
                        as: 'detail',
                        in: '$$detail.firstName',
                      },
                    },
                    0,
                  ],
                },
                lastName: {
                  $arrayElemAt: [
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$instructionUserDetails',
                            as: 'userDetail',
                            cond: { $eq: ['$$userDetail._id', '$$instructionItem.user'] },
                          },
                        },
                        as: 'detail',
                        in: '$$detail.lastName',
                      },
                    },
                    0,
                  ],
                },
              },
              time: '$$instructionItem.time',
              status: '$$instructionItem.status',
              comment: '$$instructionItem.comment',
            },
          },
        },
        'pmOrderManageDetails.pmComments': {
          $map: {
            input: { $ifNull: ['$pmOrderManageDetails.pmComments', []] },
            as: 'comment',
            in: {
              user: {
                _id: '$$comment.user',
                callingName: {
                  $arrayElemAt: [
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$pmCommentUserDetails',
                            as: 'userDetail',
                            cond: { $eq: ['$$userDetail._id', '$$comment.user'] },
                          },
                        },
                        as: 'detail',
                        in: '$$detail.callingName',
                      },
                    },
                    0,
                  ],
                },
                firstName: {
                  $arrayElemAt: [
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$pmCommentUserDetails',
                            as: 'userDetail',
                            cond: { $eq: ['$$userDetail._id', '$$comment.user'] },
                          },
                        },
                        as: 'detail',
                        in: '$$detail.firstName',
                      },
                    },
                    0,
                  ],
                },
                lastName: {
                  $arrayElemAt: [
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$pmCommentUserDetails',
                            as: 'userDetail',
                            cond: { $eq: ['$$userDetail._id', '$$comment.user'] },
                          },
                        },
                        as: 'detail',
                        in: '$$detail.lastName',
                      },
                    },
                    0,
                  ],
                },
              },
              time: '$$comment.time',
              status: '$$comment.status',
              comment: '$$comment.comment',
            },
          },
        },
      },
    },
    {
      $addFields: {
        'pmOrderManageDetails.comments': {
          pmComments: '$pmOrderManageDetails.pmComments',
          wmComments: [],
        },
      },
    },
    {
      $unset: 'pmOrderManageDetails.pmComments',
    },
    {
      $group: {
        _id: '$equipmentTypeDetails._id',
        projectId: { $first: '$projectDetails._id' },
        projectName: {
          $first: {
            $concat: ['$projectDetails.projectNumber', ' - ', '$projectDetails.title'],
          },
        },
        pmOrderId: { $first: '$pmOrderId' },
        equipmentTypes: {
          $addToSet: {
            equipmentTypeId: '$equipmentTypeDetails._id',
            type: '$equipmentTypeDetails.type',
            price: '$equipmentTypeDetails.price',
            certificateTypes: '$certificateTypeDetails',
            equipmentCategory: {
              _id: '$equipmentCategoryDetails._id',
              name: '$equipmentCategoryDetails.name',
            },
            equipmentUnit: {
              _id: '$equipmentUnitDetails._id',
              name: '$equipmentUnitDetails.name',
              abbreviation: '$equipmentUnitDetails.abbreviation',
            },
            currencyUnit: {
              _id: '$currencyUnit._id',
              name: '$currencyUnit.name',
              abbreviation: '$currencyUnit.symbol',
            },
            quantityType: {
              _id: '$quantityTypeDetails._id',
              name: '$quantityTypeDetails.name',
              priceType: '$quantityTypeDetails.priceType',
            },
            hsCode: {
              _id: '$hsCodeDetails._id',
              code: '$hsCodeDetails.code',
            },
          },
        },
        equipmentTypeImage: {
          $first: '$associatedEquipment.lastEquipmentImage',
        },
        pmOrderManageEquipment: { $first: '$pmOrderManageEquipment' },
        isTemporary: { $first: '$isTemporary' },
        users: {
          $addToSet: {
            equipmentRequestId: '$_id',
            userId: '$userDetails._id',
            callingName: '$userDetails.callingName',
            firstName: '$userDetails.firstName',
            lastName: '$userDetails.lastName',
            profileImage: '$userDetails.profileImage',
            engineerRequestedQuantity: '$engineerRequestedQuantity',
            pmApprovedQuantity: '$pmApprovedQuantity',
            engineerComment: '$engineerComment',
            totalAmount: '$totalAmount',
            fromDate: '$fromDate',
            toDate: '$toDate',
            rentalDays: {
              $ceil: {
                $divide: [{ $subtract: ['$toDate', '$fromDate'] }, global.constant.DAY_CONVERTOR],
              },
            },
            pricePerEquipment: '$equipmentTypeDetails.price',
            createdAt: '$createdAt',
            temporaryProductName: '$temporaryProductName',
          },
        },
        pmOrderManageDetails: {
          $first: '$pmOrderManageDetails',
        },
        maxcreatedAt: { $max: '$createdAt' },
        createdAt: {
          $first: '$createdAt',
        },
        updatedAt: {
          $first: '$updatedAt',
        },
      },
    },
    { $unwind: '$users' },
    { $sort: { 'users.createdAt': -1 } },
    {
      $group: {
        _id: '$_id',
        projectId: { $first: '$projectId' },
        projectName: { $first: '$projectName' },
        pmOrderId: { $first: '$pmOrderId' },
        equipmentTypes: { $first: '$equipmentTypes' },
        equipmentTypeImage: { $first: '$equipmentTypeImage' },
        pmOrderManageEquipment: { $first: '$pmOrderManageEquipment' },
        users: { $push: '$users' },
        pmOrderManageDetails: { $first: '$pmOrderManageDetails' },
        isTemporary: { $first: '$isTemporary' },
        maxcreatedAt: { $max: '$maxcreatedAt' },
        totalRequestedQuantity: {
          $sum: {
            $ifNull: ['$users.engineerRequestedQuantity', 0],
          },
        },
        createdAt: { $first: '$createdAt' },
        updatedAt: { $first: '$updatedAt' },
      },
    },
    { $unwind: '$equipmentTypes' },

    { $sort: { maxcreatedAt: -1 } },
    {
      $group: {
        _id: '$projectId',
        projectName: { $first: '$projectName' },
        pmOrderId: { $first: '$pmOrderId' },
        equipmentTypes: {
          $push: {
            _id: '$equipmentTypes.equipmentTypeId',
            type: '$equipmentTypes.type',
            price: '$equipmentTypes.price',
            certificateTypes: '$equipmentTypes.certificateTypes',
            equipmentCategory: '$equipmentTypes.equipmentCategory',
            equipmentUnit: '$equipmentTypes.equipmentUnit',
            currencyUnit: '$equipmentTypes.currencyUnit',
            hsCode: '$equipmentTypes.hsCode',
            quantityType: '$equipmentTypes.quantityType',
            equipmentTypeImage: '$equipmentTypeImage',
            pmOrderManageEquipment: { $ifNull: ['$pmOrderManageEquipment', null] },
            pmOrderManageDetails: { $ifNull: ['$pmOrderManageDetails', {}] },
            isTemporary: { $ifNull: ['$isTemporary', false] },
            users: '$users',
            maxCreatedAt: '$equipmentTypes.maxCreatedAt',
            totalEngineerRequestedQuantity: {
              $sum: {
                $ifNull: ['$users.engineerRequestedQuantity', 0],
              },
            },
          },
        },
        totalEquipmentType: { $sum: 1 },
        totalQuantity: {
          $sum: {
            $ifNull: ['$totalRequestedQuantity', 0],
          },
        },
        createdAt: { $first: '$createdAt' },
      },
    },
    {
      $sort: {
        createdAt: sort,
      },
    },
  ];

  if (page === '' && perPage === '') {
    return await EquipmentOrder.aggregate(pipeline);
  }

  pipeline.push(
    {
      $skip: parseInt(page) * parseInt(perPage),
    },
    {
      $limit: parseInt(perPage),
    }
  );

  return await EquipmentOrder.aggregate(pipeline);
};

/**
 * get equipment orders by shopping cart
 *
 * @param {*} filter
 * @param {*} status
 * @param {*} filter
 * @param {*} perPage
 * @param {*} sort
 * @returns
 */
exports.getEquipmentOrderByShoppingCart = async (filter, status, page, perPage, sort) => {
  const pipeline = [
    {
      $match: filter,
    },
    {
      $lookup: {
        from: 'equipment-types',
        localField: 'equipmentType',
        foreignField: '_id',
        as: 'equipmentTypeDetails',
      },
    },
    {
      $unwind: '$equipmentTypeDetails',
    },
    {
      $lookup: {
        from: 'equipment-certificate-types',
        localField: 'equipmentTypeDetails.certificateTypes',
        foreignField: '_id',
        as: 'certificateTypeDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              title: 1,
              isValidityDate: 1,
            },
          },
        ],
      },
    },
    {
      $lookup: {
        from: 'equipment-categories',
        localField: 'equipmentTypeDetails.equipmentCategory',
        foreignField: '_id',
        as: 'equipmentCategoryDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              name: 1,
              abbreviation: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$equipmentCategoryDetails',
    },
    {
      $lookup: {
        from: 'equipment-units',
        localField: 'equipmentTypeDetails.equipmentUnit',
        foreignField: '_id',
        as: 'equipmentUnitDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              title: 1,
              abbreviation: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$equipmentUnitDetails',
    },
    {
      $lookup: {
        from: 'currency-units',
        localField: 'equipmentTypeDetails.currencyUnit',
        foreignField: '_id',
        as: 'currencyUnit',
        pipeline: [
          {
            $project: {
              name: 1,
              symbol: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$currencyUnit',
    },
    {
      $lookup: {
        from: 'equipment-quantity-types',
        localField: 'equipmentTypeDetails.quantityType',
        foreignField: '_id',
        as: 'quantityTypeDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              name: 1,
              priceType: 1,
              quantityType: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$quantityTypeDetails',
    },
    {
      $lookup: {
        from: 'hs-codes',
        localField: 'equipmentTypeDetails.hsCode',
        foreignField: '_id',
        as: 'hsCodeDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              name: 1,
              code: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$hsCodeDetails',
    },
    {
      $lookup: {
        from: 'projects',
        localField: 'project',
        foreignField: '_id',
        as: 'projectDetails',
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'user',
        foreignField: '_id',
        as: 'userDetails',
      },
    },
    {
      $unwind: '$userDetails',
    },
    {
      $lookup: {
        from: 'pm-orders',
        localField: 'pmOrderId',
        foreignField: '_id',
        as: 'pmOrderDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              status: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: { path: '$pmOrderDetails', preserveNullAndEmptyArrays: true },
    },
    {
      $lookup: {
        from: 'pm-order-manage-equipments',
        localField: 'pmOrderManageEquipment',
        foreignField: '_id',
        as: 'pmOrderManageDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              pmComments: 1,
              pmRequestedQuantity: 1,
              wmApprovedQuantity: 1,
              pmReceivedQuantity: 1,
              pmDispatchQuantity: 1,
              wmReceivedQuantity: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: { path: '$pmOrderManageDetails', preserveNullAndEmptyArrays: true },
    },
    {
      $match: {
        status,
      },
    },
    {
      $lookup: {
        from: 'equipment',
        localField: 'equipmentTypeDetails._id',
        foreignField: 'equipmentType',
        as: 'associatedEquipment',
        pipeline: [
          {
            $match: {
              equipmentImage: { $exists: true, $ne: [] },
            },
          },
          { $sort: { createdAt: -1 } },
          {
            $addFields: {
              lastEquipmentImage: { $arrayElemAt: ['$equipmentImage', 0] },
            },
          },
        ],
      },
    },
    {
      $lookup: {
        from: 'equipment-certificate-types',
        localField: 'equipmentTypeDetails.certificateTypes',
        foreignField: '_id',
        as: 'certificateTypeDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              title: 1,
              isValidityDate: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: { path: '$projectDetails', preserveNullAndEmptyArrays: true },
    },
    {
      $unwind: { path: '$associatedEquipment', preserveNullAndEmptyArrays: true },
    },
    {
      $lookup: {
        from: 'users',
        let: { engineerCommentUsers: { $ifNull: ['$engineerComment.user', []] } },
        pipeline: [
          { $match: { $expr: { $in: ['$_id', '$$engineerCommentUsers'] } } },
          { $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1 } },
        ],
        as: 'engineerCommentUserDetails',
      },
    },
    {
      $lookup: {
        from: 'users',
        let: { instructionUsers: { $ifNull: ['$instruction.user', []] } },
        pipeline: [
          { $match: { $expr: { $in: ['$_id', '$$instructionUsers'] } } },
          { $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1 } },
        ],
        as: 'instructionUserDetails',
      },
    },
    {
      $lookup: {
        from: 'users',
        let: { pmCommentUsers: { $ifNull: ['$pmOrderManageDetails.pmComments.user', []] } },
        pipeline: [
          { $match: { $expr: { $in: ['$_id', '$$pmCommentUsers'] } } },
          { $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1 } },
        ],
        as: 'pmCommentUserDetails',
      },
    },
    {
      $addFields: {
        engineerComment: {
          $map: {
            input: { $ifNull: ['$engineerComment', []] },
            as: 'comment',
            in: {
              user: {
                _id: '$$comment.user',
                callingName: {
                  $arrayElemAt: [
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$engineerCommentUserDetails',
                            as: 'userDetail',
                            cond: { $eq: ['$$userDetail._id', '$$comment.user'] },
                          },
                        },
                        as: 'detail',
                        in: '$$detail.callingName',
                      },
                    },
                    0,
                  ],
                },
                firstName: {
                  $arrayElemAt: [
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$engineerCommentUserDetails',
                            as: 'userDetail',
                            cond: { $eq: ['$$userDetail._id', '$$comment.user'] },
                          },
                        },
                        as: 'detail',
                        in: '$$detail.firstName',
                      },
                    },
                    0,
                  ],
                },
                lastName: {
                  $arrayElemAt: [
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$engineerCommentUserDetails',
                            as: 'userDetail',
                            cond: { $eq: ['$$userDetail._id', '$$comment.user'] },
                          },
                        },
                        as: 'detail',
                        in: '$$detail.lastName',
                      },
                    },
                    0,
                  ],
                },
              },
              time: '$$comment.time',
              status: '$$comment.status',
              comment: '$$comment.comment',
            },
          },
        },
        instruction: {
          $map: {
            input: { $cond: [{ $eq: ['$instruction', null] }, [], ['$instruction']] },
            as: 'instructionItem',
            in: {
              user: {
                _id: '$$instructionItem.user',
                callingName: {
                  $arrayElemAt: [
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$instructionUserDetails',
                            as: 'userDetail',
                            cond: { $eq: ['$$userDetail._id', '$$instructionItem.user'] },
                          },
                        },
                        as: 'detail',
                        in: '$$detail.callingName',
                      },
                    },
                    0,
                  ],
                },
                firstName: {
                  $arrayElemAt: [
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$instructionUserDetails',
                            as: 'userDetail',
                            cond: { $eq: ['$$userDetail._id', '$$instructionItem.user'] },
                          },
                        },
                        as: 'detail',
                        in: '$$detail.firstName',
                      },
                    },
                    0,
                  ],
                },
                lastName: {
                  $arrayElemAt: [
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$instructionUserDetails',
                            as: 'userDetail',
                            cond: { $eq: ['$$userDetail._id', '$$instructionItem.user'] },
                          },
                        },
                        as: 'detail',
                        in: '$$detail.lastName',
                      },
                    },
                    0,
                  ],
                },
              },
              time: '$$instructionItem.time',
              status: '$$instructionItem.status',
              comment: '$$instructionItem.comment',
            },
          },
        },
        'pmOrderManageDetails.pmComments': {
          $map: {
            input: { $ifNull: ['$pmOrderManageDetails.pmComments', []] },
            as: 'comment',
            in: {
              user: {
                _id: '$$comment.user',
                callingName: {
                  $arrayElemAt: [
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$pmCommentUserDetails',
                            as: 'userDetail',
                            cond: { $eq: ['$$userDetail._id', '$$comment.user'] },
                          },
                        },
                        as: 'detail',
                        in: '$$detail.callingName',
                      },
                    },
                    0,
                  ],
                },
                firstName: {
                  $arrayElemAt: [
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$pmCommentUserDetails',
                            as: 'userDetail',
                            cond: { $eq: ['$$userDetail._id', '$$comment.user'] },
                          },
                        },
                        as: 'detail',
                        in: '$$detail.firstName',
                      },
                    },
                    0,
                  ],
                },
                lastName: {
                  $arrayElemAt: [
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$pmCommentUserDetails',
                            as: 'userDetail',
                            cond: { $eq: ['$$userDetail._id', '$$comment.user'] },
                          },
                        },
                        as: 'detail',
                        in: '$$detail.lastName',
                      },
                    },
                    0,
                  ],
                },
              },
              time: '$$comment.time',
              status: '$$comment.status',
              comment: '$$comment.comment',
            },
          },
        },
      },
    },
    {
      $addFields: {
        'pmOrderManageDetails.comments': {
          pmComments: '$pmOrderManageDetails.pmComments',
          wmComments: [],
        },
      },
    },
    {
      $unset: 'pmOrderManageDetails.pmComments',
    },
    {
      $lookup: {
        from: 'shopping-carts',
        localField: 'shoppingCart',
        foreignField: '_id',
        as: 'shoppingCartDetails',
      },
    },
    {
      $unwind: {
        path: '$shoppingCartDetails',
      },
    },
    {
      $group: {
        _id: '$equipmentTypeDetails._id',
        projectId: { $first: '$projectDetails._id' },
        projectName: {
          $first: {
            $concat: ['$projectDetails.projectNumber', ' - ', '$projectDetails.title'],
          },
        },
        shoppingCartId: { $first: '$shoppingCartDetails._id' },
        shoppingCartTitle: { $first: '$shoppingCartDetails.title' },
        shoppingCartFromDate: { $first: '$shoppingCartDetails.fromDate' },
        shoppingCartToDate: { $first: '$shoppingCartDetails.toDate' },
        pmOrderId: { $first: '$pmOrderId' },
        equipmentTypes: {
          $addToSet: {
            equipmentTypeId: '$equipmentTypeDetails._id',
            type: '$equipmentTypeDetails.type',
            price: '$equipmentTypeDetails.price',
            certificateTypes: '$certificateTypeDetails',
            equipmentCategory: {
              _id: '$equipmentCategoryDetails._id',
              name: '$equipmentCategoryDetails.name',
            },
            equipmentUnit: {
              _id: '$equipmentUnitDetails._id',
              name: '$equipmentUnitDetails.name',
              abbreviation: '$equipmentUnitDetails.abbreviation',
            },
            currencyUnit: {
              _id: '$currencyUnit._id',
              name: '$currencyUnit.name',
              abbreviation: '$currencyUnit.symbol',
            },
            quantityType: {
              _id: '$quantityTypeDetails._id',
              name: '$quantityTypeDetails.name',
              priceType: '$quantityTypeDetails.priceType',
            },
            hsCode: {
              _id: '$hsCodeDetails._id',
              code: '$hsCodeDetails.code',
            },
          },
        },
        equipmentTypeImage: {
          $first: '$associatedEquipment.lastEquipmentImage',
        },
        pmOrderManageEquipment: { $first: '$pmOrderManageEquipment' },
        isTemporary: { $first: '$isTemporary' },
        users: {
          $addToSet: {
            equipmentRequestId: '$_id',
            userId: '$userDetails._id',
            callingName: '$userDetails.callingName',
            firstName: '$userDetails.firstName',
            lastName: '$userDetails.lastName',
            profileImage: '$userDetails.profileImage',
            engineerRequestedQuantity: '$engineerRequestedQuantity',
            pmApprovedQuantity: '$pmApprovedQuantity',
            engineerComment: '$engineerComment',
            totalAmount: '$totalAmount',
            fromDate: '$fromDate',
            toDate: '$toDate',
            rentalDays: {
              $ceil: {
                $divide: [{ $subtract: ['$toDate', '$fromDate'] }, global.constant.DAY_CONVERTOR],
              },
            },
            pricePerEquipment: '$equipmentTypeDetails.price',
            createdAt: '$createdAt',
            temporaryProductName: '$temporaryProductName',
          },
        },
        pmOrderManageDetails: {
          $first: '$pmOrderManageDetails',
        },
        maxcreatedAt: { $max: '$createdAt' },
        createdAt: {
          $first: '$createdAt',
        },
        updatedAt: {
          $first: '$updatedAt',
        },
      },
    },
    { $unwind: '$users' },
    { $sort: { 'users.createdAt': -1 } },
    {
      $addFields: {
        totalDays: {
          $dateDiff: {
            startDate: '$shoppingCartFromDate',
            endDate: '$shoppingCartToDate',
            unit: 'day',
          },
        },
      },
    },
    {
      $group: {
        _id: '$_id',
        projectId: { $first: '$projectId' },
        projectName: { $first: '$projectName' },
        shoppingCartId: { $first: '$shoppingCartId' },
        shoppingCartTitle: { $first: '$shoppingCartTitle' },
        shoppingCartFromDate: { $first: '$shoppingCartFromDate' },
        shoppingCartToDate: { $first: '$shoppingCartToDate' },
        totalDays: { $first: '$totalDays' },
        pmOrderId: { $first: '$pmOrderId' },
        equipmentTypes: { $first: '$equipmentTypes' },
        equipmentTypeImage: { $first: '$equipmentTypeImage' },
        pmOrderManageEquipment: { $first: '$pmOrderManageEquipment' },
        users: { $push: '$users' },
        pmOrderManageDetails: { $first: '$pmOrderManageDetails' },
        isTemporary: { $first: '$isTemporary' },
        maxcreatedAt: { $max: '$maxcreatedAt' },
        totalRequestedQuantity: {
          $sum: {
            $ifNull: ['$users.engineerRequestedQuantity', 0],
          },
        },
        createdAt: { $first: '$createdAt' },
        updatedAt: { $first: '$updatedAt' },
      },
    },
    { $unwind: '$equipmentTypes' },

    { $sort: { maxcreatedAt: -1 } },
    {
      $group: {
        _id: '$projectId',
        projectName: { $first: '$projectName' },
        shoppingCartId: { $first: '$shoppingCartId' },
        shoppingCartTitle: { $first: '$shoppingCartTitle' },
        shoppingCartFromDate: { $first: '$shoppingCartFromDate' },
        shoppingCartToDate: { $first: '$shoppingCartToDate' },
        totalDays: { $first: '$totalDays' },
        pmOrderId: { $first: '$pmOrderId' },
        equipmentTypes: {
          $push: {
            _id: '$equipmentTypes.equipmentTypeId',
            type: '$equipmentTypes.type',
            price: '$equipmentTypes.price',
            certificateTypes: '$equipmentTypes.certificateTypes',
            equipmentCategory: '$equipmentTypes.equipmentCategory',
            equipmentUnit: '$equipmentTypes.equipmentUnit',
            currencyUnit: '$equipmentTypes.currencyUnit',
            hsCode: '$equipmentTypes.hsCode',
            quantityType: '$equipmentTypes.quantityType',
            equipmentTypeImage: '$equipmentTypeImage',
            pmOrderManageEquipment: { $ifNull: ['$pmOrderManageEquipment', null] },
            pmOrderManageDetails: { $ifNull: ['$pmOrderManageDetails', {}] },
            isTemporary: { $ifNull: ['$isTemporary', false] },
            users: '$users',
            maxCreatedAt: '$equipmentTypes.maxCreatedAt',
            totalEngineerRequestedQuantity: {
              $sum: {
                $ifNull: ['$users.engineerRequestedQuantity', 0],
              },
            },
          },
        },
        totalEquipmentType: { $sum: 1 },
        totalQuantity: {
          $sum: {
            $ifNull: ['$totalRequestedQuantity', 0],
          },
        },
        createdAt: { $first: '$createdAt' },
      },
    },
    {
      $sort: {
        createdAt: sort,
      },
    },
  ];

  if (page === '' && perPage === '') {
    return await EquipmentOrder.aggregate(pipeline);
  }

  pipeline.push(
    {
      $skip: parseInt(page) * parseInt(perPage),
    },
    {
      $limit: parseInt(perPage),
    }
  );

  return await EquipmentOrder.aggregate(pipeline);
};

/**
 * get shopping cart wise project equipments
 *
 * @param {*} filter
 * @param {*} status
 * @param {*} search
 * @param {*} page
 * @param {*} perPage
 * @param {*} sort
 * @returns
 */
exports.shoppingCartWiseProjectEquipments = async (filter, search, page, perPage, sort) => {
  const pipeline = [
    {
      $match: filter,
    },
    {
      $lookup: {
        from: 'equipment-orders',
        localField: '_id',
        foreignField: 'shoppingCart',
        as: 'equipmentOrders',
      },
    },
    { $unwind: { path: '$equipmentOrders', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: 'equipment-types',
        localField: 'equipmentOrders.equipmentType',
        foreignField: '_id',
        as: 'equipmentTypeDetails',
      },
    },
    { $unwind: { path: '$equipmentTypeDetails', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: 'projects',
        localField: 'project',
        foreignField: '_id',
        as: 'projectDetails',
      },
    },
    { $unwind: { path: '$projectDetails', preserveNullAndEmptyArrays: true } },
    {
      $match: {
        $or: [
          { 'equipmentTypeDetails.type': { $regex: search, $options: 'i' } },
          { 'projectDetails.title': { $regex: search, $options: 'i' } },
          { title: { $regex: search, $options: 'i' } },
        ],
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'equipmentOrders.user',
        foreignField: '_id',
        as: 'userDetails',
      },
    },
    { $unwind: { path: '$userDetails', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: 'equipment',
        localField: 'equipmentTypeDetails._id',
        foreignField: 'equipmentType',
        as: 'associatedEquipment',
        pipeline: [
          { $match: { equipmentImage: { $exists: true, $ne: [] } } },
          { $sort: { createdAt: -1 } },
          {
            $addFields: {
              lastEquipmentImage: { $arrayElemAt: ['$equipmentImage', 0] },
            },
          },
        ],
      },
    },
    { $unwind: { path: '$associatedEquipment', preserveNullAndEmptyArrays: true } },
    {
      $addFields: {
        engineerRequestedQuantity: {
          $toDouble: { $ifNull: ['$equipmentOrders.engineerRequestedQuantity', 0] },
        },
      },
    },
    {
      $group: {
        _id: {
          shoppingCart: '$_id',
          projectId: '$projectDetails._id',
          equipmentTypeId: '$equipmentTypeDetails._id',
          userId: '$userDetails._id',
        },
        shoppingCart: { $first: '$_id' },
        shoppingCartTitle: { $first: '$title' },
        projectId: { $first: '$projectDetails._id' },
        projectName: {
          $first: {
            $cond: {
              if: { $ne: ['$projectDetails.projectNumber', ''] },
              then: {
                $concat: [
                  '$projectDetails.projectNumber',
                  ' - ',
                  { $ifNull: ['$projectDetails.title', ''] },
                ],
              },
              else: { $ifNull: ['$projectDetails.title', ''] },
            },
          },
        },
        equipmentType: {
          $first: {
            equipmentTypeId: '$equipmentTypeDetails._id',
            type: '$equipmentTypeDetails.type',
            price: '$equipmentTypeDetails.price',
            equipmentTypeImage: '$associatedEquipment.lastEquipmentImage',
          },
        },
        engineerRequestedQuantity: { $first: '$engineerRequestedQuantity' },
        createdAt: { $first: '$createdAt' },
        updatedAt: { $first: '$updatedAt' },
      },
    },
    {
      $group: {
        _id: {
          shoppingCart: '$_id.shoppingCart',
          projectId: '$_id.projectId',
          equipmentTypeId: '$_id.equipmentTypeId',
        },
        shoppingCart: { $first: '$_id.shoppingCart' },
        shoppingCartTitle: { $first: '$shoppingCartTitle' },
        projectId: { $first: '$_id.projectId' },
        projectName: { $first: '$projectName' },
        equipmentType: { $first: '$equipmentType' },
        totalEngineerRequestedQuantity: { $sum: '$engineerRequestedQuantity' },
        createdAt: { $first: '$createdAt' },
        updatedAt: { $first: '$updatedAt' },
      },
    },
    {
      $group: {
        _id: { shoppingCart: '$_id.shoppingCart', projectId: '$_id.projectId' },
        shoppingCart: { $first: '$_id.shoppingCart' },
        shoppingCartTitle: { $first: '$shoppingCartTitle' },
        projectId: { $first: '$projectId' },
        projectName: { $first: '$projectName' },
        equipmentTypes: {
          $push: {
            _id: '$equipmentType.equipmentTypeId',
            type: '$equipmentType.type',
            price: '$equipmentType.price',
            equipmentTypeImage: '$equipmentType.equipmentTypeImage',
            totalEngineerRequestedQuantity: '$totalEngineerRequestedQuantity',
          },
        },
        totalQuantity: { $sum: '$totalEngineerRequestedQuantity' },
        totalEquipmentType: {
          $sum: {
            $cond: [{ $gt: ['$totalEngineerRequestedQuantity', 0] }, 1, 0],
          },
        },
        createdAt: { $first: '$createdAt' },
        updatedAt: { $first: '$updatedAt' },
      },
    },
    {
      $addFields: {
        totalEquipmentType: {
          $cond: [{ $eq: ['$totalQuantity', 0] }, 0, '$totalEquipmentType'],
        },
      },
    },
    { $sort: { createdAt: sort || -1 } },
  ];

  if (page !== '' && perPage !== '') {
    pipeline.push({ $skip: parseInt(page) * parseInt(perPage) }, { $limit: parseInt(perPage) });
  }

  return await ShoppingCart.aggregate(pipeline);
};

exports.listShoppingCartProjectEquipmentOrder = async (
  filter,
  status,
  search,
  page,
  perPage,
  sort
) => {
  const pipeline = [
    {
      $match: filter,
    },
    {
      $lookup: {
        from: 'equipment-types',
        localField: 'equipmentType',
        foreignField: '_id',
        as: 'equipmentTypeDetails',
        pipeline: [
          ...(search
            ? [
                {
                  $match: {
                    type: {
                      $regex: search,
                      $options: 'i',
                    },
                    account: filter.account,
                  },
                },
              ]
            : []),
        ],
      },
    },
    {
      $unwind: '$equipmentTypeDetails',
    },
    {
      $lookup: {
        from: 'equipment-certificate-types',
        localField: 'equipmentTypeDetails.certificateTypes',
        foreignField: '_id',
        as: 'certificateTypeDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              title: 1,
              isValidityDate: 1,
            },
          },
        ],
      },
    },
    {
      $lookup: {
        from: 'equipment-categories',
        localField: 'equipmentTypeDetails.equipmentCategory',
        foreignField: '_id',
        as: 'equipmentCategoryDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              name: 1,
              abbreviation: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$equipmentCategoryDetails',
    },
    {
      $lookup: {
        from: 'equipment-units',
        localField: 'equipmentTypeDetails.equipmentUnit',
        foreignField: '_id',
        as: 'equipmentUnitDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              title: 1,
              abbreviation: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$equipmentUnitDetails',
    },
    {
      $lookup: {
        from: 'currency-units',
        localField: 'equipmentTypeDetails.currencyUnit',
        foreignField: '_id',
        as: 'currencyUnit',
        pipeline: [
          {
            $project: {
              name: 1,
              symbol: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$currencyUnit',
    },
    {
      $lookup: {
        from: 'equipment-quantity-types',
        localField: 'equipmentTypeDetails.quantityType',
        foreignField: '_id',
        as: 'quantityTypeDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              name: 1,
              priceType: 1,
              quantityType: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$quantityTypeDetails',
    },
    {
      $lookup: {
        from: 'hs-codes',
        localField: 'equipmentTypeDetails.hsCode',
        foreignField: '_id',
        as: 'hsCodeDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              name: 1,
              code: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$hsCodeDetails',
    },
    {
      $lookup: {
        from: 'projects',
        localField: 'project',
        foreignField: '_id',
        as: 'projectDetails',
      },
    },
    {
      $lookup: {
        from: 'shopping-carts',
        localField: 'shoppingCart',
        foreignField: '_id',
        as: 'shoppingCartDetails',
      },
    },
    {
      $unwind: '$shoppingCartDetails',
    },
    {
      $lookup: {
        from: 'users',
        localField: 'user',
        foreignField: '_id',
        as: 'userDetails',
      },
    },
    {
      $unwind: '$userDetails',
    },
    {
      $lookup: {
        from: 'pm-orders',
        localField: 'pmOrderId',
        foreignField: '_id',
        as: 'pmOrderDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              status: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: { path: '$pmOrderDetails', preserveNullAndEmptyArrays: true },
    },
    {
      $lookup: {
        from: 'pm-order-manage-equipments',
        localField: 'pmOrderManageEquipment',
        foreignField: '_id',
        as: 'pmOrderManageDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              pmComments: 1,
              pmRequestedQuantity: 1,
              wmApprovedQuantity: 1,
              pmReceivedQuantity: 1,
              pmDispatchQuantity: 1,
              wmReceivedQuantity: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: { path: '$pmOrderManageDetails', preserveNullAndEmptyArrays: true },
    },
    {
      $match: {
        status,
      },
    },
    {
      $lookup: {
        from: 'equipment',
        localField: 'equipmentTypeDetails._id',
        foreignField: 'equipmentType',
        as: 'associatedEquipment',
        pipeline: [
          {
            $match: {
              equipmentImage: { $exists: true, $ne: [] },
            },
          },
          { $sort: { createdAt: -1 } },
          {
            $addFields: {
              lastEquipmentImage: { $arrayElemAt: ['$equipmentImage', 0] },
            },
          },
        ],
      },
    },
    {
      $lookup: {
        from: 'equipment-certificate-types',
        localField: 'equipmentTypeDetails.certificateTypes',
        foreignField: '_id',
        as: 'certificateTypeDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              title: 1,
              isValidityDate: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: { path: '$projectDetails', preserveNullAndEmptyArrays: true },
    },
    {
      $unwind: { path: '$associatedEquipment', preserveNullAndEmptyArrays: true },
    },
    {
      $lookup: {
        from: 'users',
        let: { engineerCommentUsers: { $ifNull: ['$engineerComment.user', []] } },
        pipeline: [
          { $match: { $expr: { $in: ['$_id', '$$engineerCommentUsers'] } } },
          { $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1 } },
        ],
        as: 'engineerCommentUserDetails',
      },
    },
    {
      $lookup: {
        from: 'users',
        let: { instructionUsers: { $ifNull: ['$instruction.user', []] } },
        pipeline: [
          { $match: { $expr: { $in: ['$_id', '$$instructionUsers'] } } },
          { $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1 } },
        ],
        as: 'instructionUserDetails',
      },
    },
    {
      $lookup: {
        from: 'users',
        let: { pmCommentUsers: { $ifNull: ['$pmOrderManageDetails.pmComments.user', []] } },
        pipeline: [
          { $match: { $expr: { $in: ['$_id', '$$pmCommentUsers'] } } },
          { $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1 } },
        ],
        as: 'pmCommentUserDetails',
      },
    },
    {
      $addFields: {
        engineerComment: {
          $map: {
            input: { $ifNull: ['$engineerComment', []] },
            as: 'comment',
            in: {
              user: {
                _id: '$$comment.user',
                callingName: {
                  $arrayElemAt: [
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$engineerCommentUserDetails',
                            as: 'userDetail',
                            cond: { $eq: ['$$userDetail._id', '$$comment.user'] },
                          },
                        },
                        as: 'detail',
                        in: '$$detail.callingName',
                      },
                    },
                    0,
                  ],
                },
                firstName: {
                  $arrayElemAt: [
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$engineerCommentUserDetails',
                            as: 'userDetail',
                            cond: { $eq: ['$$userDetail._id', '$$comment.user'] },
                          },
                        },
                        as: 'detail',
                        in: '$$detail.firstName',
                      },
                    },
                    0,
                  ],
                },
                lastName: {
                  $arrayElemAt: [
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$engineerCommentUserDetails',
                            as: 'userDetail',
                            cond: { $eq: ['$$userDetail._id', '$$comment.user'] },
                          },
                        },
                        as: 'detail',
                        in: '$$detail.lastName',
                      },
                    },
                    0,
                  ],
                },
              },
              time: '$$comment.time',
              status: '$$comment.status',
              comment: '$$comment.comment',
            },
          },
        },
        instruction: {
          $map: {
            input: { $cond: [{ $eq: ['$instruction', null] }, [], ['$instruction']] },
            as: 'instructionItem',
            in: {
              user: {
                _id: '$$instructionItem.user',
                callingName: {
                  $arrayElemAt: [
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$instructionUserDetails',
                            as: 'userDetail',
                            cond: { $eq: ['$$userDetail._id', '$$instructionItem.user'] },
                          },
                        },
                        as: 'detail',
                        in: '$$detail.callingName',
                      },
                    },
                    0,
                  ],
                },
                firstName: {
                  $arrayElemAt: [
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$instructionUserDetails',
                            as: 'userDetail',
                            cond: { $eq: ['$$userDetail._id', '$$instructionItem.user'] },
                          },
                        },
                        as: 'detail',
                        in: '$$detail.firstName',
                      },
                    },
                    0,
                  ],
                },
                lastName: {
                  $arrayElemAt: [
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$instructionUserDetails',
                            as: 'userDetail',
                            cond: { $eq: ['$$userDetail._id', '$$instructionItem.user'] },
                          },
                        },
                        as: 'detail',
                        in: '$$detail.lastName',
                      },
                    },
                    0,
                  ],
                },
              },
              time: '$$instructionItem.time',
              status: '$$instructionItem.status',
              comment: '$$instructionItem.comment',
            },
          },
        },
        'pmOrderManageDetails.pmComments': {
          $map: {
            input: { $ifNull: ['$pmOrderManageDetails.pmComments', []] },
            as: 'comment',
            in: {
              user: {
                _id: '$$comment.user',
                callingName: {
                  $arrayElemAt: [
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$pmCommentUserDetails',
                            as: 'userDetail',
                            cond: { $eq: ['$$userDetail._id', '$$comment.user'] },
                          },
                        },
                        as: 'detail',
                        in: '$$detail.callingName',
                      },
                    },
                    0,
                  ],
                },
                firstName: {
                  $arrayElemAt: [
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$pmCommentUserDetails',
                            as: 'userDetail',
                            cond: { $eq: ['$$userDetail._id', '$$comment.user'] },
                          },
                        },
                        as: 'detail',
                        in: '$$detail.firstName',
                      },
                    },
                    0,
                  ],
                },
                lastName: {
                  $arrayElemAt: [
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$pmCommentUserDetails',
                            as: 'userDetail',
                            cond: { $eq: ['$$userDetail._id', '$$comment.user'] },
                          },
                        },
                        as: 'detail',
                        in: '$$detail.lastName',
                      },
                    },
                    0,
                  ],
                },
              },
              time: '$$comment.time',
              status: '$$comment.status',
              comment: '$$comment.comment',
            },
          },
        },
      },
    },
    {
      $addFields: {
        'pmOrderManageDetails.comments': {
          pmComments: '$pmOrderManageDetails.pmComments',
          wmComments: [],
        },
      },
    },
    {
      $unset: 'pmOrderManageDetails.pmComments',
    },
    {
      $group: {
        _id: {
          projectId: '$projectDetails._id',
          shoppingCartId: '$shoppingCart',
          equipmentTypeId: '$equipmentTypeDetails._id',
        },
        projectName: {
          $first: {
            $concat: ['$projectDetails.projectNumber', ' - ', '$projectDetails.title'],
          },
        },
        shoppingCartId: { $first: '$shoppingCart' },
        shoppingCartTitle: { $first: '$shoppingCartDetails.title' },
        shoppingCartFromDate: { $first: '$shoppingCartDetails.fromDate' },
        shoppingCartToDate: { $first: '$shoppingCartDetails.toDate' },
        pmOrderId: { $first: '$pmOrderId' },
        equipmentTypes: {
          $addToSet: {
            equipmentTypeId: '$equipmentTypeDetails._id',
            type: '$equipmentTypeDetails.type',
            price: '$equipmentTypeDetails.price',
            certificateTypes: '$certificateTypeDetails',
            equipmentCategory: {
              _id: '$equipmentCategoryDetails._id',
              name: '$equipmentCategoryDetails.name',
            },
            equipmentUnit: {
              _id: '$equipmentUnitDetails._id',
              name: '$equipmentUnitDetails.name',
              abbreviation: '$equipmentUnitDetails.abbreviation',
            },
            currencyUnit: {
              _id: '$currencyUnit._id',
              name: '$currencyUnit.name',
              abbreviation: '$currencyUnit.symbol',
            },
            quantityType: {
              _id: '$quantityTypeDetails._id',
              name: '$quantityTypeDetails.name',
              priceType: '$quantityTypeDetails.priceType',
            },
            hsCode: {
              _id: '$hsCodeDetails._id',
              code: '$hsCodeDetails.code',
            },
          },
        },
        equipmentTypeImage: {
          $first: '$associatedEquipment.lastEquipmentImage',
        },
        pmOrderManageEquipment: { $first: '$pmOrderManageEquipment' },
        isTemporary: { $first: '$isTemporary' },
        users: {
          $addToSet: {
            equipmentRequestId: '$_id',
            userId: '$userDetails._id',
            callingName: '$userDetails.callingName',
            firstName: '$userDetails.firstName',
            lastName: '$userDetails.lastName',
            profileImage: '$userDetails.profileImage',
            engineerRequestedQuantity: '$engineerRequestedQuantity',
            pmApprovedQuantity: '$pmApprovedQuantity',
            engineerComment: '$engineerComment',
            totalAmount: '$totalAmount',
            fromDate: '$fromDate',
            toDate: '$toDate',
            rentalDays: {
              $ceil: {
                $divide: [{ $subtract: ['$toDate', '$fromDate'] }, global.constant.DAY_CONVERTOR],
              },
            },
            pricePerEquipment: '$equipmentTypeDetails.price',
            createdAt: '$createdAt',
            temporaryProductName: '$temporaryProductName',
          },
        },
        pmOrderManageDetails: {
          $first: '$pmOrderManageDetails',
        },
        maxcreatedAt: { $max: '$createdAt' },
        createdAt: {
          $first: '$createdAt',
        },
        updatedAt: {
          $first: '$updatedAt',
        },
      },
    },

    { $unwind: '$users' },
    { $sort: { 'users.createdAt': -1 } },
    {
      $group: {
        _id: {
          projectId: '$_id.projectId',
          shoppingCartId: '$_id.shoppingCartId',
          equipmentTypeId: '$_id.equipmentTypeId',
        },
        projectName: { $first: '$projectName' },
        shoppingCartId: { $first: '$shoppingCartId' },
        shoppingCartTitle: { $first: '$shoppingCartTitle' },
        shoppingCartFromDate: { $first: '$shoppingCartFromDate' },
        shoppingCartToDate: { $first: '$shoppingCartToDate' },
        pmOrderId: { $first: '$pmOrderId' },
        equipmentTypes: { $first: '$equipmentTypes' },
        equipmentTypeImage: { $first: '$equipmentTypeImage' },
        pmOrderManageEquipment: { $first: '$pmOrderManageEquipment' },
        isTemporary: { $first: '$isTemporary' },
        users: { $push: '$users' },
        pmOrderManageDetails: { $first: '$pmOrderManageDetails' },
        maxcreatedAt: { $max: '$maxcreatedAt' },
        createdAt: { $first: '$createdAt' },
        updatedAt: { $first: '$updatedAt' },
      },
    },
    { $unwind: '$equipmentTypes' },

    { $sort: { maxcreatedAt: -1 } },
    {
      $group: {
        _id: {
          projectId: '$_id.projectId',
          shoppingCartId: '$_id.shoppingCartId',
        },
        projectName: { $first: '$projectName' },
        shoppingCartId: { $first: '$shoppingCartId' },
        shoppingCartTitle: { $first: '$shoppingCartTitle' },
        shoppingCartFromDate: { $first: '$shoppingCartFromDate' },
        shoppingCartToDate: { $first: '$shoppingCartToDate' },
        pmOrderId: { $first: '$pmOrderId' },
        equipmentTypes: {
          $push: {
            _id: '$equipmentTypes.equipmentTypeId',
            type: '$equipmentTypes.type',
            price: '$equipmentTypes.price',
            certificateTypes: '$equipmentTypes.certificateTypes',
            equipmentCategory: '$equipmentTypes.equipmentCategory',
            equipmentUnit: '$equipmentTypes.equipmentUnit',
            currencyUnit: '$equipmentTypes.currencyUnit',
            hsCode: '$equipmentTypes.hsCode',
            quantityType: '$equipmentTypes.quantityType',
            equipmentTypeImage: '$equipmentTypeImage',
            pmOrderManageEquipment: { $ifNull: ['$pmOrderManageEquipment', null] },
            pmOrderManageDetails: { $ifNull: ['$pmOrderManageDetails', {}] },
            isTemporary: { $ifNull: ['$isTemporary', false] },
            users: '$users',
            maxCreatedAt: '$equipmentTypes.maxCreatedAt',
            totalEngineerRequestedQuantity: {
              $sum: {
                $ifNull: ['$users.engineerRequestedQuantity', 0],
              },
            },
          },
        },
        totalEquipmentType: { $sum: 1 },
        totalQuantity: {
          $sum: {
            $ifNull: ['$users.totalEngineerRequestedQuantity', 0],
          },
        },
        createdAt: { $first: '$createdAt' },
      },
    },
    {
      $sort: {
        createdAt: sort,
      },
    },
  ];

  if (page === '' && perPage === '') {
    return await EquipmentOrder.aggregate(pipeline);
  }

  pipeline.push(
    {
      $skip: parseInt(page) * parseInt(perPage),
    },
    {
      $limit: parseInt(perPage),
    }
  );

  return await EquipmentOrder.aggregate(pipeline);
};

/**
 * List Equipment Order Count
 *
 * @param {*} filter
 * @returns
 */
exports.listEquipmentOrderCount = async filter => {
  const pipeline = [
    {
      $match: filter,
    },
    {
      $lookup: {
        from: 'projects',
        localField: 'project',
        foreignField: '_id',
        as: 'projectDetails',
      },
    },
    {
      $unwind: { path: '$projectDetails', preserveNullAndEmptyArrays: true },
    },
    {
      $group: {
        _id: '$projectDetails._id',
      },
    },
  ];

  return await EquipmentOrder.aggregate(pipeline);
};

/**
 * List Shopping Cart Order Count
 *
 * @param {*} filter
 * @returns
 */
exports.listShoppingCartOrderCount = async filter => {
  const pipeline = [
    {
      $match: filter,
    },
    {
      $group: {
        _id: '$shoppingCart',
      },
    },
  ];
  return await EquipmentOrder.aggregate(pipeline);
};
