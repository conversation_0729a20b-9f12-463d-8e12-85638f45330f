/* models */
const Nationality = require('../models/nationality.model');

// Utils
const NATIONALITY_DATA = require('../utils/nationality.utils');

/**
 * Prepare and insert Nationality data in collection
 *
 * @returns
 */
exports.up = async () => {
  let countDocuments = await Nationality.countDocuments();

  if (countDocuments === 0) {
    await Nationality.insertMany(NATIONALITY_DATA);
  } else {
    console.log('Nationality already exists');
    return true;
  }
};
