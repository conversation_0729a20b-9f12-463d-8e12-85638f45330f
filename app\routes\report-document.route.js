// library
const express = require('express');
const routes = express.Router();

// Validator
const validator = require('../validators/report-document.validator');

// middleware
const { verifyToken, authAccount, deletedAt } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const reportDocumentController = require('../controllers/report-document.controller.js');

module.exports = routes;

// Create Report Document
routes.post(
  '',
  verifyToken,
  authAccount,
  validator.createReportDocumentValidationRule(),
  validate,
  reportDocumentController.createReportDocument
);

// Update Report Document
routes.put(
  '/:id',
  verifyToken,
  authAccount,
  validate,
  reportDocumentController.updateReportDocument
);

// get report document by user project report
routes.get(
  '/user-project-report/:id',
  verifyToken,
  authAccount,
  validate,
  reportDocumentController.getReportDocument
);

// delete report document
routes.delete(
  '/:id',
  verifyToken,
  authAccount,
  validate,
  deletedAt,
  reportDocumentController.deleteReportDocument
);
