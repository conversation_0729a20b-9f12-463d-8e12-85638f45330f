const { body, constantUtils } = require('../validators/parent.validator');

exports.questionValidationRule = () => {
  return [body('title').isString().notEmpty().withMessage(constantUtils.TITLE_REQUIRED)];
};

exports.updateQuestionValidationRule = () => {
  return [
    body('title')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.TITLE_REQUIRED)
      .optional({ checkFalsy: false }),
  ];
};
