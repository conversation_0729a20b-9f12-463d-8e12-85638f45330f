// library
const express = require('express');
const routes = express.Router();

// middleware
const { verifyToken, defaultCreatedDetails } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');
const {
  createEquipmentWarehouseValidationRule,
} = require('../validators/equipment-warehouse.validator');

// controller
const equipmentWarehouseController = require('../controllers/equipment-warehouse.controller');

//Create
routes.post(
  '',
  verifyToken,
  createEquipmentWarehouseValidationRule(),
  validate,
  defaultCreatedDetails,
  equipmentWarehouseController.createEquipmentWarehouse
);

module.exports = routes;
