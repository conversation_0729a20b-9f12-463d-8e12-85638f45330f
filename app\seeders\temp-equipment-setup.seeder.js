/* models */
const EquipmentCategory = require('../models/equipment-category.model');
const CurrencyUnit = require('../models/currency-unit.model');
const EquipmentUnit = require('../models/equipment-unit.model');
const EquipmentQuantityType = require('../models/equipment-quantity-type.model');
const HSCode = require('../models/hs-code.model');
// const EquipmentType = require('../models/equipment-type.model');
const Role = require('../models/role.model');
const User = require('../models/user.model');

/* Command to run script
-> npm run seeder temp-equipment-setup
*/

/**
 * Prepare and add default equipment setup data
 *
 * @returns
 */
exports.up = async () => {
  try {
    const getRole = await Role.find({ title: 'admin', deletedAt: null });

    let counter = 0;
    for (let data of getRole) {
      const user = await User.findOne({ role: data._id, deletedAt: null });

      if (user) {
        const { account, _id } = user;
        let defaultParams = {
          isTemporary: true,
          account,
          createdBy: _id,
          createdAt: new Date(),
        };

        let equipmentCategory = await EquipmentCategory.findOne({
          name: 'Temporary Category',
          account,
          deletedAt: null,
        });

        if (!equipmentCategory) {
          // prepare equipment category data
          await EquipmentCategory.create({
            name: 'Temporary Category',
            abbreviation: 'TMP',
            isActive: true,
            ...defaultParams,
          });
        } else {
          await EquipmentCategory.findByIdAndUpdate(equipmentCategory._id, {
            isTemporary: true,
          });
        }

        let currencyUnit = await CurrencyUnit.findOne({
          name: 'Temp',
          account,
          deletedAt: null,
        });

        if (!currencyUnit) {
          // prepare currency unit data
          await CurrencyUnit.create({
            name: 'Temp',
            symbol: 'TMP',
            ...defaultParams,
          });
        } else {
          await CurrencyUnit.findByIdAndUpdate(currencyUnit._id, {
            isTemporary: true,
          });
        }

        let equipmentUnit = await EquipmentUnit.findOne({
          title: 'Temp Unit',
          account,
          deletedAt: null,
        });

        if (!equipmentUnit) {
          // prepare equipment unit data
          await EquipmentUnit.create({
            title: 'Temp Unit',
            abbreviation: 'TU',
            isActive: true,
            ...defaultParams,
          });
        } else {
          await EquipmentUnit.findByIdAndUpdate(equipmentUnit._id, { isTemporary: true });
        }

        let equipmentQuantityType = await EquipmentQuantityType.findOne({
          name: 'Temporary Quantity Type',
          account,
          deletedAt: null,
        });

        if (!equipmentQuantityType) {
          // prepare equipment quantity type data
          equipmentQuantityType = await EquipmentQuantityType.create({
            name: 'Temporary Quantity Type',
            abbreviation: 'TQT',
            priceType: 'buy',
            quantityType: 'multiple',
            isActive: true,
            ...defaultParams,
          });
        } else {
          await EquipmentQuantityType.findByIdAndUpdate(equipmentQuantityType._id, {
            isTemporary: true,
          });
        }

        let hsCode = await HSCode.findOne({
          name: 'Temporary HS Code',
          account,
          deletedAt: null,
        });

        if (!hsCode) {
          // prepare HS code data
          await HSCode.create({
            name: 'Temporary HS Code',
            code: '-',
            isActive: true,
            ...defaultParams,
          });
        } else {
          await HSCode.findByIdAndUpdate(hsCode._id, {
            isTemporary: true,
          });
        }
        counter++;
      }
    }
    console.log(`Created ${counter} default equipment setup data successfully`);
  } catch (error) {
    console.error('Error in temp-equipment-setup seeder', error);
  }
};
