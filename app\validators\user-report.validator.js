const { body, constantUtils } = require('../validators/parent.validator');

exports.userReportValidationRule = () => {
  return [
    body('project').notEmpty().withMessage(constantUtils.PROJECT_REQUIRED),
    body('report').notEmpty().withMessage(constantUtils.REPORT_REQUIRED),
    body('location').notEmpty().withMessage(constantUtils.LOCATION_REQUIRED),
  ];
};

exports.webUserReportValidationRule = () => {
  return [
    body('project').notEmpty().withMessage(constantUtils.PROJECT_REQUIRED),
    body('report').notEmpty().withMessage(constantUtils.REPORT_REQUIRED),
    body('location').notEmpty().withMessage(constantUtils.LOCATION_REQUIRED),
    body('questions').notEmpty().withMessage(constantUtils.QUESTION_REQUIRED),
    body('signature').notEmpty().withMessage(constantUtils.SIGNATURE_REQUIRED),
    body('status').notEmpty().withMessage(constantUtils.STATUS_REQUIRED),
  ];
};

exports.userReportDetailValidationRule = () => {
  return [body('userReports').isArray().notEmpty().withMessage(constantUtils.USER_REPORT_REQUIRED)];
};
