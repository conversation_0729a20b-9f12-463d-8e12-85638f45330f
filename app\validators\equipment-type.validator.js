const { body, constantUtils } = require('../validators/parent.validator');

exports.createEquipmentTypeValidationRule = () => {
  return [
    body('type').isString().notEmpty().withMessage(constantUtils.TYPE_REQUIRED),
    body('equipmentCategory').notEmpty().withMessage(constantUtils.EQUIPMENT_CATEGORY_REQUIRED),
    body('equipmentUnit').notEmpty().withMessage(constantUtils.EQUIPMENT_UNIT_REQUIRED),
    body('price').isNumeric().notEmpty().withMessage(constantUtils.EQUIPMENT_PRICE_REQUIRED),
    body('quantityType').notEmpty().withMessage(constantUtils.QUANTITY_TYPE_REQUIRED),
    body('hsCode').notEmpty().withMessage(constantUtils.HS_CODE_REQUIRED),
  ];
};

exports.updateEquipmentTypeValidationRule = () => {
  return [
    body('type')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.TYPE_REQUIRED)
      .optional({ checkFalsy: false }),
    body('equipmentCategory')
      .notEmpty()
      .withMessage(constantUtils.EQUIPMENT_CATEGORY_REQUIRED)
      .optional({ checkFalsy: false }),
    body('equipmentUnit')
      .notEmpty()
      .withMessage(constantUtils.EQUIPMENT_UNIT_REQUIRED)
      .optional({ checkFalsy: false }),
    body('price')
      .isNumeric()
      .notEmpty()
      .withMessage(constantUtils.EQUIPMENT_PRICE_REQUIRED)
      .optional({ checkFalsy: false }),
    body('quantityType')
      .notEmpty()
      .withMessage(constantUtils.QUANTITY_TYPE_REQUIRED)
      .optional({ checkFalsy: false }),
    body('hsCode')
      .notEmpty()
      .withMessage(constantUtils.HS_CODE_REQUIRED)
      .optional({ checkFalsy: false }),
  ];
};
