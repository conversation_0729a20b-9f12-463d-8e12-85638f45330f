module.exports = [
  {
    code: '4',
    alphaCode2: 'AF',
    alphaCode3: 'AFG',
    country: 'Afghanistan',
    nationality: 'Afghan',
  },
  {
    code: '248',
    alphaCode2: 'AX',
    alphaCode3: 'ALA',
    country: '\u00c5land Islands',
    nationality: '\u00c5land Island',
  },
  {
    code: '8',
    alphaCode2: 'AL',
    alphaCode3: 'ALB',
    country: 'Albania',
    nationality: 'Albanian',
  },
  {
    code: '12',
    alphaCode2: 'DZ',
    alphaCode3: 'DZA',
    country: 'Algeria',
    nationality: 'Algerian',
  },
  {
    code: '16',
    alphaCode2: 'AS',
    alphaCode3: 'ASM',
    country: 'American Samoa',
    nationality: 'American Samoan',
  },
  {
    code: '20',
    alphaCode2: 'AD',
    alphaCode3: 'AND',
    country: 'Andorra',
    nationality: 'Andorran',
  },
  {
    code: '24',
    alphaCode2: 'AO',
    alphaCode3: 'AGO',
    country: 'Angola',
    nationality: 'Angolan',
  },
  {
    code: '660',
    alphaCode2: 'AI',
    alphaCode3: 'AIA',
    country: 'Anguilla',
    nationality: 'Anguillan',
  },
  {
    code: '10',
    alphaCode2: 'AQ',
    alphaCode3: 'ATA',
    country: 'Antarctica',
    nationality: 'Antarctic',
  },
  {
    code: '28',
    alphaCode2: 'AG',
    alphaCode3: 'ATG',
    country: 'Antigua and Barbuda',
    nationality: 'Antiguan or Barbudan',
  },
  {
    code: '32',
    alphaCode2: 'AR',
    alphaCode3: 'ARG',
    country: 'Argentina',
    nationality: 'Argentine',
  },
  {
    code: '51',
    alphaCode2: 'AM',
    alphaCode3: 'ARM',
    country: 'Armenia',
    nationality: 'Armenian',
  },
  {
    code: '533',
    alphaCode2: 'AW',
    alphaCode3: 'ABW',
    country: 'Aruba',
    nationality: 'Aruban',
  },
  {
    code: '36',
    alphaCode2: 'AU',
    alphaCode3: 'AUS',
    country: 'Australia',
    nationality: 'Australian',
  },
  {
    code: '40',
    alphaCode2: 'AT',
    alphaCode3: 'AUT',
    country: 'Austria',
    nationality: 'Austrian',
  },
  {
    code: '31',
    alphaCode2: 'AZ',
    alphaCode3: 'AZE',
    country: 'Azerbaijan',
    nationality: 'Azerbaijani, Azeri',
  },
  {
    code: '44',
    alphaCode2: 'BS',
    alphaCode3: 'BHS',
    country: 'Bahamas',
    nationality: 'Bahamian',
  },
  {
    code: '48',
    alphaCode2: 'BH',
    alphaCode3: 'BHR',
    country: 'Bahrain',
    nationality: 'Bahraini',
  },
  {
    code: '50',
    alphaCode2: 'BD',
    alphaCode3: 'BGD',
    country: 'Bangladesh',
    nationality: 'Bangladeshi',
  },
  {
    code: '52',
    alphaCode2: 'BB',
    alphaCode3: 'BRB',
    country: 'Barbados',
    nationality: 'Barbadian',
  },
  {
    code: '112',
    alphaCode2: 'BY',
    alphaCode3: 'BLR',
    country: 'Belarus',
    nationality: 'Belarusian',
  },
  {
    code: '56',
    alphaCode2: 'BE',
    alphaCode3: 'BEL',
    country: 'Belgium',
    nationality: 'Belgian',
  },
  {
    code: '84',
    alphaCode2: 'BZ',
    alphaCode3: 'BLZ',
    country: 'Belize',
    nationality: 'Belizean',
  },
  {
    code: '204',
    alphaCode2: 'BJ',
    alphaCode3: 'BEN',
    country: 'Benin',
    nationality: 'Beninese, Beninois',
  },
  {
    code: '60',
    alphaCode2: 'BM',
    alphaCode3: 'BMU',
    country: 'Bermuda',
    nationality: 'Bermudian, Bermudan',
  },
  {
    code: '64',
    alphaCode2: 'BT',
    alphaCode3: 'BTN',
    country: 'Bhutan',
    nationality: 'Bhutanese',
  },
  {
    code: '68',
    alphaCode2: 'BO',
    alphaCode3: 'BOL',
    country: 'Bolivia (Plurinational State of)',
    nationality: 'Bolivian',
  },
  {
    code: '535',
    alphaCode2: 'BQ',
    alphaCode3: 'BES',
    country: 'Bonaire, Sint Eustatius and Saba',
    nationality: 'Bonaire',
  },
  {
    code: '70',
    alphaCode2: 'BA',
    alphaCode3: 'BIH',
    country: 'Bosnia and Herzegovina',
    nationality: 'Bosnian or Herzegovinian',
  },
  {
    code: '72',
    alphaCode2: 'BW',
    alphaCode3: 'BWA',
    country: 'Botswana',
    nationality: 'Motswana, Botswanan',
  },
  {
    code: '74',
    alphaCode2: 'BV',
    alphaCode3: 'BVT',
    country: 'Bouvet Island',
    nationality: 'Bouvet Island',
  },
  {
    code: '76',
    alphaCode2: 'BR',
    alphaCode3: 'BRA',
    country: 'Brazil',
    nationality: 'Brazilian',
  },
  {
    code: '86',
    alphaCode2: 'IO',
    alphaCode3: 'IOT',
    country: 'British Indian Ocean Territory',
    nationality: 'BIOT',
  },
  {
    code: '96',
    alphaCode2: 'BN',
    alphaCode3: 'BRN',
    country: 'Brunei Darussalam',
    nationality: 'Bruneian',
  },
  {
    code: '100',
    alphaCode2: 'BG',
    alphaCode3: 'BGR',
    country: 'Bulgaria',
    nationality: 'Bulgarian',
  },
  {
    code: '854',
    alphaCode2: 'BF',
    alphaCode3: 'BFA',
    country: 'Burkina Faso',
    nationality: 'Burkinab\u00e9',
  },
  {
    code: '108',
    alphaCode2: 'BI',
    alphaCode3: 'BDI',
    country: 'Burundi',
    nationality: 'Burundian',
  },
  {
    code: '132',
    alphaCode2: 'CV',
    alphaCode3: 'CPV',
    country: 'Cabo Verde',
    nationality: 'Cabo Verdean',
  },
  {
    code: '116',
    alphaCode2: 'KH',
    alphaCode3: 'KHM',
    country: 'Cambodia',
    nationality: 'Cambodian',
  },
  {
    code: '120',
    alphaCode2: 'CM',
    alphaCode3: 'CMR',
    country: 'Cameroon',
    nationality: 'Cameroonian',
  },
  {
    code: '124',
    alphaCode2: 'CA',
    alphaCode3: 'CAN',
    country: 'Canada',
    nationality: 'Canadian',
  },
  {
    code: '136',
    alphaCode2: 'KY',
    alphaCode3: 'CYM',
    country: 'Cayman Islands',
    nationality: 'Caymanian',
  },
  {
    code: '140',
    alphaCode2: 'CF',
    alphaCode3: 'CAF',
    country: 'Central African Republic',
    nationality: 'Central African',
  },
  {
    code: '148',
    alphaCode2: 'TD',
    alphaCode3: 'TCD',
    country: 'Chad',
    nationality: 'Chadian',
  },
  {
    code: '152',
    alphaCode2: 'CL',
    alphaCode3: 'CHL',
    country: 'Chile',
    nationality: 'Chilean',
  },
  {
    code: '156',
    alphaCode2: 'CN',
    alphaCode3: 'CHN',
    country: 'China',
    nationality: 'Chinese',
  },
  {
    code: '162',
    alphaCode2: 'CX',
    alphaCode3: 'CXR',
    country: 'Christmas Island',
    nationality: 'Christmas Island',
  },
  {
    code: '166',
    alphaCode2: 'CC',
    alphaCode3: 'CCK',
    country: 'Cocos (Keeling) Islands',
    nationality: 'Cocos Island',
  },
  {
    code: '170',
    alphaCode2: 'CO',
    alphaCode3: 'COL',
    country: 'Colombia',
    nationality: 'Colombian',
  },
  {
    code: '174',
    alphaCode2: 'KM',
    alphaCode3: 'COM',
    country: 'Comoros',
    nationality: 'Comoran, Comorian',
  },
  {
    code: '178',
    alphaCode2: 'CG',
    alphaCode3: 'COG',
    country: 'Congo (Republic of the)',
    nationality: 'Congolese',
  },
  {
    code: '180',
    alphaCode2: 'CD',
    alphaCode3: 'COD',
    country: 'Congo (Democratic Republic of the)',
    nationality: 'Congolese',
  },
  {
    code: '184',
    alphaCode2: 'CK',
    alphaCode3: 'COK',
    country: 'Cook Islands',
    nationality: 'Cook Island',
  },
  {
    code: '188',
    alphaCode2: 'CR',
    alphaCode3: 'CRI',
    country: 'Costa Rica',
    nationality: 'Costa Rican',
  },
  {
    code: '384',
    alphaCode2: 'CI',
    alphaCode3: 'CIV',
    country: 'C\u00f4te dIvoire',
    nationality: 'Ivorian',
  },
  {
    code: '191',
    alphaCode2: 'HR',
    alphaCode3: 'HRV',
    country: 'Croatia',
    nationality: 'Croatian',
  },
  {
    code: '192',
    alphaCode2: 'CU',
    alphaCode3: 'CUB',
    country: 'Cuba',
    nationality: 'Cuban',
  },
  {
    code: '531',
    alphaCode2: 'CW',
    alphaCode3: 'CUW',
    country: 'Cura\u00e7ao',
    nationality: 'Cura\u00e7aoan',
  },
  {
    code: '196',
    alphaCode2: 'CY',
    alphaCode3: 'CYP',
    country: 'Cyprus',
    nationality: 'Cypriot',
  },
  {
    code: '203',
    alphaCode2: 'CZ',
    alphaCode3: 'CZE',
    country: 'Czech Republic',
    nationality: 'Czech',
  },
  {
    code: '208',
    alphaCode2: 'DK',
    alphaCode3: 'DNK',
    country: 'Denmark',
    nationality: 'Danish',
  },
  {
    code: '262',
    alphaCode2: 'DJ',
    alphaCode3: 'DJI',
    country: 'Djibouti',
    nationality: 'Djiboutian',
  },
  {
    code: '212',
    alphaCode2: 'DM',
    alphaCode3: 'DMA',
    country: 'Dominica',
    nationality: 'Dominican',
  },
  {
    code: '214',
    alphaCode2: 'DO',
    alphaCode3: 'DOM',
    country: 'Dominican Republic',
    nationality: 'Dominican',
  },
  {
    code: '218',
    alphaCode2: 'EC',
    alphaCode3: 'ECU',
    country: 'Ecuador',
    nationality: 'Ecuadorian',
  },
  {
    code: '818',
    alphaCode2: 'EG',
    alphaCode3: 'EGY',
    country: 'Egypt',
    nationality: 'Egyptian',
  },
  {
    code: '222',
    alphaCode2: 'SV',
    alphaCode3: 'SLV',
    country: 'El Salvador',
    nationality: 'Salvadoran',
  },
  {
    code: '226',
    alphaCode2: 'GQ',
    alphaCode3: 'GNQ',
    country: 'Equatorial Guinea',
    nationality: 'Equatorial Guinean, Equatoguinean',
  },
  {
    code: '232',
    alphaCode2: 'ER',
    alphaCode3: 'ERI',
    country: 'Eritrea',
    nationality: 'Eritrean',
  },
  {
    code: '233',
    alphaCode2: 'EE',
    alphaCode3: 'EST',
    country: 'Estonia',
    nationality: 'Estonian',
  },
  {
    code: '231',
    alphaCode2: 'ET',
    alphaCode3: 'ETH',
    country: 'Ethiopia',
    nationality: 'Ethiopian',
  },
  {
    code: '238',
    alphaCode2: 'FK',
    alphaCode3: 'FLK',
    country: 'Falkland Islands (Malvinas)',
    nationality: 'Falkland Island',
  },
  {
    code: '234',
    alphaCode2: 'FO',
    alphaCode3: 'FRO',
    country: 'Faroe Islands',
    nationality: 'Faroese',
  },
  {
    code: '242',
    alphaCode2: 'FJ',
    alphaCode3: 'FJI',
    country: 'Fiji',
    nationality: 'Fijian',
  },
  {
    code: '246',
    alphaCode2: 'FI',
    alphaCode3: 'FIN',
    country: 'Finland',
    nationality: 'Finnish',
  },
  {
    code: '250',
    alphaCode2: 'FR',
    alphaCode3: 'FRA',
    country: 'France',
    nationality: 'French',
  },
  {
    code: '254',
    alphaCode2: 'GF',
    alphaCode3: 'GUF',
    country: 'French Guiana',
    nationality: 'French Guianese',
  },
  {
    code: '258',
    alphaCode2: 'PF',
    alphaCode3: 'PYF',
    country: 'French Polynesia',
    nationality: 'French Polynesian',
  },
  {
    code: '260',
    alphaCode2: 'TF',
    alphaCode3: 'ATF',
    country: 'French Southern Territories',
    nationality: 'French Southern Territories',
  },
  {
    code: '266',
    alphaCode2: 'GA',
    alphaCode3: 'GAB',
    country: 'Gabon',
    nationality: 'Gabonese',
  },
  {
    code: '270',
    alphaCode2: 'GM',
    alphaCode3: 'GMB',
    country: 'Gambia',
    nationality: 'Gambian',
  },
  {
    code: '268',
    alphaCode2: 'GE',
    alphaCode3: 'GEO',
    country: 'Georgia',
    nationality: 'Georgian',
  },
  {
    code: '276',
    alphaCode2: 'DE',
    alphaCode3: 'DEU',
    country: 'Germany',
    nationality: 'German',
  },
  {
    code: '288',
    alphaCode2: 'GH',
    alphaCode3: 'GHA',
    country: 'Ghana',
    nationality: 'Ghanaian',
  },
  {
    code: '292',
    alphaCode2: 'GI',
    alphaCode3: 'GIB',
    country: 'Gibraltar',
    nationality: 'Gibraltar',
  },
  {
    code: '300',
    alphaCode2: 'GR',
    alphaCode3: 'GRC',
    country: 'Greece',
    nationality: 'Greek, Hellenic',
  },
  {
    code: '304',
    alphaCode2: 'GL',
    alphaCode3: 'GRL',
    country: 'Greenland',
    nationality: 'Greenlandic',
  },
  {
    code: '308',
    alphaCode2: 'GD',
    alphaCode3: 'GRD',
    country: 'Grenada',
    nationality: 'Grenadian',
  },
  {
    code: '312',
    alphaCode2: 'GP',
    alphaCode3: 'GLP',
    country: 'Guadeloupe',
    nationality: 'Guadeloupe',
  },
  {
    code: '316',
    alphaCode2: 'GU',
    alphaCode3: 'GUM',
    country: 'Guam',
    nationality: 'Guamanian, Guambat',
  },
  {
    code: '320',
    alphaCode2: 'GT',
    alphaCode3: 'GTM',
    country: 'Guatemala',
    nationality: 'Guatemalan',
  },
  {
    code: '831',
    alphaCode2: 'GG',
    alphaCode3: 'GGY',
    country: 'Guernsey',
    nationality: 'Channel Island',
  },
  {
    code: '324',
    alphaCode2: 'GN',
    alphaCode3: 'GIN',
    country: 'Guinea',
    nationality: 'Guinean',
  },
  {
    code: '624',
    alphaCode2: 'GW',
    alphaCode3: 'GNB',
    country: 'Guinea-Bissau',
    nationality: 'Bissau-Guinean',
  },
  {
    code: '328',
    alphaCode2: 'GY',
    alphaCode3: 'GUY',
    country: 'Guyana',
    nationality: 'Guyanese',
  },
  {
    code: '332',
    alphaCode2: 'HT',
    alphaCode3: 'HTI',
    country: 'Haiti',
    nationality: 'Haitian',
  },
  {
    code: '334',
    alphaCode2: 'HM',
    alphaCode3: 'HMD',
    country: 'Heard Island and McDonald Islands',
    nationality: 'Heard Island or McDonald Islands',
  },
  {
    code: '336',
    alphaCode2: 'VA',
    alphaCode3: 'VAT',
    country: 'Vatican City State',
    nationality: 'Vatican',
  },
  {
    code: '340',
    alphaCode2: 'HN',
    alphaCode3: 'HND',
    country: 'Honduras',
    nationality: 'Honduran',
  },
  {
    code: '344',
    alphaCode2: 'HK',
    alphaCode3: 'HKG',
    country: 'Hong Kong',
    nationality: 'Hong Kong, Hong Kongese',
  },
  {
    code: '348',
    alphaCode2: 'HU',
    alphaCode3: 'HUN',
    country: 'Hungary',
    nationality: 'Hungarian, Magyar',
  },
  {
    code: '352',
    alphaCode2: 'IS',
    alphaCode3: 'ISL',
    country: 'Iceland',
    nationality: 'Icelandic',
  },
  {
    code: '356',
    alphaCode2: 'IN',
    alphaCode3: 'IND',
    country: 'India',
    nationality: 'Indian',
  },
  {
    code: '360',
    alphaCode2: 'ID',
    alphaCode3: 'IDN',
    country: 'Indonesia',
    nationality: 'Indonesian',
  },
  {
    code: '364',
    alphaCode2: 'IR',
    alphaCode3: 'IRN',
    country: 'Iran',
    nationality: 'Iranian, Persian',
  },
  {
    code: '368',
    alphaCode2: 'IQ',
    alphaCode3: 'IRQ',
    country: 'Iraq',
    nationality: 'Iraqi',
  },
  {
    code: '372',
    alphaCode2: 'IE',
    alphaCode3: 'IRL',
    country: 'Ireland',
    nationality: 'Irish',
  },
  {
    code: '833',
    alphaCode2: 'IM',
    alphaCode3: 'IMN',
    country: 'Isle of Man',
    nationality: 'Manx',
  },
  {
    code: '376',
    alphaCode2: 'IL',
    alphaCode3: 'ISR',
    country: 'Israel',
    nationality: 'Israeli',
  },
  {
    code: '380',
    alphaCode2: 'IT',
    alphaCode3: 'ITA',
    country: 'Italy',
    nationality: 'Italian',
  },
  {
    code: '388',
    alphaCode2: 'JM',
    alphaCode3: 'JAM',
    country: 'Jamaica',
    nationality: 'Jamaican',
  },
  {
    code: '392',
    alphaCode2: 'JP',
    alphaCode3: 'JPN',
    country: 'Japan',
    nationality: 'Japanese',
  },
  {
    code: '832',
    alphaCode2: 'JE',
    alphaCode3: 'JEY',
    country: 'Jersey',
    nationality: 'Channel Island',
  },
  {
    code: '400',
    alphaCode2: 'JO',
    alphaCode3: 'JOR',
    country: 'Jordan',
    nationality: 'Jordanian',
  },
  {
    code: '398',
    alphaCode2: 'KZ',
    alphaCode3: 'KAZ',
    country: 'Kazakhstan',
    nationality: 'Kazakhstani, Kazakh',
  },
  {
    code: '404',
    alphaCode2: 'KE',
    alphaCode3: 'KEN',
    country: 'Kenya',
    nationality: 'Kenyan',
  },
  {
    code: '296',
    alphaCode2: 'KI',
    alphaCode3: 'KIR',
    country: 'Kiribati',
    nationality: 'I-Kiribati',
  },
  {
    code: '408',
    alphaCode2: 'KP',
    alphaCode3: 'PRK',
    country: 'Korea (Democratic Peoples Republic of)',
    nationality: 'North Korean',
  },
  {
    code: '410',
    alphaCode2: 'KR',
    alphaCode3: 'KOR',
    country: 'Korea (Republic of)',
    nationality: 'South Korean',
  },
  {
    code: '414',
    alphaCode2: 'KW',
    alphaCode3: 'KWT',
    country: 'Kuwait',
    nationality: 'Kuwaiti',
  },
  {
    code: '417',
    alphaCode2: 'KG',
    alphaCode3: 'KGZ',
    country: 'Kyrgyzstan',
    nationality: 'Kyrgyzstani, Kyrgyz, Kirgiz, Kirghiz',
  },
  {
    code: '418',
    alphaCode2: 'LA',
    alphaCode3: 'LAO',
    country: 'Lao Peoples Democratic Republic',
    nationality: 'Lao, Laotian',
  },
  {
    code: '428',
    alphaCode2: 'LV',
    alphaCode3: 'LVA',
    country: 'Latvia',
    nationality: 'Latvian',
  },
  {
    code: '422',
    alphaCode2: 'LB',
    alphaCode3: 'LBN',
    country: 'Lebanon',
    nationality: 'Lebanese',
  },
  {
    code: '426',
    alphaCode2: 'LS',
    alphaCode3: 'LSO',
    country: 'Lesotho',
    nationality: 'Basotho',
  },
  {
    code: '430',
    alphaCode2: 'LR',
    alphaCode3: 'LBR',
    country: 'Liberia',
    nationality: 'Liberian',
  },
  {
    code: '434',
    alphaCode2: 'LY',
    alphaCode3: 'LBY',
    country: 'Libya',
    nationality: 'Libyan',
  },
  {
    code: '438',
    alphaCode2: 'LI',
    alphaCode3: 'LIE',
    country: 'Liechtenstein',
    nationality: 'Liechtenstein',
  },
  {
    code: '440',
    alphaCode2: 'LT',
    alphaCode3: 'LTU',
    country: 'Lithuania',
    nationality: 'Lithuanian',
  },
  {
    code: '442',
    alphaCode2: 'LU',
    alphaCode3: 'LUX',
    country: 'Luxembourg',
    nationality: 'Luxembourg, Luxembourgish',
  },
  {
    code: '446',
    alphaCode2: 'MO',
    alphaCode3: 'MAC',
    country: 'Macao',
    nationality: 'Macanese, Chinese',
  },
  {
    code: '807',
    alphaCode2: 'MK',
    alphaCode3: 'MKD',
    country: 'Macedonia (the former Yugoslav Republic of)',
    nationality: 'Macedonian',
  },
  {
    code: '450',
    alphaCode2: 'MG',
    alphaCode3: 'MDG',
    country: 'Madagascar',
    nationality: 'Malagasy',
  },
  {
    code: '454',
    alphaCode2: 'MW',
    alphaCode3: 'MWI',
    country: 'Malawi',
    nationality: 'Malawian',
  },
  {
    code: '458',
    alphaCode2: 'MY',
    alphaCode3: 'MYS',
    country: 'Malaysia',
    nationality: 'Malaysian',
  },
  {
    code: '462',
    alphaCode2: 'MV',
    alphaCode3: 'MDV',
    country: 'Maldives',
    nationality: 'Maldivian',
  },
  {
    code: '466',
    alphaCode2: 'ML',
    alphaCode3: 'MLI',
    country: 'Mali',
    nationality: 'Malian, Malinese',
  },
  {
    code: '470',
    alphaCode2: 'MT',
    alphaCode3: 'MLT',
    country: 'Malta',
    nationality: 'Maltese',
  },
  {
    code: '584',
    alphaCode2: 'MH',
    alphaCode3: 'MHL',
    country: 'Marshall Islands',
    nationality: 'Marshallese',
  },
  {
    code: '474',
    alphaCode2: 'MQ',
    alphaCode3: 'MTQ',
    country: 'Martinique',
    nationality: 'Martiniquais, Martinican',
  },
  {
    code: '478',
    alphaCode2: 'MR',
    alphaCode3: 'MRT',
    country: 'Mauritania',
    nationality: 'Mauritanian',
  },
  {
    code: '480',
    alphaCode2: 'MU',
    alphaCode3: 'MUS',
    country: 'Mauritius',
    nationality: 'Mauritian',
  },
  {
    code: '175',
    alphaCode2: 'YT',
    alphaCode3: 'MYT',
    country: 'Mayotte',
    nationality: 'Mahoran',
  },
  {
    code: '484',
    alphaCode2: 'MX',
    alphaCode3: 'MEX',
    country: 'Mexico',
    nationality: 'Mexican',
  },
  {
    code: '583',
    alphaCode2: 'FM',
    alphaCode3: 'FSM',
    country: 'Micronesia (Federated States of)',
    nationality: 'Micronesian',
  },
  {
    code: '498',
    alphaCode2: 'MD',
    alphaCode3: 'MDA',
    country: 'Moldova (Republic of)',
    nationality: 'Moldovan',
  },
  {
    code: '492',
    alphaCode2: 'MC',
    alphaCode3: 'MCO',
    country: 'Monaco',
    nationality: 'Mon\u00e9gasque, Monacan',
  },
  {
    code: '496',
    alphaCode2: 'MN',
    alphaCode3: 'MNG',
    country: 'Mongolia',
    nationality: 'Mongolian',
  },
  {
    code: '499',
    alphaCode2: 'ME',
    alphaCode3: 'MNE',
    country: 'Montenegro',
    nationality: 'Montenegrin',
  },
  {
    code: '500',
    alphaCode2: 'MS',
    alphaCode3: 'MSR',
    country: 'Montserrat',
    nationality: 'Montserratian',
  },
  {
    code: '504',
    alphaCode2: 'MA',
    alphaCode3: 'MAR',
    country: 'Morocco',
    nationality: 'Moroccan',
  },
  {
    code: '508',
    alphaCode2: 'MZ',
    alphaCode3: 'MOZ',
    country: 'Mozambique',
    nationality: 'Mozambican',
  },
  {
    code: '104',
    alphaCode2: 'MM',
    alphaCode3: 'MMR',
    country: 'Myanmar',
    nationality: 'Burmese',
  },
  {
    code: '516',
    alphaCode2: 'NA',
    alphaCode3: 'NAM',
    country: 'Namibia',
    nationality: 'Namibian',
  },
  {
    code: '520',
    alphaCode2: 'NR',
    alphaCode3: 'NRU',
    country: 'Nauru',
    nationality: 'Nauruan',
  },
  {
    code: '524',
    alphaCode2: 'NP',
    alphaCode3: 'NPL',
    country: 'Nepal',
    nationality: 'Nepali, Nepalese',
  },
  {
    code: '528',
    alphaCode2: 'NL',
    alphaCode3: 'NLD',
    country: 'Netherlands',
    nationality: 'Dutch, Netherlandic',
  },
  {
    code: '540',
    alphaCode2: 'NC',
    alphaCode3: 'NCL',
    country: 'New Caledonia',
    nationality: 'New Caledonian',
  },
  {
    code: '554',
    alphaCode2: 'NZ',
    alphaCode3: 'NZL',
    country: 'New Zealand',
    nationality: 'New Zealand, NZ',
  },
  {
    code: '558',
    alphaCode2: 'NI',
    alphaCode3: 'NIC',
    country: 'Nicaragua',
    nationality: 'Nicaraguan',
  },
  {
    code: '562',
    alphaCode2: 'NE',
    alphaCode3: 'NER',
    country: 'Niger',
    nationality: 'Nigerien',
  },
  {
    code: '566',
    alphaCode2: 'NG',
    alphaCode3: 'NGA',
    country: 'Nigeria',
    nationality: 'Nigerian',
  },
  {
    code: '570',
    alphaCode2: 'NU',
    alphaCode3: 'NIU',
    country: 'Niue',
    nationality: 'Niuean',
  },
  {
    code: '574',
    alphaCode2: 'NF',
    alphaCode3: 'NFK',
    country: 'Norfolk Island',
    nationality: 'Norfolk Island',
  },
  {
    code: '580',
    alphaCode2: 'MP',
    alphaCode3: 'MNP',
    country: 'Northern Mariana Islands',
    nationality: 'Northern Marianan',
  },
  {
    code: '578',
    alphaCode2: 'NO',
    alphaCode3: 'NOR',
    country: 'Norway',
    nationality: 'Norwegian',
  },
  {
    code: '512',
    alphaCode2: 'OM',
    alphaCode3: 'OMN',
    country: 'Oman',
    nationality: 'Omani',
  },
  {
    code: '586',
    alphaCode2: 'PK',
    alphaCode3: 'PAK',
    country: 'Pakistan',
    nationality: 'Pakistani',
  },
  {
    code: '585',
    alphaCode2: 'PW',
    alphaCode3: 'PLW',
    country: 'Palau',
    nationality: 'Palauan',
  },
  {
    code: '275',
    alphaCode2: 'PS',
    alphaCode3: 'PSE',
    country: 'Palestine, State of',
    nationality: 'Palestinian',
  },
  {
    code: '591',
    alphaCode2: 'PA',
    alphaCode3: 'PAN',
    country: 'Panama',
    nationality: 'Panamanian',
  },
  {
    code: '598',
    alphaCode2: 'PG',
    alphaCode3: 'PNG',
    country: 'Papua New Guinea',
    nationality: 'Papua New Guinean, Papuan',
  },
  {
    code: '600',
    alphaCode2: 'PY',
    alphaCode3: 'PRY',
    country: 'Paraguay',
    nationality: 'Paraguayan',
  },
  {
    code: '604',
    alphaCode2: 'PE',
    alphaCode3: 'PER',
    country: 'Peru',
    nationality: 'Peruvian',
  },
  {
    code: '608',
    alphaCode2: 'PH',
    alphaCode3: 'PHL',
    country: 'Philippines',
    nationality: 'Philippine, Filipino',
  },
  {
    code: '612',
    alphaCode2: 'PN',
    alphaCode3: 'PCN',
    country: 'Pitcairn',
    nationality: 'Pitcairn Island',
  },
  {
    code: '616',
    alphaCode2: 'PL',
    alphaCode3: 'POL',
    country: 'Poland',
    nationality: 'Polish',
  },
  {
    code: '620',
    alphaCode2: 'PT',
    alphaCode3: 'PRT',
    country: 'Portugal',
    nationality: 'Portuguese',
  },
  {
    code: '630',
    alphaCode2: 'PR',
    alphaCode3: 'PRI',
    country: 'Puerto Rico',
    nationality: 'Puerto Rican',
  },
  {
    code: '634',
    alphaCode2: 'QA',
    alphaCode3: 'QAT',
    country: 'Qatar',
    nationality: 'Qatari',
  },
  {
    code: '638',
    alphaCode2: 'RE',
    alphaCode3: 'REU',
    country: 'R\u00e9union',
    nationality: 'R\u00e9unionese, R\u00e9unionnais',
  },
  {
    code: '642',
    alphaCode2: 'RO',
    alphaCode3: 'ROU',
    country: 'Romania',
    nationality: 'Romanian',
  },
  {
    code: '643',
    alphaCode2: 'RU',
    alphaCode3: 'RUS',
    country: 'Russian Federation',
    nationality: 'Russian',
  },
  {
    code: '646',
    alphaCode2: 'RW',
    alphaCode3: 'RWA',
    country: 'Rwanda',
    nationality: 'Rwandan',
  },
  {
    code: '652',
    alphaCode2: 'BL',
    alphaCode3: 'BLM',
    country: 'Saint Barth\u00e9lemy',
    nationality: 'Barth\u00e9lemois',
  },
  {
    code: '654',
    alphaCode2: 'SH',
    alphaCode3: 'SHN',
    country: 'Saint Helena, Ascension and Tristan da Cunha',
    nationality: 'Saint Helenian',
  },
  {
    code: '659',
    alphaCode2: 'KN',
    alphaCode3: 'KNA',
    country: 'Saint Kitts and Nevis',
    nationality: 'Kittitian or Nevisian',
  },
  {
    code: '662',
    alphaCode2: 'LC',
    alphaCode3: 'LCA',
    country: 'Saint Lucia',
    nationality: 'Saint Lucian',
  },
  {
    code: '663',
    alphaCode2: 'MF',
    alphaCode3: 'MAF',
    country: 'Saint Martin (French part)',
    nationality: 'Saint-Martinoise',
  },
  {
    code: '666',
    alphaCode2: 'PM',
    alphaCode3: 'SPM',
    country: 'Saint Pierre and Miquelon',
    nationality: 'Saint-Pierrais or Miquelonnais',
  },
  {
    code: '670',
    alphaCode2: 'VC',
    alphaCode3: 'VCT',
    country: 'Saint Vincent and the Grenadines',
    nationality: 'Saint Vincentian, Vincentian',
  },
  {
    code: '882',
    alphaCode2: 'WS',
    alphaCode3: 'WSM',
    country: 'Samoa',
    nationality: 'Samoan',
  },
  {
    code: '674',
    alphaCode2: 'SM',
    alphaCode3: 'SMR',
    country: 'San Marino',
    nationality: 'Sammarinese',
  },
  {
    code: '678',
    alphaCode2: 'ST',
    alphaCode3: 'STP',
    country: 'Sao Tome and Principe',
    nationality: 'S\u00e3o Tom\u00e9an',
  },
  {
    code: '682',
    alphaCode2: 'SA',
    alphaCode3: 'SAU',
    country: 'Saudi Arabia',
    nationality: 'Saudi, Saudi Arabian',
  },
  {
    code: '686',
    alphaCode2: 'SN',
    alphaCode3: 'SEN',
    country: 'Senegal',
    nationality: 'Senegalese',
  },
  {
    code: '688',
    alphaCode2: 'RS',
    alphaCode3: 'SRB',
    country: 'Serbia',
    nationality: 'Serbian',
  },
  {
    code: '690',
    alphaCode2: 'SC',
    alphaCode3: 'SYC',
    country: 'Seychelles',
    nationality: 'Seychellois',
  },
  {
    code: '694',
    alphaCode2: 'SL',
    alphaCode3: 'SLE',
    country: 'Sierra Leone',
    nationality: 'Sierra Leonean',
  },
  {
    code: '702',
    alphaCode2: 'SG',
    alphaCode3: 'SGP',
    country: 'Singapore',
    nationality: 'Singaporean',
  },
  {
    code: '534',
    alphaCode2: 'SX',
    alphaCode3: 'SXM',
    country: 'Sint Maarten (Dutch part)',
    nationality: 'Sint Maarten',
  },
  {
    code: '703',
    alphaCode2: 'SK',
    alphaCode3: 'SVK',
    country: 'Slovakia',
    nationality: 'Slovak',
  },
  {
    code: '705',
    alphaCode2: 'SI',
    alphaCode3: 'SVN',
    country: 'Slovenia',
    nationality: 'Slovenian, Slovene',
  },
  {
    code: '90',
    alphaCode2: 'SB',
    alphaCode3: 'SLB',
    country: 'Solomon Islands',
    nationality: 'Solomon Island',
  },
  {
    code: '706',
    alphaCode2: 'SO',
    alphaCode3: 'SOM',
    country: 'Somalia',
    nationality: 'Somali, Somalian',
  },
  {
    code: '710',
    alphaCode2: 'ZA',
    alphaCode3: 'ZAF',
    country: 'South Africa',
    nationality: 'South African',
  },
  {
    code: '239',
    alphaCode2: 'GS',
    alphaCode3: 'SGS',
    country: 'South Georgia and the South Sandwich Islands',
    nationality: 'South Georgia or South Sandwich Islands',
  },
  {
    code: '728',
    alphaCode2: 'SS',
    alphaCode3: 'SSD',
    country: 'South Sudan',
    nationality: 'South Sudanese',
  },
  {
    code: '724',
    alphaCode2: 'ES',
    alphaCode3: 'ESP',
    country: 'Spain',
    nationality: 'Spanish',
  },
  {
    code: '144',
    alphaCode2: 'LK',
    alphaCode3: 'LKA',
    country: 'Sri Lanka',
    nationality: 'Sri Lankan',
  },
  {
    code: '729',
    alphaCode2: 'SD',
    alphaCode3: 'SDN',
    country: 'Sudan',
    nationality: 'Sudanese',
  },
  {
    code: '740',
    alphaCode2: 'SR',
    alphaCode3: 'SUR',
    country: 'Suriname',
    nationality: 'Surinamese',
  },
  {
    code: '744',
    alphaCode2: 'SJ',
    alphaCode3: 'SJM',
    country: 'Svalbard and Jan Mayen',
    nationality: 'Svalbard',
  },
  {
    code: '748',
    alphaCode2: 'SZ',
    alphaCode3: 'SWZ',
    country: 'Swaziland',
    nationality: 'Swazi',
  },
  {
    code: '752',
    alphaCode2: 'SE',
    alphaCode3: 'SWE',
    country: 'Sweden',
    nationality: 'Swedish',
  },
  {
    code: '756',
    alphaCode2: 'CH',
    alphaCode3: 'CHE',
    country: 'Switzerland',
    nationality: 'Swiss',
  },
  {
    code: '760',
    alphaCode2: 'SY',
    alphaCode3: 'SYR',
    country: 'Syrian Arab Republic',
    nationality: 'Syrian',
  },
  {
    code: '158',
    alphaCode2: 'TW',
    alphaCode3: 'TWN',
    country: 'Taiwan, Province of China',
    nationality: 'Chinese, Taiwanese',
  },
  {
    code: '762',
    alphaCode2: 'TJ',
    alphaCode3: 'TJK',
    country: 'Tajikistan',
    nationality: 'Tajikistani',
  },
  {
    code: '834',
    alphaCode2: 'TZ',
    alphaCode3: 'TZA',
    country: 'Tanzania, United Republic of',
    nationality: 'Tanzanian',
  },
  {
    code: '764',
    alphaCode2: 'TH',
    alphaCode3: 'THA',
    country: 'Thailand',
    nationality: 'Thai',
  },
  {
    code: '626',
    alphaCode2: 'TL',
    alphaCode3: 'TLS',
    country: 'Timor-Leste',
    nationality: 'Timorese',
  },
  {
    code: '768',
    alphaCode2: 'TG',
    alphaCode3: 'TGO',
    country: 'Togo',
    nationality: 'Togolese',
  },
  {
    code: '772',
    alphaCode2: 'TK',
    alphaCode3: 'TKL',
    country: 'Tokelau',
    nationality: 'Tokelauan',
  },
  {
    code: '776',
    alphaCode2: 'TO',
    alphaCode3: 'TON',
    country: 'Tonga',
    nationality: 'Tongan',
  },
  {
    code: '780',
    alphaCode2: 'TT',
    alphaCode3: 'TTO',
    country: 'Trinidad and Tobago',
    nationality: 'Trinidadian or Tobagonian',
  },
  {
    code: '788',
    alphaCode2: 'TN',
    alphaCode3: 'TUN',
    country: 'Tunisia',
    nationality: 'Tunisian',
  },
  {
    code: '792',
    alphaCode2: 'TR',
    alphaCode3: 'TUR',
    country: 'Turkey',
    nationality: 'Turkish',
  },
  {
    code: '795',
    alphaCode2: 'TM',
    alphaCode3: 'TKM',
    country: 'Turkmenistan',
    nationality: 'Turkmen',
  },
  {
    code: '796',
    alphaCode2: 'TC',
    alphaCode3: 'TCA',
    country: 'Turks and Caicos Islands',
    nationality: 'Turks and Caicos Island',
  },
  {
    code: '798',
    alphaCode2: 'TV',
    alphaCode3: 'TUV',
    country: 'Tuvalu',
    nationality: 'Tuvaluan',
  },
  {
    code: '800',
    alphaCode2: 'UG',
    alphaCode3: 'UGA',
    country: 'Uganda',
    nationality: 'Ugandan',
  },
  {
    code: '804',
    alphaCode2: 'UA',
    alphaCode3: 'UKR',
    country: 'Ukraine',
    nationality: 'Ukrainian',
  },
  {
    code: '784',
    alphaCode2: 'AE',
    alphaCode3: 'ARE',
    country: 'United Arab Emirates',
    nationality: 'Emirati, Emirian, Emiri',
  },
  {
    code: '826',
    alphaCode2: 'GB',
    alphaCode3: 'GBR',
    country: 'United Kingdom of Great Britain and Northern Ireland',
    nationality: 'British, UK',
  },
  {
    code: '581',
    alphaCode2: 'UM',
    alphaCode3: 'UMI',
    country: 'United States Minor Outlying Islands',
    nationality: 'American',
  },
  {
    code: '840',
    alphaCode2: 'US',
    alphaCode3: 'USA',
    country: 'United States of America',
    nationality: 'American',
  },
  {
    code: '858',
    alphaCode2: 'UY',
    alphaCode3: 'URY',
    country: 'Uruguay',
    nationality: 'Uruguayan',
  },
  {
    code: '860',
    alphaCode2: 'UZ',
    alphaCode3: 'UZB',
    country: 'Uzbekistan',
    nationality: 'Uzbekistani, Uzbek',
  },
  {
    code: '548',
    alphaCode2: 'VU',
    alphaCode3: 'VUT',
    country: 'Vanuatu',
    nationality: 'Ni-Vanuatu, Vanuatuan',
  },
  {
    code: '862',
    alphaCode2: 'VE',
    alphaCode3: 'VEN',
    country: 'Venezuela (Bolivarian Republic of)',
    nationality: 'Venezuelan',
  },
  {
    code: '704',
    alphaCode2: 'VN',
    alphaCode3: 'VNM',
    country: 'Vietnam',
    nationality: 'Vietnamese',
  },
  {
    code: '92',
    alphaCode2: 'VG',
    alphaCode3: 'VGB',
    country: 'Virgin Islands (British)',
    nationality: 'British Virgin Island',
  },
  {
    code: '850',
    alphaCode2: 'VI',
    alphaCode3: 'VIR',
    country: 'Virgin Islands (U.S.)',
    nationality: 'U.S. Virgin Island',
  },
  {
    code: '876',
    alphaCode2: 'WF',
    alphaCode3: 'WLF',
    country: 'Wallis and Futuna',
    nationality: 'Wallis and Futuna, Wallisian or Futunan',
  },
  {
    code: '732',
    alphaCode2: 'EH',
    alphaCode3: 'ESH',
    country: 'Western Sahara',
    nationality: 'Sahrawi, Sahrawian, Sahraouian',
  },
  {
    code: '887',
    alphaCode2: 'YE',
    alphaCode3: 'YEM',
    country: 'Yemen',
    nationality: 'Yemeni',
  },
  {
    code: '894',
    alphaCode2: 'ZM',
    alphaCode3: 'ZMB',
    country: 'Zambia',
    nationality: 'Zambian',
  },
  {
    code: '716',
    alphaCode2: 'ZW',
    alphaCode3: 'ZWE',
    country: 'Zimbabwe',
    nationality: 'Zimbabwean',
  },
];
