// library
const express = require('express');
const routes = express.Router();

// middleware
const { verifyToken, authAccount } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const feedbackController = require('../controllers/feedback.controller');

// Validator
const validator = require('../validators/feedback.validator');

/** Create Feedback */
routes.post(
  '',
  verifyToken,
  authAccount,
  validator.feedbackValidationRule(),
  validate,
  feedbackController.createFeedback
);

/** Get All Feedback */
routes.get('', verifyToken, authAccount, validate, feedbackController.getAllFeedback);

module.exports = routes;
