const Likelihood = require('../models/likelihood.model');

const replacementMap = {
  Rare: 'Improbable',
  Possible: 'Remote',
  Likely: 'Occasional',
  'Almost certain': 'Probable',
};

const desiredLikelihoods = [
  { title: 'Improbable', color: '029E3B', likelihoodValue: 1 },
  { title: 'Remote', color: 'FFFF00', likelihoodValue: 2 },
  { title: 'Occasional', color: 'FFA500', likelihoodValue: 3 },
  { title: 'Probable', color: '944E4E', likelihoodValue: 4 },
  { title: 'Frequent', color: '570606', likelihoodValue: 5 },
];

/**
 * Prepare and update the likelihood data in collection
 *
 * @returns
 */
exports.up = async () => {
  console.log('Starting likelihood collection update...');

  // Track changes for reporting
  const changes = {
    updated: 0,
    created: 0,
  };

  const existingData = await Likelihood.find();
  console.log(`Found ${existingData.length} existing likelihoods in db`);

  for (const likelihood of existingData) {
    const oldTitle = likelihood.title;

    if (replacementMap[oldTitle]) {
      const newTitle = replacementMap[oldTitle];

      const newDataInfo = desiredLikelihoods.find(data => data.title === newTitle);

      if (newDataInfo) {
        await Likelihood.findByIdAndUpdate(likelihood._id, {
          title: newTitle,
          color: newDataInfo.color,
          likelihoodValue: newDataInfo.likelihoodValue,
        });

        changes.updated++;
        console.log(`Updated record with ID ${likelihood._id} from ${oldTitle} to ${newTitle}`);
      }
    }
  }

  const updatedData = await Likelihood.find();
  const existingTitles = updatedData.map(data => data.title);

  for (const desired of desiredLikelihoods) {
    if (!existingTitles.includes(desired.title)) {
      await Likelihood.create({
        title: desired.title,
        color: desired.color,
        likelihoodValue: desired.likelihoodValue,
      });

      changes.created++;
      console.log(`Created new likelihood: ${desired.title}`);
    }
  }

  console.log(
    `Likelihood collection update completed: ${changes.updated} records updated, ${changes.created} new likelihoods created.`
  );

  return changes;
};
