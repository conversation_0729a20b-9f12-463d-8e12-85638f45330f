const express = require('express');
const routes = express.Router();

const { validate } = require('../middlewares/validate.middleware');
const userCertificateController = require('../controllers/user-certificate.controller');

// middleware
const { verifyToken } = require('../middlewares/auth.middleware');

// Update file info
routes.patch(
  '/:id/:fileType/:fileId',
  verifyToken,
  validate,
  userCertificateController.updateDocumentData
);

module.exports = routes;
