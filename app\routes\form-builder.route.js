// library
const express = require('express');
const routes = express.Router();

// middleware
const { verifyToken, authAccount } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');
const validator = require('../validators/form-builder.validator');

// controller
const formbuilderController = require('../controllers/form-builder.controller');

routes.post(
  '',
  verifyToken,
  authAccount,
  validate,
  validator.formbuilderValidationRule(),
  formbuilderController.create
);
routes.get('/:cardtype', verifyToken, authAccount, validate, formbuilderController.list);
routes.patch('/:id', verifyToken, validate, formbuilderController.updateQuestion);
routes.delete('/:id', verifyToken, validate, formbuilderController.deleteQuestion);

module.exports = routes;
