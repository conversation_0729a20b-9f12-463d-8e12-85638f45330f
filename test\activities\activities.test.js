const request = require('supertest');
const app = require('../../app/server');

// create activities
describe('POST /api/activities', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.I513fTkTfDgrekZVxyysoPEtU5Rmtr3nM-7HB7Hj7hE';
  const activitiesData = {
    name: 'Activity 3',
    scopeId: '64182d6daf85666cf96d18dd',
    weight: '10',
    project: '64119ddc6c1d88fde480fd47',
  };

  it('returns 200 and message Activity has been created successfully', async () => {
    const response = await request(app)
      .post('/api/activities')
      .set('Authorization', `Bearer ${token}`)
      .send(activitiesData);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Activity has been created successfully',
      status: true,
    });
  });

  it('returns 422 with message Required field cannot be empty.', async () => {
    const response = await request(app)
      .post('/api/activities')
      .set('Authorization', `Bearer ${token}`)
      .send(activitiesData);
    expect(response.status).toBe(422);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Required field cannot be empty.',
      status: false,
    });
  });

  it('returns 404 with message Activity already exist', async () => {
    const response = await request(app)
      .post('/api/activities')
      .set('Authorization', `Bearer ${token}`)
      .send(activitiesData);
    expect(response.status).toBe(404);
    expect(response.body).toEqual({
      message: 'Activity already exist',
      status: false,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).post('/api/activities').send(activitiesData);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});

// getAll activities
describe('GET /api/activities', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.I513fTkTfDgrekZVxyysoPEtU5Rmtr3nM-7HB7Hj7hE';

  it('returns 200 and message Activity has been retireved successfully', async () => {
    const response = await request(app)
      .get('/api/activities')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: [{}, {}],
      message: 'Activity has been retireved successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).get('/api/activities');
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});

//  update activities
describe('PATCH /api/activities/:projectId', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.I513fTkTfDgrekZVxyysoPEtU5Rmtr3nM-7HB7Hj7hE';
  const id = '641833ed4555d8f51f130542';
  const updateData = {
    name: 'Activity 4',
    scopeId: '64182d6daf85666cf96d18dd',
    weight: '50',
    project: '64119ddc6c1d88fde480fd47',
  };
  it('returns 200 and message Activity has been updated successfully', async () => {
    const response = await request(app)
      .patch(`/api/activities/${id}`)
      .set('Authorization', `Bearer ${token}`)
      .send(updateData);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Activity has been updated successfully',
      status: true,
    });
  });

  it('returns 422 with message Required field cannot be empty.', async () => {
    const response = await request(app)
      .post(`/api/activities/${id}`)
      .set('Authorization', `Bearer ${token}`)
      .send();
    expect(response.status).toBe(422);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Required field cannot be empty.',
      status: false,
    });
  });

  it('returns 404 with message No Activity Found', async () => {
    const response = await request(app)
      .post('/api/activities/6412a388268e9b25cef57a30')
      .set('Authorization', `Bearer ${token}`)
      .send(updateData);
    expect(response.status).toBe(404);
    expect(response.body).toMatchObject({
      message: 'No Activity Found',
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).patch(`/api/activities/${id}`).send(updateData);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});

//  Delete activitiesById
describe('DELETE /api/activities/:id', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.I513fTkTfDgrekZVxyysoPEtU5Rmtr3nM-7HB7Hj7hE';
  const id = '64183563885ac9224f29b156';

  it('returns 200 and message Activity has been deleted successfully', async () => {
    const response = await request(app)
      .delete(`/api/activities/${id}`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Activity has been deleted successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).delete(`/api/activities/${id}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });

  it('returns 404 with message No Activity Found', async () => {
    const response = await request(app)
      .delete('/api/activities/6412b7a6644aa8e810eb73fb')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(404);
    expect(response.body).toEqual({
      message: 'No Activity Found',
      status: false,
    });
  });
});

// get list of activity by project
describe('GET /api/activities/list/:projectId', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.I513fTkTfDgrekZVxyysoPEtU5Rmtr3nM-7HB7Hj7hE';
  const projectId = '64119ddc6c1d88fde480fd47';

  it('returns 200 and message Activity has been retireved successfully', async () => {
    const response = await request(app)
      .get(`/api/activities/list/${projectId}`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Activity has been retireved successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).get(`/api/activities/list/${projectId}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });

  it('returns 404 with message No Activity Found', async () => {
    const response = await request(app)
      .get('/api/activities/list/64119ddc6c1d88fde480fd40')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(404);
    expect(response.body).toEqual({
      message: 'No Activity Found',
      status: false,
    });
  });
});
