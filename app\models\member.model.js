const mongoose = require('mongoose');

const Member = new mongoose.Schema(
  {
    project: {
      type: mongoose.Types.ObjectId,
      ref: 'project',
    },
    function: {
      type: mongoose.Types.ObjectId,
      ref: 'function',
      default: null,
    },
    user: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    isApprover: {
      type: Boolean,
      default: false,
    },
    showOnDpr: {
      type: Boolean,
      default: false,
    },
    rotation: {
      type: String,
      enum: ['R1', 'R2'],
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

Member.index({ _id: 1, user: 1, project: 1 });
Member.index({ account: 1, deletedAt: 1 });
Member.index({ account: 1, user: 1, deletedAt: 1 });
Member.index({ project: 1, function: 1, deletedAt: 1 });
Member.index({ user: 1, rotation: 1 });

module.exports = mongoose.model('member', Member);
