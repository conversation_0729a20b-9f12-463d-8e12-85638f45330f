const mongoose = require('mongoose');

const HSCode = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
    },
    code: {
      type: String,
      required: true,
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    isTemporary: {
      type: Boolean,
      default: false,
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

HSCode.index({ account: 1, deletedAt: 1 });
HSCode.index({ account: 1, deletedAt: 1, isActive: 1 });
HSCode.index({ name: 1 });
HSCode.index({ code: 1 });

module.exports = mongoose.model('hs-code', HSCode);
