const { body, constantUtils } = require('../validators/parent.validator');

exports.createEquipmentQuantityTypeValidationRule = () => {
  return [
    body('name').isString().notEmpty().withMessage(constantUtils.EQUIPMENT_CATEGORY_NAME_REQUIRED),
    body('priceType').isString().notEmpty().withMessage(constantUtils.PRICE_TYPE_REQUIRED),
    body('quantityType').isString().notEmpty().withMessage(constantUtils.QUANTITY_TYPE_REQUIRED),
  ];
};

exports.updateEquipmentQuantityTypeValidationRule = () => {
  return [
    body('name')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.EQUIPMENT_CATEGORY_NAME_REQUIRED)
      .optional({ checkFalsy: false }),
    body('priceType')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.PRICE_TYPE_REQUIRED)
      .optional({ checkFalsy: false }),
    body('quantityType')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.QUANTITY_TYPE_REQUIRED)
      .optional({ checkFalsy: false }),
  ];
};
