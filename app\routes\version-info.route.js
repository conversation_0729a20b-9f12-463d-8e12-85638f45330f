const express = require('express');
const routes = express.Router();

const { validate } = require('../middlewares/validate.middleware');
const versionInfoController = require('../controllers/version-info.controller');

// middleware
const { verifyToken, authAccount } = require('../middlewares/auth.middleware');

// Validator
const validator = require('../validators/version-info.validator');

routes.post(
  '',
  verifyToken,
  authAccount,
  validator.createVersionInfoValidationRule(),
  validate,
  versionInfoController.createVersionInfo
);

routes.post(
  '/check-version',
  validator.checkVersionInfoValidationRule(),
  validate,
  versionInfoController.checkVersionInfo
);

routes.get('', verifyToken, authAccount, validate, versionInfoController.getAllVersionInfo);

routes.patch('/:id', verifyToken, authAccount, validate, versionInfoController.updateVersionInfo);

routes.delete('/:id', verifyToken, authAccount, validate, versionInfoController.deleteVersionInfo);

module.exports = routes;
