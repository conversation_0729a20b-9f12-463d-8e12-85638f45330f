const Licence = require('../models/licence.model');

/**
 * Create Licence
 *
 * @param {*} licence
 * @returns
 */
exports.createLicence = async licence => {
  return await Licence.create(licence);
};

/**
 * Get All Licence
 *
 * @param {*} page
 * @param {*} perPage
 * @returns
 */
exports.getAllLicence = async () => {
  return Licence.aggregate([
    {
      $lookup: {
        from: 'permissions',
        localField: '_id',
        foreignField: 'licence',
        pipeline: [{ $project: { licence: 0, createdAt: 0, updatedAt: 0, __v: 0 } }],
        as: 'permissions',
      },
    },
  ]);
};

/**
 * Get Licence By Name
 *
 * @param {*} licenceName
 * @returns
 */
exports.getLicenceByName = async licenceName => {
  return await Licence.find({ name: licenceName });
};

/**
 * Get Licence By Filter
 *
 * @param {*} filter
 * @returns
 */
exports.getLicencesByFilter = async filter => {
  return await Licence.findOne(filter);
};

/**
 * Get All Licences
 *
 * @returns
 */
exports.getLicences = async () => {
  return await Licence.find();
};
