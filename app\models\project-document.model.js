const mongoose = require('mongoose');

const ProjectDocument = mongoose.Schema(
  {
    title: {
      type: String,
    },
    project: {
      type: mongoose.Types.ObjectId,
      ref: 'project',
    },
    documentNumber: {
      type: String,
      default: null,
    },
    type: {
      type: String,
      enum: [
        'procedure',
        'management_of_change',
        'safety_update',
        'quality_update',
        'safety_notification',
      ],
    },
    document: [
      {
        name: { type: String },
        url: { type: String },
        type: {
          type: String,
          default: null,
        },
        version: {
          type: String,
          default: 'v1',
        },
        isActive: {
          type: Boolean,
          default: true,
        },
        date: {
          type: Date,
          default: null,
        },
      },
    ],
    docReviews: [
      {
        user: {
          type: mongoose.Types.ObjectId,
          ref: 'user',
        },
        isAcknowledged: {
          type: Boolean,
          default: false,
        },
        acknowledgedAt: {
          type: Date,
          default: null,
        },
      },
    ],
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('project-document', ProjectDocument);
