const ReturnOrderHistory = require('../models/return-order-history.model');

exports.createReturnOrderHistory = async (requestData, session) => {
  const returnOrderHistory = new ReturnOrderHistory(requestData);
  return await returnOrderHistory.save({ session });
};

exports.updateReturnOrderHistory = async (id, requestData, session) => {
  return await ReturnOrderHistory.findByIdAndUpdate(id, requestData, {
    new: true,
    session,
  });
};

exports.removeReturnOrderHistory = async (id, session) => {
  return await ReturnOrderHistory.findByIdAndDelete(id, { session });
};

exports.getSingleReturnOrderHistory = async filter => {
  return await ReturnOrderHistory.findOne(filter).populate([
    {
      path: 'equipment',
      select: { _id: 1, name: 1 },
      strictPopulate: false,
      populate: {
        path: 'warehouse',
        select: { name: 1 },
      },
    },
    {
      path: 'project',
      select: { _id: 1, title: 1, projectNumber: 1 },
      strictPopulate: false,
    },
    {
      path: 'wmComment.user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
  ]);
};

exports.getReturnOrderHistoryById = async id => {
  return await ReturnOrderHistory.findById(id);
};

/**
 * Return Order History By Filter
 *
 * @param {*} filter
 * @returns
 */
exports.getReturnOrderHistoryByFilter = async filter => {
  return await ReturnOrderHistory.findOne(filter);
};

/**
 * Get Return Order History
 *
 * @param {*} filter
 * @returns
 */
exports.getReturnOrderHistory = async filter => {
  return await ReturnOrderHistory.find(filter);
};

exports.getReturnOrderEquipmentList = async (filter, page, perPage, sort) => {
  let aggregateFunction = [
    {
      $match: filter,
    },
    {
      $lookup: {
        from: 'accounts',
        localField: 'account',
        foreignField: '_id',
        pipeline: [{ $project: { _id: 1, name: 1 } }],
        as: 'account',
      },
    },
    {
      $unwind: '$account',
    },
    {
      $lookup: {
        from: 'users',
        localField: 'createdBy',
        foreignField: '_id',
        pipeline: [{ $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1 } }],
        as: 'createdBy',
      },
    },
    {
      $unwind: '$createdBy',
    },
    {
      $lookup: {
        from: 'equipment',
        localField: 'equipment',
        foreignField: '_id',
        as: 'equipmentDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              name: 1,
              equipmentNumber: 1,
              qrCode: 1,
              equipmentImage: 1,
              serialNumber: 1,
              equipmentType: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$equipmentDetails',
    },
    {
      $lookup: {
        from: 'equipment-types',
        localField: 'equipmentDetails.equipmentType',
        foreignField: '_id',
        as: 'equipmentTypeDetails',
        pipeline: [{ $project: { _id: 1, type: 1, equipmentCategory: 1, quantityType: 1 } }],
      },
    },
    {
      $unwind: '$equipmentTypeDetails',
    },
    {
      $lookup: {
        from: 'equipment-categories',
        localField: 'equipmentTypeDetails.equipmentCategory',
        foreignField: '_id',
        as: 'equipmentCategoryDetails',
        pipeline: [{ $project: { _id: 1, name: 1 } }],
      },
    },
    {
      $unwind: '$equipmentCategoryDetails',
    },
    {
      $lookup: {
        from: 'equipment-quantity-types',
        localField: 'equipmentTypeDetails.quantityType',
        foreignField: '_id',
        as: 'quantityTypeDetails',
        pipeline: [{ $project: { _id: 1, name: 1, priceType: 1, quantityType: 1 } }],
      },
    },
    {
      $unwind: '$quantityTypeDetails',
    },
    {
      $lookup: {
        from: 'projects',
        localField: 'project',
        foreignField: '_id',
        pipeline: [{ $project: { _id: 1, title: 1, projectNumber: 1 } }],
        as: 'project',
      },
    },
    {
      $unwind: '$project',
    },
    {
      $group: {
        _id: '$_id',
        equipmentId: { $first: '$equipmentDetails._id' },
        equipmentName: { $first: '$equipmentDetails.name' },
        equipmentNumber: { $first: '$equipmentDetails.equipmentNumber' },
        equipmentImage: { $first: '$equipmentDetails.equipmentImage' },
        qrCode: { $first: '$equipmentDetails.qrCode' },
        serialNumber: { $first: '$equipmentDetails.serialNumber' },
        equipmentType: { $first: '$equipmentTypeDetails.type' },
        equipmentCategory: { $first: '$equipmentCategoryDetails.name' },
        equipmentQuantityType: { $first: '$quantityTypeDetails.quantityType' },
        pmReceivedQuantity: { $first: '$pmReceivedQuantity' },
        pmDispatchQuantity: { $first: '$pmDispatchQuantity' },
        status: { $first: '$status' },
        returnOrder: { $first: '$returnOrder' },
        orderBy: {
          $first: {
            id: '$createdBy._id',
            callingName: '$createdBy.callingName',
            firstName: '$createdBy.firstName',
            lastName: '$createdBy.lastName',
          },
        },
        project: {
          $first: '$project',
        },
        account: { $first: '$account' },
        createdAt: { $first: '$createdAt' },
        updatedAt: { $first: '$updatedAt' },
      },
    },
    {
      $sort: {
        createdAt: sort,
      },
    },
    {
      $group: {
        _id: {
          returnOrder: '$returnOrder',
          project: '$project.title',
          projectId: '$project._id',
        },
        returnEquipmentData: {
          $push: {
            _id: '$_id',
            equipmentId: '$equipmentId',
            equipmentName: '$equipmentName',
            equipmentNumber: '$equipmentNumber',
            equipmentImage: '$equipmentImage',
            qrCode: '$qrCode',
            serialNumber: '$serialNumber',
            equipmentType: '$equipmentType',
            equipmentCategory: '$equipmentCategory',
            equipmentQuantityType: '$equipmentQuantityType',
            pmReceivedQuantity: '$pmReceivedQuantity',
            pmDispatchQuantity: '$pmDispatchQuantity',
            status: '$status',
            orderBy: '$orderBy',
            project: '$project',
            account: '$account',
            createdAt: '$createdAt',
            updatedAt: '$updatedAt',
          },
        },
      },
    },
  ];

  aggregateFunction.push(
    {
      $skip: parseInt(page) * parseInt(perPage),
    },
    {
      $limit: parseInt(perPage),
    }
  );

  return await ReturnOrderHistory.aggregate(aggregateFunction);
};

/**
 * Update Return Order
 *
 * @param {*} filter
 * @param {*} requestData
 * @returns
 */
exports.updateReturnOrder = async (filter, requestData, session) => {
  return await ReturnOrderHistory.findOneAndUpdate(filter, requestData, {
    new: true,
    session,
  });
};
