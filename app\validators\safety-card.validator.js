const { body, constantUtils } = require('../validators/parent.validator');

exports.safetyCardValidationRule = () => {
  return [
    body('title')
      .isString()
      .notEmpty()
      .isLength({ max: global.constant.QHSE_TITLE_LIMIT })
      .withMessage(constantUtils.INVALID_TITLE_LENGTH),
    body('project').isString().notEmpty().withMessage(constantUtils.INVALID_PROJECT_NAME),
    body('location').isString().notEmpty().withMessage(constantUtils.INVALID_PROJECT_LOCATION),
  ];
};

exports.updateSafetyCardValidationRule = () => {
  return [
    body('title')
      .isString()
      .notEmpty()
      .isLength({ max: global.constant.QHSE_TITLE_LIMIT })
      .withMessage(constantUtils.INVALID_TITLE_LENGTH),
    body('project')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.INVALID_PROJECT_NAME)
      .optional({ checkFalsy: false }),
    body('location')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.INVALID_PROJECT_LOCATION)
      .optional({ checkFalsy: false }),
  ];
};
