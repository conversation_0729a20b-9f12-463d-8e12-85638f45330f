const mongoose = require('mongoose');
/* packages */
const bcrypt = require('bcryptjs');
const randomId = require('rand-token').uid;

// services
const userService = require('../services/user.service');
const { successResponse, errorResponse } = require('../utils/response.utils');
const constants = require('../utils/constants.utils');
const logService = require('../services/log.service');
const accountService = require('../services/account.service');
const nextOfKinService = require('../services/next-of-kin.service');
const questionService = require('../services/question.service');
const userRoleHistoryService = require('../services/user-role-history.service');
const memeberService = require('../services/member.service');
const roleAgreementService = require('../services/role-agreement.service');
const accountLicenceService = require('../services/account-licence.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const commonUtils = require('../utils/common.utils');
const mailerUtils = require('../utils/mailer.utils');
const { validateSearch, updateSyncApiManage } = require('../utils/common-function.utils');
const HTTP_STATUS = require('../utils/status-codes');

/* create new user */
exports.create = async (req, res) => {
  let reqData = req.body;
  const {
    password,
    email,
    firstName,
    callingName,
    lastName,
    project = null,
    projectFunction = null,
    resourceNumber,
  } = req.body;

  let newName;
  if (callingName !== undefined && callingName !== null && callingName !== '') {
    newName = callingName.trim();
  } else if (firstName !== undefined && firstName !== null && firstName !== '') {
    newName = firstName;
  } else {
    newName = 'N/A';
  }

  reqData.fullName = newName + ' ' + reqData.lastName;

  try {
    const isAlreadyExist = await userService.getByEmail(reqData.email?.toLowerCase());

    if (isAlreadyExist) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(errorResponse(constants.EMAIL_ALREADY_EXIST));
    }
    if (resourceNumber) {
      const isResourceNumberExist = await userService.getByResourceNumber(resourceNumber);

      if (isResourceNumberExist) {
        return res
          .status(HTTP_STATUS.BAD_REQUEST)
          .json(errorResponse(constants.RESOURCE_NUMBER_ALREADY_EXIST));
      }
    }

    reqData.account = req.userData.account;
    reqData.createdBy = req.userData.id;
    reqData.email = reqData.email?.toLowerCase();

    /* 30 min expire time */
    reqData.resetExpiresIn = new Date(Date.now() + parseInt(30 * 60 * 1000));
    /* generate random token */
    reqData.resetToken = randomId(16);

    //passwing password as null
    reqData.password = bcrypt.hashSync(reqData.password);
    const account = await accountService.findAccountById(req.userData.account);
    let createdUser = await userService.createUser(reqData);
    createdUser = await createdUser.populate([
      {
        path: 'roles role',
        select: { title: 1, _id: 1, isActive: 1, accessType: 1 },
        strictPopulate: false,
      },
    ]);

    let kinReq = req.body.nextOfKin;
    if (Object.hasOwn(req.body, 'nextOfKin')) {
      kinReq.forEach(async (element, index) => {
        if (index <= 2) {
          let newReq = { ...element, user: createdUser._id, createdBy: req.userData.id };
          await nextOfKinService.create(newReq);
        }
      });
    }

    /** create member of project */
    if (project) {
      let newReq = {
        user: createdUser._id,
        account: req.userData.account,
        project,
        function: projectFunction || null,
        isApprover: false,
      };

      let exist = await memeberService.getMember(newReq);

      if (exist) {
        return res.status(400).json(responseUtils.errorResponse(constantUtils.MEMBER_EXIST));
      }

      await memeberService.createMember(newReq);
    }
    /** create member of project */

    let template = process.env.SENDGRID_NEW_USER_REGISTRATION_EMAIL;
    let data;
    data = {
      email,
      password,
      company: account.name,
      userName: newName + ' ' + lastName,
      supportTeamEmail: process.env.SUPPORT_TEAM_EMAIL,
      isAdmin: false,
      accessType: createdUser.role.accessType,
      currentYear: new Date().getFullYear(),
      bestRegards: process.env.BEST_REGARDS,
      logo: global.constant.APP_LOGO,
    };
    const baseLoginUrl = process.env.BASE_URL + global.constant.LOGIN_URL;
    if (createdUser.role.accessType === 'web') {
      data.loginUrl = baseLoginUrl;
    } else {
      data.androidApp = process.env.ANDROID_APP_LINK || '';
      data.iosApp = process.env.IOS_APP_LINK || '';

      if (createdUser.role.accessType === 'both') {
        data.loginUrl = baseLoginUrl;
      }
    }
    await mailerUtils.sendMailer(email, template, data);
    if (createdUser) {
      /* Log success data */
      let logData = {
        module: 'user',
        userId: createdUser._id,
        actionType: 'create',
        responseMsg: constants.USER_CREATE,
        requestData: reqData,
        responseData: successResponse(constants.USER_CREATE, createdUser),
        statusCode: 200,
        createdBy: createdUser._id,
      };
      await logService.createLog(logData);

      /* create user role history */
      let userRoleHistoryData = {
        user: createdUser._id,
        role: createdUser.role,
        account: req.userData.account,
        createdBy: req.userData.id,
      };
      await this.manageUserRoleHistory(userRoleHistoryData);

      /* update sync api manage data */
      if (createdUser) {
        await this.commonUpdateSyncApiManage(req.userData.account);
      }
      return res.status(200).json(successResponse(constants.USER_CREATE, createdUser));
    }
  } catch (err) {
    if (err.message) {
      /* log error data */
      let logData = {
        module: 'user',
        actionType: 'create',
        responseMsg: err.message,
        errorMsg: constants.SOMETHING_WENT_WRONG,
        responseData: errorResponse(constants.SOMETHING_WENT_WRONG, {
          message: err.message,
        }),
        requestData: reqData,
        statusCode: 500,
      };

      await logService.createLog(logData);
    }
    return res.status(500).json(
      errorResponse(constants.SOMETHING_WENT_WRONG, {
        message: err.message,
      })
    );
  }
};

/**
 * Get User Profile
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getUserProfile = async (req, res) => {
  try {
    const id = req.query.admin ?? req.userData._id;
    const { type } = req.query;

    let isExist = await userService.getUserById(id, type);

    if (!isExist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_USER));
    }

    let userProfile = await questionService.checkUserQuestionDiffrence(isExist[0]);

    userProfile.userCertificate.forEach(certificate => {
      if (certificate.certificateType.validityDate) {
        if (new Date(certificate.endDate) < new Date()) {
          certificate.status = 'expired';
        }
      }
    });

    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.USER_EXIST, userProfile));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

exports.deleteUser = async (req, res) => {
  try {
    const id = req.params.id;
    const isExist = await userService.getUserById(id);

    if (!isExist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_USER));
    }
    const response = await userService.deleteUser(id);
    await nextOfKinService.delete(id, req.deletedAt);
    /* update sync api manage data */
    if (response) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.USER_DELETED, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Update User
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateUser = async (req, res) => {
  try {
    const id = req.params.id;
    const isExist = await userService.getUserData(id);
    if (req?.body?.email) {
      req.body.email = req.body.email.toLowerCase();
    }

    if (!isExist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_USER));
    }

    const isAdminUser = await accountService.findAccountById(req.userData.account);

    const filterData = {
      account: req.userData.account,
      isApproved: true,
    };
    const accountLicenceData = await accountLicenceService.getAccountLicenceByAccount(filterData);

    const [accountLicence] = accountLicenceData.filter(
      data => data.permission.name === 'Personnel'
    );
    let roleAgreements;
    if (accountLicence) {
      let filterRoleAgreement = {
        role: req.userData.role._id,
        accountLicence: accountLicence._id,
        isActive: true,
        deletedBy: null,
      };

      [roleAgreements] = await roleAgreementService.filterRoleAgreements(filterRoleAgreement);
    }

    const updatePermission = roleAgreements ? roleAgreements.agreement.update : false;
    // only admin can update user
    if (!updatePermission) {
      const adminPermission = await this.checkAdminPermissions(req, isExist, isAdminUser);

      if (!adminPermission) {
        return res
          .status(HTTP_STATUS.BAD_REQUEST)
          .json(responseUtils.errorResponse(constantUtils.ONLY_ADMIN_ACCESS_TO_RESOURCES));
      }
      const emailPermission = await this.checkEmailPermissions(req, isExist, isAdminUser);

      if (!emailPermission) {
        return res
          .status(HTTP_STATUS.BAD_REQUEST)
          .json(responseUtils.errorResponse(constantUtils.RESTRICTED_EMAIL_UPDATE));
      }
    }
    const isAlreadyExist = await userService.getByEmail(req.body.email);
    if (isAlreadyExist) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(errorResponse(constants.EMAIL_ALREADY_EXIST));
    }
    if (req.body.resourceNumber) {
      const isResourceNumberExist = await userService.getByResourceNumber(req.body.resourceNumber);
      if (isResourceNumberExist) {
        return res
          .status(HTTP_STATUS.BAD_REQUEST)
          .json(errorResponse(constants.RESOURCE_NUMBER_ALREADY_EXIST));
      }
    }
    // eslint-disable-next-line no-prototype-builtins
    if (req.body.hasOwnProperty('nextOfKin')) {
      await this.handleNextOfKin(id, req.body.nextOfKin, req.userData.id, res);
    }
    req.body.createdBy = req.userData.id;

    const response = await userService.updateUser(id, req.body);

    /** send email if email changed */
    if (req?.body?.email) {
      await mailerUtils.sendMailer(req.body.email, process.env.SENDGRID_CREDIANTIAL_CHANGE_EMAIL, {
        newEmail: req.body.email,
        oldEmail: isExist.email,
        company: isAdminUser.name,
        userName: `${isExist.firstName}  ${isExist.lastName}`,
        supportTeamEmail: process.env.SUPPORT_TEAM_EMAIL,
        isAdmin: false,
        accessType: isExist.role.accessType,
        currentYear: new Date().getFullYear(),
        bestRegards: process.env.BEST_REGARDS,
        logo: global.constant.APP_LOGO,
      });
    }

    if (Object.hasOwn(req.body, 'email') && isExist.role.title == 'admin') {
      await accountService.updateAccount(isExist.account, { email: req.body.email });
    }

    // update user role history
    if (response && req.body.role) {
      let userRoleHistoryData = {
        user: response._id,
        role: response.role,
        account: req.userData.account,
        createdBy: req.userData.id,
      };
      await this.manageUserRoleHistory(userRoleHistoryData);
    }
    if (response) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.USER_UPDATED, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get all Users
 *
 * @param {*} req
 * @param {*} res
 */
exports.getAllUsers = async (req, res) => {
  try {
    let page = req.query.page ? req.query.page : '';
    let perPage = req.query.perPage ? req.query.perPage : '';
    let search = await validateSearch(req.query.search);
    let assignedProject = req.query.assignedProject;
    delete req.query.assignedProject;

    if (!search && search !== '') {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.INVALID_SEARCH));
    }

    let filterForAllRecords = {};
    let data;
    let allRecordsCount;
    if (
      req.query.search ||
      (req.query.country && req.query.country.toLowerCase() != 'all') ||
      (req.query.nationality && req.query.nationality.toLowerCase() != 'all')
    ) {
      // Extract all filter parameters
      let { search = '', country = '', nationality = '' } = req.query;

      // Process search term
      const searchName = commonUtils.isValidId(search) ? search : search.replace(/_/g, ' ');

      // Process country filter
      if (country && country.includes('_')) {
        country = commonUtils.replaceUnderscoreWithSpace(country);
      }
      country = country && country.toLowerCase() !== 'all' ? new RegExp(country, 'i') : '';

      // Process nationality filter
      if (nationality && nationality.includes('_')) {
        nationality = commonUtils.replaceUnderscoreWithSpace(nationality);
      }
      nationality =
        nationality && nationality.toLowerCase() !== 'all' ? new RegExp(nationality, 'i') : '';

      // Get data with all filters applied
      const searchResult = await userService.searchUser(req, page, perPage, assignedProject);

      // Handle the new return format from searchUser
      if (searchResult && typeof searchResult === 'object' && 'data' in searchResult) {
        data = searchResult.data;
        allRecordsCount = searchResult.totalCount;
      } else {
        data = searchResult;
      }

      // Prepare filter for all records count
      filterForAllRecords = {
        $and: [
          ...(commonUtils.isValidId(search) ? [{ _id: searchName }] : []),
          ...(!commonUtils.isValidId(search) && search
            ? [{ callingName: searchName }, { firstName: searchName }, { lastName: searchName }]
            : []),
          ...(search
            ? [
                {
                  $expr: {
                    $regexMatch: {
                      input: { $concat: ['$callingName', ' ', '$firstName', ' ', '$lastName'] },
                      regex: searchName,
                      options: 'i',
                    },
                  },
                },
              ]
            : []),
        ],
        ...(country ? { country: country } : {}),
        ...(nationality ? { nationality: nationality } : {}),
        ...(req.query.isActive && { isActive: req.query.isActive }),
        account: req.userData.account,
        deletedAt: null,
      };
    } else if (req.query.role) {
      let filter = {
        account: req.userData.account,
        role: req.query.role,
        isActive: true,
        deletedAt: null,
      };
      filterForAllRecords = filter;
      data = await userService.getAllActiveUsersByRole(filter);
    } else {
      let removeKeys = ['page', 'perPage', 'sort'];
      let requestParams = await commonUtils.filterParamsModify(req.query, removeKeys);
      let filterData = {
        ...requestParams,
        account: req.userData.account,
        ...(req.query.isActive ? { isActive: req.query.isActive } : {}),
        deletedAt: null,
      };
      filterForAllRecords = filterData;
      data = await userService.getAllUsers(page, perPage, filterData, assignedProject);
    }
    // If allRecordsCount is not already set by searchUser, use data.length as the count
    if (typeof allRecordsCount === 'undefined') {
      allRecordsCount = data.length;
    }

    // Only apply manual pagination if we're not using the searchUser function
    // or if we're using assignedProject (which doesn't use MongoDB pagination)
    if (
      page !== '' &&
      perPage !== '' &&
      (req.query.role ||
        (!req.query.search && !req.query.nationality && !req.query.country) ||
        assignedProject)
    ) {
      const pageNumber = parseInt(page, 10) || 0;
      const itemsPerPage = parseInt(perPage, 10) || 10;

      const start = pageNumber * itemsPerPage;
      const end = start + itemsPerPage;
      data = data.slice(start, end);
    }
    const dataClone = JSON.parse(JSON.stringify(data));
    // eslint-disable-next-line no-undef
    const userData = await Promise.all(
      dataClone.map(async item => {
        const userProject = await memeberService.getMemberByUserId({
          user: commonUtils.toObjectId(item._id),
          deletedAt: null,
        });
        return {
          ...item,
          projectDetails:
            userProject && userProject.length > 0 ? userProject[0].projectDetails : [],
        };
      })
    );

    // add all records count
    let finalResponse = {
      usersData: assignedProject ? data : userData,
      currentPage: Number(page),
    };

    if (!assignedProject) {
      // If we already have the count from searchUser, use it directly
      if (typeof allRecordsCount !== 'undefined') {
        finalResponse.allRecordsCount = allRecordsCount;
      } else {
        // Otherwise, get the count from the database
        finalResponse = await commonUtils.getCountFromQuery(
          'user',
          filterForAllRecords,
          finalResponse
        );
      }
    } else {
      finalResponse.allRecordsCount = allRecordsCount;
      finalResponse.usersData = data;
    }

    res.status(200).json(responseUtils.successResponse(constantUtils.ALL_USER_LIST, finalResponse));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Change user status to inactive
 *
 * @param {*} req
 * @param {*} res
 */
exports.changeStatus = async (req, res) => {
  try {
    if (req?.userData?.role?.includes('admin')) {
      const users = await userService.getActiveUsers(req.body.user);
      users.forEach(async user => {
        const userData = await userService.getUserById(user._id);
        if (!userData) {
          return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_USER));
        }

        await userService.changeStatus(user._id);
      });
      await this.commonUpdateSyncApiManage(req.userData.account);
      return res.status(200).json(responseUtils.successResponse(constantUtils.STATUS_CHANGED));
    }

    return res
      .status(HTTP_STATUS.BAD_REQUEST)
      .json(responseUtils.successResponse(constantUtils.NO_ACCESS));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(constantUtils.ERROR));
  }
};

/**
 * Get user by id
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getUserById = async (req, res) => {
  try {
    const id = req.params.id;
    const getUser = await userService.getUserById(id);

    if (getUser.length === 0) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_USER));
    }

    getUser[0].userCertificate.forEach(certificate => {
      if (certificate.certificateType.validityDate) {
        if (new Date(certificate.endDate) < new Date()) {
          certificate.status = 'expired';
        }
      }
    });

    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.USER_EXIST, getUser[0]));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Manage user role history
 *
 * @param {*} requestData
 */
exports.manageUserRoleHistory = async requestData => {
  try {
    let { user, role, account, createdBy } = requestData;
    let filterData = {
      user,
      account,
      deletedAt: null,
    };
    const getHistoryData = await userRoleHistoryService.getAllUserRoleHistory(filterData);

    if (getHistoryData.length > 0) {
      const lastHistory = getHistoryData[getHistoryData.length - 1];
      if (lastHistory.status == 'current') {
        let updateData = {
          status: userRoleHistoryService.PREVIOUS,
          updatedAt: new Date(),
          updatedBy: createdBy,
        };
        await userRoleHistoryService.updateUserRoleHistory(lastHistory._id, updateData);
      }
    }

    let createData = {
      user,
      role,
      status: userRoleHistoryService.CURRENT,
      account,
      createdBy,
      createdAt: new Date(),
    };
    await userRoleHistoryService.createUserRoleHistory(createData);
  } catch (error) {
    throw new Error(error.message);
  }
};

exports.checkAdminPermissions = async (req, isExist, isAdminUser) => {
  if (
    req.userData.email != isExist.email &&
    isAdminUser &&
    isAdminUser.email !== req.userData.email &&
    req.userData.role.accessType !== 'all'
  ) {
    return false;
  }
  return true;
};

exports.checkEmailPermissions = async (req, isExist, isAdminUser) => {
  if (
    (req.userData.role.accessType == 'mobile' && req.body.email) ||
    (req.userData.email != isExist.email &&
      req.userData.role.accessType !== 'all' &&
      isAdminUser &&
      isAdminUser.email !== req.userData.email)
  ) {
    return false;
  }
  return true;
};

exports.handleNextOfKin = async (id, kinData, userId, res) => {
  for (const { _id, ...element } of kinData) {
    if (_id) {
      await nextOfKinService.update(_id, element);
    } else {
      const totalKin = await nextOfKinService.getKinByUserId(id);
      if (totalKin.length < 3) {
        const newReq = { ...element, user: id, createdBy: userId };
        await nextOfKinService.create(newReq);
      } else {
        return res.status(400).json(responseUtils.errorResponse(constantUtils.LIMIT_EXCEED));
      }
    }
  }
};

/**
 * submit user Rating
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.submitUserRating = async (req, res) => {
  try {
    let reqData = req.body;
    const userId = req.params.id;

    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.INVALID_ID));
    }

    const [user] = await userService.getSingleUser(userId, req.userData.account);

    if (!user) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_USER));
    }

    reqData = { ...reqData, ratedBy: req.userData._id, ratedAt: new Date() };

    const userData = await userService.updateuserRating(user._id, reqData);

    res.status(200).json(responseUtils.successResponse(constantUtils.SUBMIT_RATING, userData));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Delete user rating
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.deleteUserRating = async (req, res) => {
  try {
    const { id, ratingId } = req.params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.INVALID_ID));
    }

    const [user] = await userService.getUsersByFilter({
      _id: id,
      rating: { $elemMatch: { _id: ratingId } },
      account: req.userData.account,
      deletedAt: null,
    });

    if (!user) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_USER));
    }

    if (
      user.rating.some(
        rating => rating._id.equals(ratingId) && !rating.ratedBy.equals(req.userData._id)
      )
    ) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.DELETE_USER_RATING));
    }

    const userData = await userService.deleteUserRating(user._id, ratingId);

    res.status(200).json(responseUtils.successResponse(constantUtils.RATING_DELETED, userData));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

exports.commonUpdateSyncApiManage = async account => {
  await updateSyncApiManage({
    syncApis: ['users'],
    account,
  });
};
