/* models */
const ReportQuestion = require('../models/report-question.model');

/**
 * Create Report Questions
 *
 * @param {*} requestData
 * @returns
 */
exports.createReportQuestion = async (requestData, session) => {
  const reportQuestion = new ReportQuestion(requestData);
  return await reportQuestion.save({ session });
};

exports.getQuestionAnswer = async (filter, page, perPage) => {
  let agreegateFunction = [
    {
      $match: filter,
    },
    {
      $sort: {
        createdAt: 1,
      },
    },
    {
      $lookup: {
        from: 'accounts',
        localField: 'account',
        foreignField: '_id',
        pipeline: [{ $project: { name: 1 } }],
        as: 'account',
      },
    },
    {
      $unwind: {
        path: '$account',
      },
    },
    {
      $lookup: {
        from: 'report-question-answers',
        localField: '_id',
        foreignField: 'reportQuestion',
        pipeline: [
          {
            $match: {
              deletedAt: null,
              'title.isActive': true,
            },
          },
          { $sort: { createdAt: 1 } },
          {
            $project: {
              _id: 1,
              title: 1,
              parameterType: 1,
              option: 1,
              range: 1,
              numberOfAnsers: 1,
            },
          },
          {
            $lookup: {
              from: 'parameter-types',
              localField: 'parameterType',
              foreignField: '_id',
              pipeline: [{ $project: { name: 1, uniqueKey: 1, isActive: 1 } }],
              as: 'parameterType',
            },
          },
          {
            $unwind: {
              path: '$parameterType',
            },
          },
        ],
        as: 'answers',
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'createdBy',
        foreignField: '_id',
        pipeline: [{ $project: { callingName: 1, firstName: 1, lastName: 1 } }],
        as: 'createdBy',
      },
    },
    {
      $unwind: {
        path: '$createdBy',
      },
    },
    {
      $lookup: {
        from: 'reports',
        localField: 'report',
        foreignField: '_id',
        pipeline: [{ $project: { title: 1, type: 1, status: 1, isProgressable: 1, isPublish: 1 } }],
        as: 'report',
      },
    },
    {
      $unwind: {
        path: '$report',
      },
    },
    {
      $group: {
        _id: '$report._id',
        report: { $first: '$report' },
        questions: {
          $push: {
            _id: '$_id',
            title: '$title',
            sortOrder: '$sortOrder',
            duration: '$duration',
            weight: '$weight',
            supportedContent: '$supportedContent',
            answerTypes: '$answers',
            createdBy: '$createdBy',
            createdAt: '$createdAt',
          },
        },
        account: { $first: '$account' },
        createdBy: { $first: '$createdBy' },
        createdAt: { $first: '$createdAt' },
      },
    },
    {
      $project: {
        _id: 1,
        report: 1,
        questions: 1,
        account: 1,
        createdBy: 1,
        createdAt: 1,
      },
    },
  ];
  if (page !== '' && perPage !== '') {
    agreegateFunction.push(
      {
        $skip: parseInt(page) * parseInt(perPage),
      },
      {
        $limit: parseInt(perPage),
      }
    );
  }
  return await ReportQuestion.aggregate(agreegateFunction);
};

exports.updateReportQuestion = async (id, update, session) => {
  return await ReportQuestion.findByIdAndUpdate(id, update, { new: true, session });
};

exports.getSingleReportQuestion = async filter => {
  return await ReportQuestion.findOne(filter).populate({
    path: 'report',
    select: { _id: 1, title: 1, type: 1, status: 1, isPublish: 1 },
    strictPopulate: false,
  });
};

exports.calculateQuestionWeightAndUpdate = async report => {
  const getQuestions = await ReportQuestion.find({ report, deletedAt: null });
  if (getQuestions.length > 0) {
    let countDurations = getQuestions.reduce((sum, item) => sum + item.duration, 0);
    for (let question of getQuestions) {
      let weightPercentage = question.duration
        ? ((question.duration / countDurations) * 100).toFixed(2)
        : 0;

      await ReportQuestion.findByIdAndUpdate(
        question._id,
        { weight: weightPercentage },
        { new: true }
      );
    }
  }
};

/**
 * Update Db Migration
 *
 * @param {*} filter
 * @param {*} update
 */
exports.upDateReportQuestionDb = async (table, filter, update) => {
  return ReportQuestion.updateMany(filter, update);
};

/**
 * Get Report Question
 *
 * @param {*} ids
 */
exports.getReportQuestion = async ids => {
  let aggregationFunction = [
    { $match: { report: ids, deletedAt: null } },
    { $sort: { sortOrder: 1 } },
    {
      $lookup: {
        from: 'report-questions',
        let: { id: '$_id' },
        pipeline: [
          { $match: { $expr: { $eq: ['$_id', '$$id'] } } },
          { $unwind: '$supportedContent' },
          { $sort: { 'supportedContent.sortOrder': 1 } },
          {
            $group: {
              _id: '$_id',
              sortedSupportedContent: { $push: '$supportedContent' },
            },
          },
          { $project: { supportedContent: '$sortedSupportedContent' } },
        ],
        as: 'reportQuestionData',
      },
    },
    {
      $set: {
        supportedContent: { $arrayElemAt: ['$reportQuestionData.supportedContent', 0] },
      },
    },
    {
      $unset: 'reportQuestionData',
    },
    {
      $project: {
        title: 1,
        sortOrder: 1,
        duration: 1,
        isRequired: 1,
        weight: 1,
        supportedContent: 1,
        answerTypes: 1,
        createdBy: 1,
        createdAt: 1,
      },
    },
  ];
  return await ReportQuestion.aggregate(aggregationFunction);
};

exports.reportQuestions = async ids => {
  let aggregationFunction = [
    { $match: { report: ids, deletedAt: null } },
    { $sort: { sortOrder: 1 } },
    {
      $lookup: {
        from: 'report-questions',
        let: { id: '$_id' },
        pipeline: [
          { $match: { $expr: { $eq: ['$_id', '$$id'] } } },
          { $unwind: { path: '$supportedContent', preserveNullAndEmptyArrays: true } },
          { $sort: { 'supportedContent.sortOrder': 1 } },
          {
            $group: {
              _id: '$_id',
              sortedSupportedContent: { $push: '$supportedContent' },
            },
          },
          { $project: { supportedContent: '$sortedSupportedContent' } },
        ],
        as: 'reportQuestionData',
      },
    },
    {
      $set: {
        supportedContent: {
          $ifNull: [{ $arrayElemAt: ['$reportQuestionData.supportedContent', 0] }, []],
        },
      },
    },
    { $unset: 'reportQuestionData' },
    {
      $project: {
        title: 1,
        sortOrder: 1,
        duration: 1,
        isRequired: 1,
        weight: 1,
        supportedContent: 1,
        answerTypes: 1,
        createdBy: 1,
        createdAt: 1,
      },
    },
  ];

  return await ReportQuestion.aggregate(aggregationFunction);
};
