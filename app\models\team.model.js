const mongoose = require('mongoose');

const Team = new mongoose.Schema(
  {
    teamsWfmName: {
      type: String,
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    project: {
      type: mongoose.Types.ObjectId,
      ref: 'project',
    },
    sortOrder: {
      type: Number,
      default: 0,
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
    isDefault: {
      type: Boolean,
      default: false,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('team', Team);
