const { logger, apiLog } = require('../utils/logger.utils');
const get_ip = require('ipware')().get_ip;

/**
 * manage the response logs
 *
 * @param {*} req
 * @param {*} res
 * @param {*} next
 */
module.exports = (req, res, next) => {
  // Print the logs in console and file
  res.logger = logger.child({
    url: req.url,
    requestStartTime: Date.now(),
  });

  let statusLevel = ![200, 201].includes(res.statusCode) ? 'error' : 'info';
  res.logger[`${statusLevel}`](`${res.statusMessage} - ${res.statusCode}`);

  // Store the request logs in collection
  let ipInfo = get_ip(req);

  apiLog.log(`${statusLevel}`, {
    message: `Response Log - ${res.statusMessage}`,
    meta: {
      url: req.url,
      headerInfo: JSON.stringify(req.headers),
      ipInfo: JSON.stringify(ipInfo),
      statusCode: res.statusCode,
      method: req.method,
      createdAt: new Date(),
    },
  });

  next();
};
