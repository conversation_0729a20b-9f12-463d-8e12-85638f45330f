const routes = require('express').Router();

// Middlewares
const { verifyToken, deletedAt, authAccount } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// Validator
const validator = require('../validators/logbooks.validator');

// Controllers
const logbooksController = require('../controllers/logbooks.controller');

routes.post(
  '',
  verifyToken,
  authAccount,
  validator.create,
  validate,
  logbooksController.createLogbook
);

routes.get('/:user', verifyToken, authAccount, logbooksController.getAllLogbooks);

routes.patch(
  '/:id',
  verifyToken,
  authAccount,
  validator.update,
  validate,
  logbooksController.updateLogbook
);

// Delete logbook (soft delete)
routes.delete(
  '/:id',
  verifyToken,
  authAccount,
  validator.delete,
  validate,
  deletedAt,
  logbooksController.deleteLogbook
);

module.exports = routes;
