require('dotenv').config();
const fileSize = require('file-size');
const cryptoRandomString = require('crypto-random-string');
const constantUtils = require('../utils/constants.utils');
const IconUrl = require('../models/icon-url.model');
const axios = require('axios');
// Utils
const uploadImageUtil = require('../utils/upload-image.util');

/**
 * File Upload
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.fileUpload = async req => {
  try {
    const randString = cryptoRandomString({ length: 15, type: 'alphanumeric' });
    let fileName = req.file.originalname;
    req.file.originalname = `${randString}_${req.file.originalname}`;
    req.file.type = req.body.type;
    const iconUrl = await uploadImageUtil.uploadImage(req.file);
    let response = await IconUrl.create(iconUrl);
    let sizeOfFile = fileSize(req.file.size).human('si');
    let returnData = { fileName, sizeOfFile, iconUrl: response.iconUrl, _id: response._id };
    return returnData;
  } catch (error) {
    throw new Error(constantUtils.FILE_UPLOAD_FAILED);
  }
};

/**
 * Download File From Bucket
 *
 * @param {*} fileName
 * @param {*} res
 */
exports.downloadFile = async (fileName, res) => {
  try {
    const filePath = `${process.env.BUCKET_URL}/sample-files/${fileName}`;

    const response = await axios.get(filePath, { responseType: 'stream' });

    res.setHeader('Content-Disposition', `attachment; filename=${fileName}`);
    res.setHeader('Content-Type', response.headers['content-type']);
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    );
    response.data.pipe(res);
  } catch (error) {
    throw new Error(error.message);
  }
};

exports.multipleFileUpload = async req => {
  try {
    const files = req.files;
    for (let file of files) {
      const randString = cryptoRandomString({ length: 15, type: 'alphanumeric' });
      file.uploadName = file.originalname;
      file.originalname = `${randString}_${file.originalname}`;
      file.type = req.body.type;
    }

    let storedFiles = [];
    const uploadedFiles = await uploadImageUtil.uploadMultipleFiles(files);
    for (let fileData of uploadedFiles) {
      let response = await IconUrl.create({ iconUrl: fileData.iconUrl });
      let sizeOfFile = fileSize(fileData.size).human('si');
      let returnData = {
        fileName: fileData.uploadName,
        sizeOfFile,
        iconUrl: response.iconUrl,
        _id: response._id,
      };
      storedFiles.push(returnData);
    }
    return storedFiles;
  } catch (error) {
    throw new Error(constantUtils.FILE_UPLOAD_FAILED);
  }
};
