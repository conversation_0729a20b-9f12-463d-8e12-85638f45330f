const mongoose = require('mongoose');

const Shift = new mongoose.Schema(
  {
    startDate: {
      type: String,
    },
    endDate: {
      type: String,
      default: '',
    },
    duration: {
      type: String,
      default: '00:00',
    },
    // default project name
    defaultProject: {
      type: mongoose.Types.ObjectId,
      ref: 'project',
      default: null,
    },
    defaultIdentifier: {
      type: String,
      default: global.constant.NORMAL_DATA_IDENTIFIER, // DEFAULT_DATA_IDENTIFIER: for default project, NORMAL_DATA_IDENTIFIER: for normal project
    },
    status: {
      type: String,
      enum: ['open', 'submitted', 'in_discussion', 'closed'],
      default: 'open',
      lowercase: true,
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    project: {
      type: mongoose.Types.ObjectId,
      ref: 'project',
    },
    team: {
      type: mongoose.Types.ObjectId,
      ref: 'team',
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    deletedAt: {
      type: Date,
      default: null,
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    isDeleted: {
      type: Boolean,
      default: false,
    },
  },
  { timestamps: true }
);

Shift.index({ account: 1, project: 1, status: 1, deletedAt: 1, startDate: -1, createdAt: -1 });
Shift.index({ account: 1, deletedAt: 1, startDate: -1, createdAt: -1 });
Shift.index({ createdBy: 1, account: 1, deletedAt: 1 });

module.exports = mongoose.model('shift', Shift);
