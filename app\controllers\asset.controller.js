require('dotenv').config();

// Services
const assetService = require('../services/asset.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const commonFunction = require('../utils/common-function.utils');

/**
 * Get All Asset
 *
 * @param {*} req
 * @param {*} res
 */
exports.getAllAsset = async (req, res) => {
  try {
    let reqData = {};
    const newReq = {
      ...reqData,
      account: req.userData.account,
      project: req.query.project,
      deletedAt: null,
    };
    const assetList = await assetService.getAllAsset(newReq);

    res.status(200).json(responseUtils.successResponse(constantUtils.ALL_ASSET_LIST, assetList));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Create Asset
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.createAsset = async (req, res) => {
  try {
    const { cableName, string, project } = req.body;
    let reqData = {};
    const newReq = {
      ...reqData,
      fromLocation: req.body.fromLocation,
      account: req.userData.account,
      project: req.body.project,
      toLocation: req.body.toLocation,
      manufacturer: req.body.manufacturer,
      typeMm2: req.body.typeMm2,
      string: req.body.string,
      cableName: req.body.cableName,
    };

    const filterData = {
      cableName,
      string,
      project,
      account: req.userData.account.toString(),
      deletedAt: null,
      isDefault: false,
    };

    const exist = await assetService.getAssetByName(filterData);

    if (exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.ASSET_EXIST));
    }
    const createdAsset = await assetService.createAsset(newReq);
    // update sync api manage data
    if (createdAsset) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.CREATE_ASSET, createdAsset));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Update Asset
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateAsset = async (req, res) => {
  try {
    let id = req.params.id;
    const { cableName, string } = req.body;

    const exist = await assetService.getAssetById(id);
    if (!exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_ASSET));
    }

    const project = exist.project._id;
    const filterData = {
      _id: { $ne: id },
      cableName,
      string,
      project,
      account: req.userData.account.toString(),
      deletedAt: null,
    };

    const assetExist = await assetService.getAssetByName(filterData);

    if (assetExist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.ASSET_EXIST));
    }
    const response = await assetService.updateAsset(id, req.body);
    // update sync api manage data
    if (response) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.UPDATE_ASSET, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Delete Asset
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.deleteAsset = async (req, res) => {
  try {
    let id = req.params.id;
    const exist = await assetService.getAssetById(id);

    if (!exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_ASSET));
    }

    const response = await assetService.deleteAsset(id, req.deletedAt);
    // update sync api manage data
    if (response) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.DELETE_ASSET, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

exports.commonUpdateSyncApiManage = async account => {
  await commonFunction.updateSyncApiManage({
    syncApis: ['reportConfig'],
    account,
  });
};
