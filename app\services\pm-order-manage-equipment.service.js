const PmOrderManageEquipment = require('../models/pm-order-manage-equipment.model');

// utils
const { toObjectId } = require('../utils/common.utils');

exports.createPMOrderManageEquipment = async requestData => {
  return PmOrderManageEquipment.create(requestData);
};

exports.updatePMOrderManageEquipment = async (id, data, session) => {
  return await PmOrderManageEquipment.findByIdAndUpdate(id, { $set: data }, { new: true, session });
};

exports.updatePMOrderManageEquipmentQuantity = async (id, data, session) => {
  return await PmOrderManageEquipment.findByIdAndUpdate(id, data, { new: true, session });
};

exports.updatePMOrderManageEquipments = async (id, data) => {
  return await PmOrderManageEquipment.findByIdAndUpdate(id, { $set: data }, { new: true });
};

exports.updatePMOrderManageEquipmentByPMOrderId = async (filter, data, session) => {
  return await PmOrderManageEquipment.updateMany(filter, { $set: data }, { new: true, session });
};

exports.searchPMOrderManageEquipment = async filter => {
  return await PmOrderManageEquipment.findOne(filter).populate([
    {
      path: 'project',
      select: { title: 1, _id: 1, projectNumber: 1 },
      strictPopulate: false,
    },
    {
      path: 'wmComments.user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      strictPopulate: false,
    },
    {
      path: 'pmComments.user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      strictPopulate: false,
    },
  ]);
};

exports.getPMOrderManageEquipmentById = async id => {
  return await PmOrderManageEquipment.findById(id);
};

exports.getPMOrderManageEquipment = async filter => {
  return await PmOrderManageEquipment.find(filter);
};

exports.pMOrderManageEquipmentData = async filter => {
  return PmOrderManageEquipment.find(filter)
    .populate({
      path: 'equipmentType',
      select: { id: 1, type: 1 },
      strictPopulate: false,
    })
    .populate({
      path: 'createdBy',
      select: { email: 1 },
      strictPopulate: false,
    });
};

exports.getPMOrderManageEquipmentWithPopulatedData = async filter => {
  return await PmOrderManageEquipment.findOne(filter).populate([
    {
      path: 'equipmentType',
      model: 'equipment-type',
      populate: {
        path: 'quantityType',
        model: 'equipment-quantity-type',
        select: 'name priceType quantityType isActive',
      },
    },
  ]);
};

/**
 * Reject PM Order Manage Equipment
 *
 * @param {*} id
 * @returns
 */
exports.rejectPMOrderManageEquipment = async id => {
  return await PmOrderManageEquipment.findByIdAndDelete(id);
};

/**
 * Get Linked Equipments
 *
 * @param {*} filter
 * @returns
 */
exports.getLinkedEquipments = async filter => {
  return PmOrderManageEquipment.aggregate([
    {
      $match: filter,
    },
    {
      $lookup: {
        from: 'equipment-order-histories',
        let: {
          pmOrderId: '$pmOrder',
          equipmentTypeId: '$equipmentType',
          equipmentIds: '$equipment',
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ['$deletedAt', null] },
                  { $eq: ['$pmOrder', '$$pmOrderId'] },
                  { $eq: ['$equipmentType', '$$equipmentTypeId'] },
                  { $in: ['$equipment', '$$equipmentIds'] },
                  { $eq: ['$status', 'pre-linked'] },
                ],
              },
            },
          },
          {
            $lookup: {
              from: 'equipment',
              localField: 'equipment',
              foreignField: '_id',
              as: 'equipmentDetails',
            },
          },
          {
            $unwind: '$equipmentDetails',
          },
          {
            $lookup: {
              from: 'equipment-types',
              localField: 'equipmentDetails.equipmentType',
              foreignField: '_id',
              as: 'equipmentTypeDetails',
            },
          },
          {
            $unwind: '$equipmentTypeDetails',
          },
          {
            $lookup: {
              from: 'equipment-quantity-types',
              localField: 'equipmentTypeDetails.quantityType',
              foreignField: '_id',
              as: 'quantityTypeDetails',
            },
          },
          {
            $unwind: '$quantityTypeDetails',
          },
          // Add inventory-histories lookup
          {
            $lookup: {
              from: 'inventory-histories',
              let: {
                pmOrderId: '$pmOrder',
                equipmentId: '$equipment',
              },
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $and: [
                        { $eq: ['$deletedAt', null] },
                        { $eq: ['$pmOrder', '$$pmOrderId'] },
                        { $eq: ['$equipment', '$$equipmentId'] },
                      ],
                    },
                  },
                },
                {
                  $group: {
                    _id: '$equipment',
                    inventoryHistoriesId: { $first: '$_id' },
                  },
                },
              ],
              as: 'inventoryHistories',
            },
          },
          {
            $unwind: '$inventoryHistories',
          },
          {
            $project: {
              _id: 1, // Include the _id of the equipment-order-histories document
              name: 1,
              price: 1,
              wmDispatchQuantity: 1,
              equipmentName: '$equipmentDetails.name',
              equipmentPrice: '$equipmentDetails.value',
              equipmentId: '$equipmentDetails._id',
              equipmentImage: '$equipmentDetails.equipmentImage',
              equipmentQrCode: { $ifNull: ['$equipmentDetails.qrCode', []] },
              inventoryHistoriesId: '$inventoryHistories.inventoryHistoriesId', // Add inventory histories result
              quantityType: '$quantityTypeDetails.quantityType',
            },
          },
        ],
        as: 'linkedEquipments',
      },
    },
    {
      $project: {
        wmApprovedQuantity: 1,
        wmDispatchQuantity: 1,
        'linkedEquipments._id': 1, // Include _id in the result
        'linkedEquipments.name': 1,
        'linkedEquipments.price': 1,
        'linkedEquipments.wmDispatchQuantity': 1,
        'linkedEquipments.equipmentName': 1,
        'linkedEquipments.equipmentPrice': 1,
        'linkedEquipments.equipmentId': 1,
        'linkedEquipments.equipmentImage': 1,
        'linkedEquipments.equipmentQrCode': 1,
        'linkedEquipments.inventoryHistoriesId': 1, // Include inventory histories in final result
        'linkedEquipments.quantityType': 1,
      },
    },
  ]);
};

/**
 * Remove Linked Equipments
 *
 * @param {*} equipment
 * @returns
 */
exports.updateLinkedEquipments = async filter => {
  return await PmOrderManageEquipment.updateMany(
    { _id: toObjectId(filter.pmOrder) }, // Find the document
    { $pull: { equipment: toObjectId(filter.equipment) } } // Remove the equipment ID from the array
  );
};

/**
 * Get single PM Order Manage Equipment by filter
 *
 * @param {*} filter
 * @returns
 */
exports.getSinglePMOrderManageEquipmentByFilter = async filter => {
  return await PmOrderManageEquipment.findOne(filter);
};
