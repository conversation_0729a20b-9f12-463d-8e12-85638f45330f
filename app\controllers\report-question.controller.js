const mongoose = require('mongoose');

// Services
const reportQuestionService = require('../services/report-question.service');
const reportQuestionAnswerService = require('../services/report-question-answer.service');
const parameterTypeService = require('../services/parameter-type.service');
const userReportAnswerService = require('../services/user-report-answer.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const { successResponse, errorResponse } = require('../utils/response.utils');
const { transactionOptions } = require('../utils/json-format.utils');
const { toObjectId } = require('../utils/common.utils');
const commonfunctionUtils = require('../utils/common-function.utils');

/**
 * Create Report Questions
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.createReportQuestion = async (req, res) => {
  const session = await mongoose.startSession();
  const transactionOption = { ...transactionOptions };
  try {
    let reqData = req.body;

    reqData.account = req.userData.account;
    reqData.createdBy = req.userData._id;

    session.startTransaction(transactionOption);
    const responseData = await reportQuestionService.createReportQuestion(reqData, session);

    if (responseData) {
      await this.commonUpdateSyncApiManage(req.userData.account);
      return await this.createReportQuestionAnswers(reqData, responseData._id, res, session);
    }
  } catch (error) {
    await session.abortTransaction();
    res.status(500).json(errorResponse(error.message));
  } finally {
    session.endSession();
  }
};

/**
 * Create Report Questions Answers
 *
 * @param {*} requestData
 * @param {*} questionId
 * @param {*} res
 * @param {*} session
 * @returns
 */
exports.createReportQuestionAnswers = async (requestData, questionId, res, session) => {
  try {
    let prepareAnswerData = [];

    for (let answer in requestData.answers) {
      let prepareAns = {
        reportQuestion: questionId,
        report: requestData.report,
        account: requestData.account,
        createdBy: requestData.createdBy,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      for (let ans in requestData.answers[answer]) {
        if (
          !Array.isArray(requestData.answers[answer]['title']) ||
          requestData.answers[answer]['title'].length !=
            requestData.answers[answer]['numberOfAnswers']
        ) {
          await session.abortTransaction();
          return res.status(400).json(errorResponse(constantUtils.INCORRECT_ANSWER_TITLES));
        }
        prepareAns[ans] = requestData.answers[answer][ans];
      }
      prepareAnswerData.push(prepareAns);
    }

    // Insert Answers
    await reportQuestionAnswerService.insertManyReportQuestionAnswer(prepareAnswerData, session);

    await session.commitTransaction();

    // Update Question Weight
    await reportQuestionService.calculateQuestionWeightAndUpdate(requestData.report);

    return res.status(200).json(successResponse(constantUtils.CREATE_REPORT_QUESTION));
  } catch (error) {
    await session.abortTransaction();
    return res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Get All Parameter Types
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getParameterTypes = async (req, res) => {
  try {
    const parameterTypes = await parameterTypeService.getParameterTypes();
    return res.status(200).json(successResponse(constantUtils.GET_PARAMETER_TYPES, parameterTypes));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Get Report Question Answers
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getReportQuestionAnswers = async (req, res) => {
  try {
    let { reportId } = req.params;

    let page = req.query.page ?? '';
    let perPage = req.query.perPage ?? '';

    let filter = {
      report: toObjectId(reportId),
      account: req.userData.account,
      deletedAt: null,
    };
    const reportQuestionAnswers = await reportQuestionService.getQuestionAnswer(
      filter,
      page,
      perPage
    );
    return res
      .status(200)
      .json(successResponse(constantUtils.GET_REPORT_QUESTION_ANSWERS, reportQuestionAnswers[0]));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Update Report Question And Answer
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateReportQuestionAndAnswer = async (req, res) => {
  try {
    const { questionId } = req.params;
    let { answers } = req.body;
    let reqData = req.body;
    const userId = req.userData._id;

    const exist = await reportQuestionService.getSingleReportQuestion({
      _id: toObjectId(questionId),
      account: req.userData.account,
      deletedAt: null,
    });

    if (!exist) {
      return res.status(400).json(errorResponse(constantUtils.REPORT_QUESTION_NOT_EXIST));
    }

    if (exist?.report?.isPublish) {
      return res.status(400).json(errorResponse(constantUtils.REPORT_QUESTION_IS_PUBLISHED));
    }

    reqData?.answers && delete reqData.answers;
    reqData.updatedBy = userId;
    reqData.updatedAt = new Date();
    await reportQuestionService.updateReportQuestion(questionId, reqData);

    if (answers) {
      await this.questionAnswersOpration(answers, exist, userId);
    }

    //update question weight
    await reportQuestionService.calculateQuestionWeightAndUpdate(exist.report);

    await this.commonUpdateSyncApiManage(req.userData.account);

    return res.status(200).json(successResponse(constantUtils.UPDATE_REPORT_QUESTION_AND_ANSWER));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Report Question Answer Operation
 *
 * @param {*} answers
 * @param {*} questionData
 * @param {*} userId
 */
exports.questionAnswersOpration = async (answers, questionData, userId) => {
  for (const key of Object.keys(answers)) {
    switch (key) {
      case 'update':
        await this.handleUpdate(answers[key]);
        break;
      case 'create':
        for (let data of answers[key]) {
          if (Array.isArray(data.title) && data.title.length == data.numberOfAnswers) {
            data.reportQuestion = questionData._id;
            data.report = questionData.report;
            data.account = questionData.account;
            data.createdBy = userId;
            data.createdAt = new Date();
            data.updatedAt = new Date();
            await reportQuestionAnswerService.createReportQuestionAnswer(data);
          }
        }
        break;
      case 'delete':
        for (let data of answers[key]) {
          let getUserReportAns = await userReportAnswerService.getUserReportAnswers({
            reportQuestionAnswer: data,
          });

          const answerTitleIds = getUserReportAns.flatMap(element =>
            element.answers.map(answer => answer.answerTitleId)
          );

          answerTitleIds.forEach(async element => {
            await userReportAnswerService.updateUserReportAnswers(
              {
                'answers.answerTitleId': toObjectId(element),
              },
              {
                $set: { 'answers.$.isActive': false },
              }
            );
          });

          await reportQuestionAnswerService.updateReportQuestionAnswer(data, {
            deletedAt: new Date(),
            deletedBy: userId,
          });
        }
        break;
    }
  }
};

/**
 * Remove Report Question and releated answer types
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.removeReportQuestion = async (req, res) => {
  const session = await mongoose.startSession();
  const transactionOption = { ...transactionOptions };
  try {
    const { questionId } = req.params;

    const exist = await reportQuestionService.getSingleReportQuestion({
      _id: toObjectId(questionId),
      account: req.userData.account,
      deletedAt: null,
    });

    if (!exist) {
      return res.status(400).json(errorResponse(constantUtils.REPORT_QUESTION_NOT_EXIST));
    }

    if (exist?.report?.isPublish) {
      return res.status(400).json(errorResponse(constantUtils.REPORT_QUESTION_IS_PUBLISHED));
    }

    let deleteUpdate = {
      weight: 0,
      deletedAt: new Date(),
      deletedBy: req.userData._id,
    };

    session.startTransaction(transactionOption);
    const updateQuestion = await reportQuestionService.updateReportQuestion(
      questionId,
      deleteUpdate,
      session
    );

    if (updateQuestion) {
      await reportQuestionAnswerService.updateManyReportQuestionAnswer(
        { reportQuestion: questionId },
        {
          deletedAt: new Date(),
          deletedBy: req.userData._id,
        },
        session
      );

      await session.commitTransaction();
      session.endSession();

      // Update Question Weight
      await reportQuestionService.calculateQuestionWeightAndUpdate(exist.report);

      // sync for mobile if question deleted from web
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    return res.status(200).json(successResponse(constantUtils.DELETE_REPORT_QUESTION_AND_ANSWER));
  } catch (error) {
    await session.abortTransaction();
    session.endSession();
    res.status(500).json(errorResponse(error.message));
  }
};

exports.handleUpdate = async updateData => {
  for (let data of updateData) {
    let { id, title } = data;
    delete data.id;
    if (title) {
      delete data.title;
      await this.handleTitleUpdates(id, title);
    }
    await reportQuestionAnswerService.updateReportQuestionAnswer(id, data);
  }
};

exports.handleTitleUpdates = async (id, titles) => {
  for (let value of titles) {
    let { _id } = value;
    if (_id) {
      delete value._id;
      let setData = {};
      Object.keys(value).forEach(key => {
        setData[`title.$.${key}`] = value[key];
      });

      if (setData['title.$.isActive'] == false) {
        let getUserReportAns = await userReportAnswerService.getUserReportAnswers({
          reportQuestionAnswer: id,
          'answers.answerTitleId': toObjectId(_id),
          deletedAt: null,
        });

        const answerTitleIds = getUserReportAns.flatMap(element =>
          element.answers.filter(answer => answer.answerTitleId == _id)
        );
        answerTitleIds.forEach(async element => {
          await userReportAnswerService.updateUserReportAnswers(
            {
              'answers._id': element._id,
            },
            {
              $set: { 'answers.$.isActive': false },
            }
          );
        });
      }
      await reportQuestionAnswerService.updateQuestionAnswerTitle(id, _id, setData);
    } else {
      await reportQuestionAnswerService.addQuestionAnswerTitle(id, value);
    }
  }
};

/**
 * Update Db Migration
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateReportQuestionDb = async (req, res) => {
  try {
    const { filter, update } = req.body;

    const updateQuestion = await reportQuestionService.upDateReportQuestionDb(filter, update);

    if (updateQuestion) {
      return res.status(200).json(successResponse(constantUtils.DB_MIGRATION));
    }
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Update Db Migration
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateReportQuestionAnswerDb = async (req, res) => {
  try {
    const { filter, update } = req.body;

    const updateQuestionAnswer = await reportQuestionAnswerService.updateReportQuestionAnswerDb(
      filter,
      update
    );

    if (updateQuestionAnswer) {
      return res.status(200).json(successResponse(constantUtils.DB_MIGRATION));
    }
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

exports.commonUpdateSyncApiManage = async account => {
  await commonfunctionUtils.updateSyncApiManage({
    syncApis: ['reportNewFormConfig'],
    account,
  });
};
