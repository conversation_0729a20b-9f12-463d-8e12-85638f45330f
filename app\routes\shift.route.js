// library
const express = require('express');
const routes = express.Router();

// Validator
const validator = require('../validators/shift.validator');

// middleware
const { verifyToken, deletedAt, authAccount } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const shiftController = require('../controllers/shift.controller');

routes.get('/export-excel', verifyToken, authAccount, validate, shiftController.getShiftExcel);
// create shift
routes.post(
  '',
  verifyToken,
  authAccount,
  validator.shiftValidationRule(),
  validate,
  shiftController.createShift
);

// get all shifts
routes.get('', verifyToken, authAccount, validate, shiftController.getAllShifts);
routes.get('/count', verifyToken, authAccount, validate, shiftController.getAllShiftsCount);

// update shift
routes.patch(
  '/:id',
  verifyToken,
  deletedAt,
  validator.updateShiftValidationRule(),
  validate,
  shiftController.updateShift
);

// delete shift
routes.delete('/:id', verifyToken, deletedAt, validate, shiftController.deleteShift);

// Get All Shift Activites
routes.get('/activities', verifyToken, validate, shiftController.getAllShiftActivities);

// get shift-activities
routes.get('/:id/activities', verifyToken, validate, shiftController.getShiftActvityData);

// get single shift from aggregate filter
routes.get('/:id/single-shift', verifyToken, validate, shiftController.getSingleShiftById);

routes.get(
  '/:shiftId/export-pdf',
  verifyToken,
  authAccount,
  validate,
  shiftController.getShiftPDFDetails
);

module.exports = routes;
