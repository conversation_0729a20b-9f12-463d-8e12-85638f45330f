const dateFormat = require('dateformat');
const commonUtils = require('../app/utils/common.utils');
const orderUtils = require('../app/utils/order.utils');

exports.orderTemplate = async templateData => {
  let { companyPrimaryColor, requestData } = templateData;
  const projectTitle = await commonUtils.alterStringFromRequestString(
    requestData.project.projectNumber
      ? `${requestData.project.projectNumber} - ${requestData.project.title}`
      : requestData.project.title ?? 'N/A'
  );
  let receiverName = commonUtils.alterStringFromRequestString(
    `${requestData.createdBy.firstName} ${requestData.createdBy.lastName}`
  );
  let receiverContactNo = `${requestData.createdBy.contactNumber.in} ${requestData.createdBy.contactNumber.number}`;
  let senderName = commonUtils.alterStringFromRequestString(
    `${requestData.updatedBy.firstName} ${requestData.updatedBy.lastName}`
  );
  let senderContactNo = `${requestData.updatedBy.contactNumber.in} ${requestData.updatedBy.contactNumber.number}`;
  let createdAt = dateFormat(requestData.createdAt, 'dd-mm-yyyy');
  let updatedAt = dateFormat(requestData.updatedAt, 'dd-mm-yyyy');
  let orderFrom = dateFormat(requestData.fromDate, 'dd-mm-yyyy');
  let orderTo = dateFormat(requestData.toDate, 'dd-mm-yyyy');
  let orderStatus = orderUtils.orderStatus(requestData.status);
  let currencyWiseTotal = requestData.currencyTotal.join('| ');
  let getTableRows = this.generateTableRows(requestData.orderEquipment);
  return `
        <!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Details</title>
    <style>
        html {
            box-sizing: border-box;
        }

        *,
        *::before,
        *::after {
            box-sizing: inherit;
        }

        :root {
            --primary-color: ${companyPrimaryColor};
            --info-title-color: #4D5464;
            --info-desc-color: #333843;
            --status-color: #9D0202;
            --circle-bg-color: #D9D9D9;
            --circle-text-color: #000000;
            --table-header-border: #E0E6F51A;
            --total-amount: #CD6B35;
            --font-color: #323232;
        }

        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            font-family: Arial, sans-serif;
            width: 100%;
            height: 100%;
            font-size: 12px;
            font-weight: 500;
        }

        .main {
            width: 100%;
            box-sizing: border-box;
            page-break-after: always;
        }

        .header {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;

        }

        #header-text {
            font-size: 20px;
            font-weight: 600;
            color: var(--font-color);
        }

        .vertical-align {
            display: flex;
            flex-direction: column;
            justify-content: center;
            /* gap: 0; */
        }

        .vertical-align p {
            margin: 0;
            padding: 0;
        }
        .custom-table {
         margin-top: 20px;
         width: 100%;
         border-collapse: separate;
         border-spacing: 0;
        }
         .custom-table th {
            padding: 8px;
            text-align: left;
            border: 1px solid var(--table-header-border);
            padding-left: 10px;

        }

        .custom-table td {
            padding: 8px;
            text-align: left;
            border: 1px solid var(--circle-bg-color);
            padding: 10px;
        }

        .table-header th:first-child {
            border-top-left-radius: 10px;
        }

        .table-header th:last-child {
            border-top-right-radius: 10px;
        }

        .custom-table tbody tr:last-child td:first-child {
            border-bottom-left-radius: 10px;
        }

        .custom-table tbody tr:last-child td:last-child {
            border-bottom-right-radius: 10px;
        }
        .header-box1 {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            padding: 10px;
        }

        .header-box {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }

        .title {
            font-size: 12px;
            font-weight: 700;
            color: var(--font-color);
            margin: 0;
        }

        .answer {
            font-size: 12px;
            font-weight: 500;
            color: var(--font-color);
            margin: 0;
        }

        .table-header {
            background-color: var(--primary-color);
            color: var(--circle-bg-color);
        }

        .text-line {
            display: flex;
            align-items: center;
            margin-top: 20px;
        }

        .text-line p {
            margin: 0;
            padding-right: 10px;
            white-space: nowrap;
            font-size: 15px;
            font-weight: 600;
            color: var(--primary-color);
        }

        .text-line hr {
            flex-grow: 1;
            border: none;
            border-top: 1px solid var(--primary-color);
            margin: 0;
            margin-left: 5px;

        }

        .content-text {
            font-size: 12px;
            color: var(--font-color);
        }

        .header-title {
            font-size: 12px;
            font-weight: 600;
            color: #FFFFFF;
        }

        @media print {
            body {
                width: 100%;
                height: auto;
                display: block;
            }

            .main {
                width: 100%;
                max-width: 794px;
                margin: 0 auto;
                page-break-after: always;
            }

            .header {
                page-break-inside: avoid;
                display: flex;
                align-items: center;

            }

            .Status-section {
                page-break-inside: avoid;
            }

            .table-header {
                background-color: var(--primary-color) !important;
                color: var(--circle-bg-color);
            }
        }
    </style>
</head>

<body>
    <div class="main">
        <div class="header">
            <div class="vertical-align">
                <p id="header-text">Manifest Order #${requestData.orderNumber}</p>
            </div>
        </div>
        <div>
            <table class="custom-table">
                <thead>
                    <tr class="table-header">
                        <th>
                            <div class="header-box">
                                <p class="header-title">Project</p>
                            </div>
                        </th>
                        <th>
                            <div class="header-box">

                                <p class="header-title">Requested By</p>
                            </div>
                        </th>
                        <th>
                            <div class="header-box">
                                <p class="header-title">Date</p>
                            </div>

                        </th>
                        <th>
                            <div class="header-box">
                                <p class="header-title">Status</p>
                            </div>

                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <p class="answer">${projectTitle}</p>
                        </td>
                        <td>
                            <p class="answer">${senderName}</p>
                        </td>
                        <td>
                            <p class="answer">${createdAt}</p>
                        </td>
                        <td>
                            <p class="answer">${orderStatus}</p>
                        </td>
                    </tr>
                </tbody>
            </table>
            <table class="custom-table">
                <thead>
                    <tr class="table-header" >
                        <th >
                            <div class="header-box">
                                <p class="header-title">Request Items</p>
                            </div>
                        </th>
                        <th>
                            <div class="header-box">

                                <p class="header-title">Requested Quantity</p>
                            </div>
                        </th>
                        <th>
                            <div class="header-box">

                                <p class="header-title">Expected Period</p>
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <p class="answer">${requestData.requestedItems}</p>
                        </td>
                        <td>
                            <p class="answer">${requestData.requestedQuantity} Qty</p>
                        </td>
                        <td style="display: flex; justify-content: space-between;">
                            <div>
                                <p class="title">From</p>
                                <p class="answer">${orderFrom}</p>
                            </div>
                            <div>
                                <p class="title">To</p>
                                <p class="answer">${orderTo}</p>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>

            <div class="text-line">
                <p>Order Details</p>
                <hr>
            </div>
            <table class="custom-table">
                <thead>
                    <tr class="table-header" >
                        <th>
                            <div class="header-box">
                                <p class="header-title">No</p>
                            </div>
                        </th>
                        <th>
                            <div class="header-box">
                                <p class="header-title">Quantity</p>
                            </div>
                        </th>
                        <th >
                            <div class="header-box">
                                <p class="header-title">Equipment Type</p>
                            </div>
                        </th>
                        <th>
                            <div class="header-box">
                                <p class="header-title">Eqp. Name</p>
                            </div>
                        </th>
                        <th>
                            <div class="header-box">
                                <p class="header-title">Serial No.</p>
                            </div>
                        </th>
                        <th>
                            <div class="header-box">
                                <p class="header-title">HSE No.</p>
                            </div>
                        </th>
                        <th>
                            <div class="header-box">
                                <p class="header-title">Weight</p>
                            </div>
                        </th>
                        <th>
                            <div class="header-box">
                                <p class="header-title">Value</p>
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    ${getTableRows}
                </tbody>
            </table>
        </div>
        <table class="custom-table">
            <thead>
                <tr class="table-header">
                    <th>
                        <div class="header-box">
                            <p class="header-title">Details</p>
                        </div>
                    </th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>
                        <div style="display: flex; justify-content: space-between; margin-top: 10px;">
                            <p class="answer">Total Weight</p>
                            <p class="answer">${requestData.totalWeight}kg</p>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-top: 10px;">
                            <p class="answer">Total Value</p>
                            <p style="color:var(--total-amount); font-size:15px; font-weight:700; margin:0;">${currencyWiseTotal}</p>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
        <table class="custom-table" style="page-break-inside:avoid">
            <thead>
                <tr class="table-header">
                    <th colspan="3">
                        <div style="display: flex; justify-content: center; align-items: center;">
                            <p class="header-title">Sender</p>
                        </div>
                    </th>
                    <th colspan="3">
                        <div style="display: flex; justify-content: center; align-items: center;">
                            <p class="header-title">Reciver</p>
                        </div>
                    </th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>
                        <div>
                            <p class="title">Name</p>
                            <p class="answer">${senderName}</p>
                        </div>
                    </td>
                    <td>
                        <div>
                            <p class="title">Date</p>
                            <p class="answer">${updatedAt}</p>
                        </div>
                    </td>
                    <td>
                        <div>
                            <p class="title">Contact No.</p>
                            <p class="answer">${senderContactNo}</p>
                        </div>
                    </td>
                    <td>
                        <div>
                            <p class="title">Name</p>
                            <p class="answer">${receiverName}</p>
                        </div>
                    </td>
                    <td>
                        <div>
                            <p class="title">Date</p>
                            <p class="answer">${createdAt}</p>
                        </div>
                    </td>
                    <td>
                        <div>
                            <p class="title">Contact No.</p>
                            <p class="answer">${receiverContactNo}</p>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <div>
                            <p class="title">Signature</p>
                        </div>
                    </td>
                    <td colspan="2">
                        <div style="display: flex; justify-content: center; align-items: center;"></div>
                    </td>
                    <td>
                        <div>
                            <p class="title">Signature</p>
                        </div>
                    </td>
                    <td colspan="2">
                        <div style="display: flex; justify-content: center; align-items: center;"></div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</body>

</html>
      `;
};

exports.generateTableRows = tableData => {
  let rows = '';
  let count = 0;
  for (let data of tableData) {
    count++;
    let serialNumber = data.serialNumber ?? '-';
    rows += `
        <tr class="content-text">
            <td>${count}</td>
            <td>${data.quantity}</td>
            <td>${data.equipmentType}</td>
            <td>${data.equipmentName}</td>
            <td>${serialNumber}</td>
            <td>${data.hsCode}</td>
            <td>${data.weight}</td>
            <td>${data.value}</td>
        </tr>
        `;
  }
  return rows;
};
