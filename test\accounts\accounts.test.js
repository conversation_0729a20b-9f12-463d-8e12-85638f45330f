const request = require('supertest');
const app = require('../../app/server');
const { DEFAULT_PASSWORD } = process.env;
// create accounts
describe('POST /api/accounts', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.6O0ROXpENOlmf3PIz45n_UBxGoCb0DMSKVKKCMPLP6w';
  const accountData = {
    name: 'PLI Energy',
    accountOwner: '',
    createdBy: '641170af8e0d16ff25a1b0d0',
    updatedBy: '641170af8e0d16ff25a1b0d0',
    firstName: 'Wout',
    lastName: 'Dijkstra',
    email: '<EMAIL>',
    contactNumber: '+***********',
    emergencyContactNumber: '+***********',
    password: DEFAULT_PASSWORD,
    role: ['superadmin'],
    isActive: true,
    logo: 'https://via.placeholder.com/300.png/09f/fff',
  };
  it('returns 200 and message Account has been created successfully', async () => {
    const response = await request(app)
      .post('/api/accounts')
      .set('Authorization', `Bearer ${token}`)
      .send(accountData);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Account has been created successfully',
      status: true,
    });
  });

  it('returns 422 with message Required field cannot be empty.', async () => {
    const response = await request(app)
      .post('/api/accounts')
      .set('Authorization', `Bearer ${token}`)
      .send(accountData);
    expect(response.status).toBe(422);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Required field cannot be empty.',
      status: false,
    });
  });

  it('returns 401 with message Email Id already exists', async () => {
    const response = await request(app)
      .post('/api/accounts')
      .set('Authorization', `Bearer ${token}`)
      .send(accountData);
    expect(response.status).toBe(401);
    expect(response.body).toEqual({
      message: 'Email Id already exists',
      status: false,
    });
  });

  it('returns 401 with message Account already exist', async () => {
    const response = await request(app)
      .post('/api/accounts')
      .set('Authorization', `Bearer ${token}`)
      .send(accountData);
    expect(response.status).toBe(401);
    expect(response.body).toEqual({
      message: 'Account already exist',
      status: false,
    });
  });

  it('returns 401 with message Please send authentication token.', async () => {
    const response = await request(app).post('/api/accounts').send(accountData);
    expect(response.status).toBe(401);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});

// getAll accounts
describe('GET /api/accounts', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.6O0ROXpENOlmf3PIz45n_UBxGoCb0DMSKVKKCMPLP6w';

  it('returns 200 and message get all accounts List', async () => {
    const response = await request(app)
      .get('/api/accounts')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);

    expect(response.body).toMatchObject({
      data: [{}],
      message: 'Account has been retireved successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).get('/api/accounts');
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});

//get account by id
describe('GET /api/accounts/:id', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.6O0ROXpENOlmf3PIz45n_UBxGoCb0DMSKVKKCMPLP6w';
  const id = '6414300ba36fd62d42b08193';

  it('returns 200 and message Account has been retireved successfully', async () => {
    const response = await request(app)
      .get(`/api/accounts/${id}`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Account has been retireved successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).get(`/api/accounts/${id}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });

  it('returns 404 with message Account does not exist', async () => {
    const response = await request(app)
      .get('/api/accounts/64119ddc6c1d88fde480fd40')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(404);
    expect(response.body).toEqual({
      message: 'Account does not exist',
      status: false,
    });
  });
});

// get licence by account id

describe('GET /api/accounts/:id/licence', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.6O0ROXpENOlmf3PIz45n_UBxGoCb0DMSKVKKCMPLP6w';
  const id = '6414300ba36fd62d42b08193';

  it('returns 200 and message Records has been retireved successfully', async () => {
    const response = await request(app)
      .get(`/api/accounts/${id}/licence`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Records has been retireved successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).get(`/api/accounts/${id}/licence`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });

  it('returns 404 with message No licence and permission exists', async () => {
    const response = await request(app)
      .get('/api/accounts/64119ddc6c1d88fde480fd40/licence')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(404);
    expect(response.body).toEqual({
      message: 'No licence and permission exists',
      status: false,
    });
  });
});
