const logbooksService = require('../services/logbooks.service');
const HTTP_STATUS = require('../utils/status-codes');
const { successResponse, errorResponse } = require('../utils/response.utils');
const constantUtils = require('../utils/constants.utils');

/**
 * Create logbook
 *
 * @param {*} req
 * @param {*} res
 */
exports.createLogbook = async (req, res) => {
  try {
    const { project, description, user } = req.body;
    const requestedData = {
      user,
      project,
      description,
      createdBy: req.userData.id,
    };

    await logbooksService.createLogbook(requestedData);
    res.status(HTTP_STATUS.CREATED).json(successResponse(constantUtils.LOGBOOK_CREATED));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(errorResponse(error.message));
  }
};

/**
 * Get all logbook
 *
 * @param {*} req
 * @param {*} res
 */
exports.getAllLogbooks = async (req, res) => {
  try {
    const sortOrder = req.query.sortOrder || 'desc';
    const sortBy = req.query.sortBy || 'createdAt';

    const filters = {
      user: req.params.user,
      deletedAt: null,
    };

    if (req.query.project && req.query.project !== 'all') {
      filters.project = req.query.project;
    }

    const result = await logbooksService.getAllLogbooks(filters, sortOrder, sortBy);

    res.status(HTTP_STATUS.OK).json(successResponse(constantUtils.LOGBOOK_FETCHED, result));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(errorResponse(error.message));
  }
};

/**
 * Update logbook
 *
 * @param {*} req
 * @param {*} res
 */
exports.updateLogbook = async (req, res) => {
  try {
    const logbookId = req.params.id;

    const logbook = await logbooksService.getLogbookById(logbookId);
    if (!logbook)
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(errorResponse(constantUtils.LOGBOOK_NOT_FOUND));

    const updateData = {
      ...req.body,
      updatedBy: req.userData.id,
    };

    await logbooksService.updateLogbookById(logbookId, updateData);
    res.status(HTTP_STATUS.OK).json(successResponse(constantUtils.LOGBOOK_UPDATED));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(errorResponse(error.message));
  }
};

/**
 * Delete logbook
 *
 * @param {*} req
 * @param {*} res
 */
exports.deleteLogbook = async (req, res) => {
  try {
    const logbook = await logbooksService.getLogbookById(req.params.id);
    if (!logbook)
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(errorResponse(constantUtils.LOGBOOK_NOT_FOUND));

    await logbooksService.updateLogbookById(req.params.id, req.deletedAt);
    res.status(HTTP_STATUS.OK).json(successResponse(constantUtils.LOGBOOK_DELETED));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(errorResponse(error.message));
  }
};
