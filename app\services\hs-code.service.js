const HSCode = require('../models/hs-code.model');

/**
 * Create HSCode
 *
 * @param {*} requestData
 * @returns
 */
exports.createHSCode = async requestData => {
  return await HSCode.create(requestData);
};

/**
 * Filter EquipmentCategories
 *
 * @param {*} filter
 * @param {*} perPage
 * @param {*} page
 * @param {*} sort
 * @returns
 */
exports.getHSCode = async (filter, page, perPage, sort) => {
  const query = HSCode.find(filter, {
    deletedAt: 0,
    deletedBy: 0,
    __v: 0,
  })
    .sort({ createdAt: sort ?? -1 })
    .populate([
      {
        path: 'account',
        select: { _id: 1, name: 1 },
        strictPopulate: false,
      },
      {
        path: 'createdBy updatedBy deletedBy',
        model: 'user',
        select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      },
    ]);

  if (page !== '' && perPage !== '') {
    query
      .limit(perPage)
      .skip(page * perPage)
      .sort({ createdAt: sort ?? -1 });
  }

  return await query;
};

/**
 * Get HSCode by Id
 *
 * @param {*} id
 * @returns
 */
exports.getHSCodeById = async id => {
  return await HSCode.findOne({ _id: id, deletedAt: null, isActive: true }).populate([
    {
      path: 'account',
      select: { _id: 1, name: 1 },
      strictPopulate: false,
    },
    {
      path: 'createdBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
    {
      path: 'updatedBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
    {
      path: 'deletedBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
  ]);
};

/**
 * Update HSCode
 *
 * @param {*} id
 * @param {*} requestData
 * @returns
 */
exports.updateHSCode = async (id, requestData) => {
  return await HSCode.findByIdAndUpdate(id, { $set: requestData }, { new: true });
};

/**
 * Delete HSCode
 *
 * @param {*} id
 * @param {*} deletedAt
 * @returns
 */
exports.deleteHSCode = async (id, deletedAt) => {
  return await HSCode.findByIdAndUpdate(id, { $set: deletedAt }, { new: true });
};
