const Member = require('../models/member.model');

/**
 * Run the migration
 *
 * @returns
 */
exports.up = async () => {
  try {
    const result = await Member.updateMany(
      { rotation: { $exists: false } },
      { $set: { rotation: '' } }
    );
    console.log(`Successfully migrated ${result.modifiedCount} records`);
    return true;
  } catch (error) {
    console.error('Error in rotation seeder', error);
  }
};

/**
 * Rollback the migration
 *
 * @returns
 */
exports.down = async () => {
  try {
    await Member.updateMany({ rotation: { $exists: true } }, { $unset: { rotation: 1 } });
  } catch (error) {
    console.error('Error in rotation down seeder', error);
  }
};
