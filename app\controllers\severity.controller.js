require('dotenv').config();

// Services
const severityService = require('../services/severity.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');

/**
 * Get All Severity
 *
 * @param {*} req
 * @param {*} res
 */
exports.getAllSeverity = async (req, res) => {
  try {
    const severityList = await severityService.getAllSeverity();
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.ALL_SEVERITY_LIST, severityList));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};
