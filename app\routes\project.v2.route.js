// library
const express = require('express');
const routes = express.Router();

// middleware
const { verifyToken, authAccount } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const projectController = require('../controllers/project.controller');

// Project List
routes.get('', verifyToken, authAccount, validate, projectController.getAllProjectList);

module.exports = routes;
