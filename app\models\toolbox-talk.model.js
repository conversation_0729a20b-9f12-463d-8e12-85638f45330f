const mongoose = require('mongoose');

const ToolboxTalk = mongoose.Schema(
  {
    project: {
      type: mongoose.Types.ObjectId,
      ref: 'project',
    },
    location: {
      type: mongoose.Types.ObjectId,
      ref: 'location',
    },
    team: {
      type: mongoose.Types.ObjectId,
      ref: 'team',
    },
    note: {
      type: String,
      default: '',
    },
    photos: {
      type: Array,
      default: [],
    },
    memberSignature: [
      {
        user: {
          type: mongoose.Types.ObjectId,
          ref: 'user',
        },
        signature: {
          type: String,
          default: null,
        },
        signatureAt: {
          type: Date,
          default: new Date(),
        },
      },
    ],
    hostedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    createdBySignature: {
      type: String,
      default: null,
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('toolbox-talk', ToolboxTalk);
