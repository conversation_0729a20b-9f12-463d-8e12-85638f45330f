const EquipmentUnit = require('../models/equipment-unit.model');

/**
 * Create EquipmentUnit
 *
 * @param {*} requestData
 * @returns
 */
exports.createEquipmentUnit = async requestData => {
  return await EquipmentUnit.create(requestData);
};

/**
 * Filter EquipmentUnits
 *
 * @param {*} filter
 * @param {*} perPage
 * @param {*} page
 * @param {*} sort
 * @returns
 */
exports.getEquipmentUnit = async (filter, page, perPage, sort) => {
  return await EquipmentUnit.find(filter, {
    deletedAt: 0,
    deletedBy: 0,
    __v: 0,
  })
    .sort({ createdAt: sort ?? -1 })
    .limit(perPage)
    .skip(page * perPage)
    .populate([
      {
        path: 'account',
        select: { _id: 1, name: 1 },
        strictPopulate: false,
      },
      {
        path: 'createdBy',
        model: 'user',
        select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      },
      {
        path: 'updatedBy',
        model: 'user',
        select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      },
      {
        path: 'deletedBy',
        model: 'user',
        select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      },
    ]);
};

/**
 * Get EquipmentUnit by Id
 *
 * @param {*} id
 * @returns
 */
exports.getEquipmentUnitById = async id => {
  return await EquipmentUnit.findOne({ _id: id, deletedAt: null, isActive: true }).populate([
    {
      path: 'account',
      select: { _id: 1, name: 1 },
      strictPopulate: false,
    },
    {
      path: 'createdBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
    {
      path: 'updatedBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
    {
      path: 'deletedBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
  ]);
};

/**
 * Update EquipmentUnit
 *
 * @param {*} id
 * @param {*} requestData
 * @returns
 */
exports.updateEquipmentUnit = async (id, requestData) => {
  return await EquipmentUnit.findByIdAndUpdate(id, { $set: requestData }, { new: true });
};

/**
 * Delete EquipmentUnit
 *
 * @param {*} id
 * @param {*} deletedAt
 * @returns
 */
exports.deleteEquipmentUnit = async (id, deletedAt) => {
  return await EquipmentUnit.findByIdAndUpdate(id, { $set: deletedAt }, { new: true });
};
