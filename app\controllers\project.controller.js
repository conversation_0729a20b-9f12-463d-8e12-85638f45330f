// Responses
const { successResponse, errorResponse } = require('../utils/response.utils');

// constants
const constants = require('../utils/constants.utils');

// Services
const projectServices = require('../services/project.service');
const locationService = require('../services/location.service');
const memberService = require('../services/member.service');
const functionService = require('../services/function.service');
const assetService = require('../services/asset.service');
const projectStringService = require('../services/project-string.service');
const scopeService = require('../services/scope.service');
const activityService = require('../services/activity.service');
const teamService = require('../services/team.service');
const shiftService = require('../services/shift.service');
const reportTypeService = require('../services/report-type.service');
const safetyCardService = require('../services/safety-card.service');
const reportService = require('../services/report.service');
const shiftActivityService = require('../services/shift-activity.service');
const profileFunctionService = require('../services/profile-function.service');
const userService = require('../services/user.service');
const certificateService = require('../services/certificate.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const { generateUserWiseZip } = require('../utils/export-zip.utils');
const commonUtils = require('../utils/common.utils');
const commonFunctionsUtils = require('../utils/common-function.utils');
const { getTrainingMatrixExcel } = require('../utils/export-excel.util');
const HTTP_STATUS = require('../utils/status-codes');

/**
 * Create New Project
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.createProject = async (req, res) => {
  try {
    const { title, standByTypes, status, projectNumber, client } = req.body;
    let reqData = {};
    const newReq = {
      ...reqData,
      title,
      account: req.userData.account,
      projectNumber,
      client,
      defaultIdentifier: global.constant.NORMAL_DATA_IDENTIFIER,
      standByTypes,
      status,
    };

    const exist = await projectServices.getProjectByName(title, req.userData.account, false);

    if (exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.PROJECT_EXIST));
    }

    const createdProject = await projectServices.createProject(newReq);

    // update sync api manage data
    if (createdProject) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }

    return res.status(200).json(successResponse(constants.CREATE_PROJECT, createdProject));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Update New Project
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateProject = async (req, res) => {
  try {
    let id = req.params.id;
    const { title, status, projectNumber, client } = req.body;

    const exist = await projectServices.getProjectById(id, req.userData.account);
    const isActive = Object.hasOwn(req.body, 'isActive') ? req.body.isActive : exist.isActive;
    let reqData = {};
    const newReq = {
      ...reqData,
      title,
      projectNumber,
      client,
      isActive,
      account: req.userData.account,
    };

    if (status) {
      newReq.status = status;
    }

    if (!exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_PROJECT));
    }
    const updatedProject = await projectServices.updateProject(id, newReq);

    // update sync api manage data
    if (updatedProject) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }

    return res.status(200).json(successResponse(constants.UPDATE_PROJECT, updatedProject));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Get All Projects
 *
 * @param {*} req
 * @param {*} res
 */
exports.getAllProjects = async (req, res) => {
  try {
    let filterData;
    if (req.query.isActive !== undefined) {
      filterData = {
        account: req.userData.account,
        isActive: req.query.isActive === 'true',
        deletedAt: null,
        isDefault: false, // show all the project which is not isDefault
      };
    } else {
      filterData = {
        account: req.userData.account,
        deletedAt: null,
        isDefault: false,
      };
    }

    if (
      ![global.constant.ADMIN_ROLE, global.constant.SUPER_ADMIN_ROLE].includes(
        req.userData.role.title
      )
    ) {
      let searchData = {
        ...(req.userData.role.isAssignAllProjects ? {} : { user: req.userData._id }),
        account: req.userData.account,
        deletedAt: null,
      };

      const projectList = await commonUtils.getAssignedProjectList(
        req.userData.role.isAssignAllProjects,
        searchData
      );

      if (projectList.length > 0) {
        filterData._id = { $in: projectList };
      }
    }

    let { status } = req.query;

    filterData = {
      ...filterData,
      ...(status && status !== 'all' && { status: { $in: status.split(',') } }),
    };

    const projectList = await projectServices.getAllProjects(filterData, req.query.sort);

    res.status(HTTP_STATUS.OK).json(successResponse(constants.ALL_PROJECT, projectList));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(errorResponse(error.message));
  }
};

/**
 * Get All Project List with ProjectNumber - Title
 *
 * @param {*} req
 * @param {*} res
 */
exports.getAllProjectList = async (req, res) => {
  try {
    let filterData;
    if (req.query.isActive !== undefined) {
      filterData = {
        account: req.userData.account,
        isActive: req.query.isActive === 'true',
        deletedAt: null,
        isDefault: false, // show all the project which is not isDefault
      };
    } else {
      filterData = {
        account: req.userData.account,
        deletedAt: null,
        isDefault: false,
      };
    }

    if (
      ![global.constant.ADMIN_ROLE, global.constant.SUPER_ADMIN_ROLE].includes(
        req.userData.role.title
      )
    ) {
      let searchData = {
        ...(req.userData.role.isAssignAllProjects ? {} : { user: req.userData._id }),
        account: req.userData.account,
        deletedAt: null,
      };

      const projectList = await commonUtils.getAssignedProjectList(
        req.userData.role.isAssignAllProjects,
        searchData
      );

      const projectIds = projectList.map(project => project._id);

      if (projectList.length > 0) {
        filterData._id = { $in: projectIds };
      }
    }

    let { status } = req.query;

    filterData = {
      ...filterData,
      ...(status && status !== 'all' && { status: { $in: status.split(',') } }),
    };

    const projects = await projectServices.getAllProject(filterData, req.query.sort);

    res.status(HTTP_STATUS.OK).json(successResponse(constants.ALL_PROJECT, projects));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(errorResponse(error.message));
  }
};

/**
 * Delete By Id
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.deleteProject = async (req, res) => {
  try {
    let id = req.params.id;
    const exist = await projectServices.getProjectById(id, req.userData.account);

    if (!exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_PROJECT));
    }

    const response = await projectServices.deleteProject(id, req.deletedAt);

    await locationService.deleteAllProjectLocations(id, req.deletedAt);
    // project location
    await assetService.deleteAllProjectAsset(id, req.deletedAt);
    // project asset
    await activityService.deleteAllProjectActivity(id, req.deletedAt);
    // project activity
    await teamService.deleteAllProjectTeam(id, req.deletedAt);
    // project team
    await functionService.deleteAllProjectFunction(id, req.deletedAt);
    // project string
    await projectStringService.deleteAllProjectString(id, req.deletedAt);
    // scope
    await scopeService.deleteAllProjectScope(id, req.deletedAt);
    // members
    await memberService.deleteAllProjectMember(id, req.deletedAt);
    // safety card
    await safetyCardService.deleteAllProjectSafetyCard(id, req.deletedAt);
    // report-type
    await reportTypeService.deleteAllProjectReport(id, req.deletedAt);
    // report
    await reportService.deleteAllReport(id, req.deletedAt);
    // get shift id by project id
    const filterBody = {
      project: commonUtils.toObjectId(id),
      account: commonUtils.toObjectId(req.userData.account),
      deletedAt: null,
    };
    const shiftId = await shiftService.getShiftByProjectId(filterBody);
    // shift
    await shiftService.deleteAllProjectShift(id, req.deletedAt);
    // shift activity
    await shiftActivityService.deleteAllProjectShiftActivity(shiftId, req.deletedAt);

    // update sync api manage data
    if (response) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }

    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.DELETE_PROJECT, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get location by project id
 *
 * @param {*} req
 * @param {*} res
 */
exports.getLocationsByProjectId = async (req, res) => {
  try {
    let project = req.params.id;
    if (!commonUtils.isValidId(project)) {
      return res.status(400).json(errorResponse(constantUtils.INVALID_PROJECT_ID));
    }
    let { account } = req.userData;
    const { page, perPage, sort, filterData } = await commonFunctionsUtils.getSearchAndSort(
      req,
      project,
      account,
      'title'
    );
    const locationsList = await locationService.getAllLocation(filterData, page, perPage, sort);

    res.status(200).json(successResponse(constants.LIST_LOCATION, locationsList));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Get member by project id
 *
 * @param {*} req
 * @param {*} res
 */

exports.getMembersByProjectId = async (req, res) => {
  try {
    let project = req.params.id;
    if (!commonUtils.isValidId(project)) {
      return res.status(400).json(errorResponse(constantUtils.INVALID_PROJECT_ID));
    }
    let page = req.query.page ? Number(req.query.page) : null;
    let perPage = req.query.perPage ? Number(req.query.perPage) : null;
    let sortOrder;
    let sortOrderField;
    if (!req.query.sortOrder) {
      sortOrder = req.query.sort === 'asc' ? 1 : -1;
      sortOrderField = false;
    } else {
      sortOrderField = true;
      sortOrder = req.query.sortOrder === 'asc' ? 1 : -1;
    }

    const filterData = {
      account: req.userData.account,
      project: commonUtils.toObjectId(project),
      deletedAt: null,
    };
    let memberList = await memberService.getMemberByProjectId(
      filterData,
      sortOrderField,
      sortOrder,
      page,
      perPage,
      req.query.name
    );

    res.status(200).json(responseUtils.successResponse(constantUtils.ALL_MEMBER_LIST, memberList));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get function by project id
 *
 * @param {*} req
 * @param {*} res
 */
exports.getFunctionsByProjectId = async (req, res) => {
  try {
    let project = req.params.id;
    if (!commonUtils.isValidId(project)) {
      return res.status(400).json(errorResponse(constantUtils.INVALID_PROJECT_ID));
    }
    let { account } = req.userData;
    const { page, perPage, sort, filterData } = await commonFunctionsUtils.getSearchAndSort(
      req,
      project,
      account,
      'functionName',
      'sortOrder'
    );
    const functionList = await functionService.getAllFunction(filterData, page, perPage, sort);

    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.ALL_FUNCTION_LIST, functionList));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get asset by project id
 *
 * @param {*} req
 * @param {*} res
 */
exports.getAssetsByProjectId = async (req, res) => {
  try {
    let project = req.params.id;
    if (!commonUtils.isValidId(project)) {
      return res.status(400).json(errorResponse(constantUtils.INVALID_PROJECT_ID));
    }
    let { account } = req.userData;
    const { page, perPage, sort, filterData } = await commonFunctionsUtils.getSearchAndSort(
      req,
      project,
      account,
      'cableName'
    );
    const assetList = await assetService.getAllAsset(filterData, page, perPage, sort);
    res.status(200).json(responseUtils.successResponse(constantUtils.ALL_ASSET_LIST, assetList));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get projectString by project id
 *
 * @param {*} req
 * @param {*} res
 */
exports.getProjectStringsByProjectId = async (req, res) => {
  try {
    const projectId = req.params.id;
    if (!commonUtils.isValidId(projectId)) {
      return res.status(400).json(errorResponse(constantUtils.INVALID_PROJECT_ID));
    }
    let { account } = req.userData;
    const { page, perPage, sort, filterData } = await commonFunctionsUtils.getSearchAndSort(
      req,
      projectId,
      account,
      'name'
    );

    const projectStringData = await projectStringService.getProjectStringByProjectId(
      filterData,
      page,
      perPage,
      sort
    );

    res
      .status(200)
      .json(
        responseUtils.successResponse(constantUtils.ALL_PROJECT_STRING_LIST, projectStringData)
      );
  } catch (err) {
    res.status(500).json(responseUtils.errorResponse(err.message));
  }
};

/**
 * Get scope by project id
 *
 * @param {*} req
 * @param {*} res
 */
exports.getScopeByProjectId = async (req, res) => {
  try {
    const projectId = req.params.id;
    if (!commonUtils.isValidId(projectId)) {
      return res.status(400).json(errorResponse(constantUtils.INVALID_PROJECT_ID));
    }
    let { account } = req.userData;
    // Get search and sort data from request
    const { page, perPage, sort, filterData } = await commonFunctionsUtils.getSearchAndSort(
      req,
      projectId,
      account,
      'name',
      'sortOrder'
    );

    const scopeData = await scopeService.getScopeByProjectId(filterData, page, perPage, sort);

    res.status(200).json(responseUtils.successResponse(constantUtils.ALL_SCOPE_LIST, scopeData));
  } catch (err) {
    res.status(500).json(responseUtils.errorResponse(err.message));
  }
};

/**
 * Get activity by project id
 *
 * @param {*} req
 * @param {*} res
 */
exports.getActivityByProjectId = async (req, res) => {
  try {
    const projectId = req.params.id;
    if (!commonUtils.isValidId(projectId)) {
      return res.status(400).json(errorResponse(constantUtils.INVALID_PROJECT_ID));
    }
    let { account } = req.userData;
    const { page, perPage, sort, filterData } = await commonFunctionsUtils.getSearchAndSort(
      req,
      projectId,
      account,
      'name',
      'sortOrder'
    );

    const activityData = await activityService.getActivityByProjectId(
      filterData,
      page,
      perPage,
      sort
    );

    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.ALL_ACTIVITY_LIST, activityData));
  } catch (err) {
    res.status(500).json(responseUtils.errorResponse(err.message));
  }
};

/**
 * Get teams by project id
 *
 * @param {*} req
 * @param {*} res
 */
exports.getTeamsByProjectId = async (req, res) => {
  try {
    const projectId = req.params.id;

    let { account } = req.userData;
    const { page, perPage, sort, filterData } = await commonFunctionsUtils.getSearchAndSort(
      req,
      projectId,
      account,
      'teamsWfmName',
      'sortOrder'
    );
    const teamData = await teamService.getTeamByProjectId(filterData, page, perPage, sort);

    res.status(200).json(responseUtils.successResponse(constantUtils.ALL_TEAM_LIST, teamData));
  } catch (err) {
    res.status(500).json(responseUtils.errorResponse(err.message));
  }
};

/**
 * Get All details from project
 *
 * @param {*} req
 * @param {*} res
 */
exports.getAllProjectsDetails = async (req, res) => {
  try {
    let { expire } = req.query;
    const rotation = req.query.rotation && req.query.rotation !== 'all' ? req.query.rotation : null;

    const filterData = {
      account: req.userData.account,
      deletedAt: null,
    };

    let response;
    let trainingMatrix;

    if (req.query.project && req.query.project !== 'all') {
      filterData.project = req.query.project;
      let functionsData;
      let functionIds;

      if (req.query.functions === 'all') {
        /*  if function all is provided in the query params */
        functionsData = await functionService.getAllFunction(filterData);
        functionIds = functionsData.map(data => data._id);
      } else if (req.query.functions) {
        /* if specific function id is provided in the query params */
        functionsData = await functionService.getAllFunction(filterData);
        functionIds = req.query.functions.split(',');
      } else {
        /*  if function is not provided in the query params */
        return res
          .status(200)
          .json(responseUtils.successResponse(constantUtils.PROJECT_TRANING_MATRICS, []));
      }

      delete filterData.project;

      filterData.function = { $in: functionIds };
      let memberIds;
      let memberData;

      if (req.query.user && req.query.user === 'all') {
        /*  if user all is provided in the query params */
        memberData = await memberService.getAllMember({
          ...filterData,
          ...(rotation && { rotation: rotation }),
        });
        memberIds = memberData.map(data => data.user?._id);
      } else {
        /*  if specific user is provided in the query params */
        filterData.user = commonUtils.toObjectId(req.query.user);
        memberData = await memberService.getAllMember({
          ...filterData,
          ...(rotation && { rotation: rotation }),
        });
        memberIds = [commonUtils.toObjectId(req.query.user)];
        delete filterData.user;
      }

      delete filterData.function;
      filterData.user = { $in: memberIds };

      let filter = {
        account: req.userData.account,
        user: { $in: memberIds },
        status: 'approved',
        ...(rotation && { rotation: rotation }),
        deletedAt: null,
        isActive: true,
      };
      filter = await this.checkAndAddFilterForExpireCertificates(expire, filter);
      delete filterData.user;

      trainingMatrix = await projectServices.getAllProjectsDetails(
        filter,
        functionsData,
        memberData
      );

      let requiredFunctions = [];

      trainingMatrix.map(func => requiredFunctions.push(func[Object.keys(func)[0]].id));

      let functionFilter = {
        account: req.userData.account,
        function: { $in: requiredFunctions },
        deletedAt: null,
      };

      let certificates = await certificateService.getAllCertificate(functionFilter);

      let requiredCertificate = certificates.reduce((result, requiredData) => {
        let certificateWithFunction = requiredData.certificates.map(certificate => ({
          function: requiredData.function,
          certificate,
        }));

        return result.concat(certificateWithFunction);
      }, []);

      response = { trainingMatrix, requiredCertificate };
    } else {
      let filter = {
        account: req.userData.account,
        deletedAt: null,
      };
      let profileFunctions = await profileFunctionService.getAllProfileFunction(filter, '', '', 1);
      delete filter.profileFunctions;
      let profileFunctionIds = profileFunctions.map(profileFunction => profileFunction._id);
      filter.profileFunction = { $in: profileFunctionIds };

      let userProfileFunctionData = await userService.getUserProfileFunction({
        account: req.userData.account,
        profileFunction: { $in: profileFunctionIds },
        deletedAt: null,
      });

      let userIds = userProfileFunctionData.map(user => user._id);
      let filterData = {
        ...(rotation && { rotation: rotation }),
        account: req.userData.account,
        user: { $in: userIds },
        status: 'approved',
        deletedAt: null,
        isActive: true,
      };

      filterData = await this.checkAndAddFilterForExpireCertificates(expire, filterData);

      trainingMatrix = await projectServices.getProjectsDetails(
        filterData,
        profileFunctions,
        userProfileFunctionData
      );
      response = { trainingMatrix, requiredCertificate: [] };
    }

    // export zip
    if (req.query?.export && req.query?.export === 'true') {
      const projectDetail =
        !('project' in req.query) || req.query.project == 'all'
          ? 'all'
          : await projectServices.getProjectById(req.query.project, req.userData.account);
      let folderName =
        projectDetail === 'all' ? 'Certificate_all' : `Certificate_${projectDetail.title}`;
      folderName = folderName.replace(/\s/g, '_'); // replace spaces with underscore
      return await generateUserWiseZip(response, res, folderName);
    }

    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.PROJECT_TRANING_MATRICS, response));
  } catch (error) {
    return res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Check And Add Filter For Expire Certificates
 *
 * @param {*} expireVal
 * @param {*} filterData
 * @returns
 */
exports.checkAndAddFilterForExpireCertificates = async (expireVal, filterData) => {
  try {
    if (expireVal && expireVal !== 'all') {
      switch (expireVal) {
        case 'certificate_expired':
          filterData.endDate = { $lt: new Date() };
          break;
        case 'certificate_expire_in_30':
          filterData.endDate = {
            $gte: new Date(),
            $lt: new Date(new Date().setDate(new Date().getDate() + 30)),
          };
          break;
        case 'certificate_expire_in_60':
          filterData.endDate = {
            $gte: new Date(),
            $lt: new Date(new Date().setDate(new Date().getDate() + 60)),
          };
          break;
      }
    }
  } catch (error) {
    console.error(error);
  }
  return filterData;
};

/**
 * Get Project By Approver
 *
 * @param {*} req
 * @param {*} res
 */
exports.getProjectsByApprover = async (req, res) => {
  try {
    let projectFilter;
    if (
      req.userData.role.title == global.constant.ADMIN_ROLE ||
      req.userData.role.title == global.constant.SUPER_ADMIN_ROLE
    ) {
      projectFilter = {
        account: req.userData.account,
        deletedAt: null,
      };
    } else {
      let projectIds = [];
      if (req.userData.role.isAssignAllProjects) {
        const allProjects = await projectServices.getAllProjects({
          account: req.userData.account,
          deletedAt: null,
        });
        if (allProjects.length > 0) {
          projectIds = allProjects.map(project => project._id);
        }
      } else {
        let filter = {
          account: req.userData.account,
          user: req.userData._id,
          isApprover: true,
          deletedAt: null,
        };

        const assignedProjects = await memberService.getAllMember(filter);
        projectIds = assignedProjects.map(data => commonUtils.toObjectId(data.project._id));
      }
      projectFilter = {
        account: req.userData.account,
        _id: { $in: projectIds },
        deletedAt: null,
        isDefault: false, // show project list for return cart project dropdown of mobile
      };
    }

    let { status } = req.query;
    projectFilter = {
      ...projectFilter,
      ...(status && { status: { $in: status.split(',') } }), // check project status
    };

    let projects = await projectServices.getProjectsByApprover(projectFilter);

    res.status(200).json(responseUtils.successResponse(constantUtils.ALL_PROJECT, projects));
  } catch (error) {
    return res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Get Location Asset Data By Project Id
 *
 * @param {*} req
 * @param {*} res
 */
exports.getLocationAssetDataByProjectId = async (req, res) => {
  try {
    const { id } = req.params;
    let filter = {
      account: req.userData.account,
      project: commonUtils.toObjectId(id),
      deletedAt: null,
    };
    const getLocations = await locationService.getLocationWhichHasAssets(filter);

    res.status(200).json(successResponse(constantUtils.LIST_LOCATION, getLocations));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Get training matrix excel
 *
 * @param {*} req
 * @param {*} res
 */
exports.getTrainingMatrixExcel = async (req, res) => {
  try {
    let { expire } = req.query;
    const accountFilterData = {
      account: req.userData.account,
      deletedAt: null,
    };
    const filterData = accountFilterData;
    let { personalDetails, contractualDetails } = req.body;

    if (contractualDetails?.drivingLicence == true) {
      delete contractualDetails.drivingLicence;
      personalDetails = { ...personalDetails, drivingLicence: true };
    }
    if (contractualDetails?.seamansBook == true) {
      delete contractualDetails.seamansBook;
      personalDetails = { ...personalDetails, seamansBook: true };
    }

    if (contractualDetails !== undefined) {
      if (
        Object.hasOwn(contractualDetails, 'passport') &&
        Object.hasOwn(contractualDetails, 'secondaryPassport')
      ) {
        delete contractualDetails.passport;
        delete contractualDetails.secondaryPassport;

        contractualDetails = {
          ...contractualDetails,
          passport: true,
          passportIssueDate: true,
          passportExpiryDate: true,
          secondaryPassport: true,
          secondaryPassportIssueDate: true,
          secondaryPassportExpiryDate: true,
        };
      } else if (Object.hasOwn(contractualDetails, 'passport')) {
        delete contractualDetails.passport;
        contractualDetails = {
          ...contractualDetails,
          passport: true,
          passportIssueDate: true,
          passportExpiryDate: true,
        };
      } else if (Object.hasOwn(contractualDetails, 'secondaryPassport')) {
        delete contractualDetails.secondaryPassport;
        contractualDetails = {
          ...contractualDetails,
          secondaryPassport: true,
          secondaryPassportIssueDate: true,
          secondaryPassportExpiryDate: true,
        };
      }

      if (
        Object.hasOwn(contractualDetails, 'healthInsurance') &&
        Object.hasOwn(contractualDetails, 'liabilityInsurance')
      ) {
        delete contractualDetails.healthInsurance;
        delete contractualDetails.liabilityInsurance;

        contractualDetails = {
          ...contractualDetails,
          healthInsurance: true,
          healthInsuranceIssueDate: true,
          healthInsuranceExpiryDate: true,
          liabilityInsurance: true,
          liabilityInsuranceIssueDate: true,
          liabilityInsuranceExpiryDate: true,
        };
      } else if (Object.hasOwn(contractualDetails, 'healthInsurance')) {
        delete contractualDetails.healthInsurance;
        contractualDetails = {
          ...contractualDetails,
          healthInsurance: true,
          healthInsuranceIssueDate: true,
          healthInsuranceExpiryDate: true,
        };
      } else if (Object.hasOwn(contractualDetails, 'liabilityInsurance')) {
        delete contractualDetails.liabilityInsurance;
        contractualDetails = {
          ...contractualDetails,
          liabilityInsurance: true,
          liabilityInsuranceIssueDate: true,
          liabilityInsuranceExpiryDate: true,
        };
      }
    }

    let response;
    let trainingMatrix;

    if (req.query.project && req.query.project !== 'all') {
      filterData.project = req.query.project;
      let functionsData;
      let functionIds;

      if (req.query.functions === 'all') {
        /*  if function all is provided in the query params */
        functionsData = await functionService.getAllFunction(filterData);
        functionIds = functionsData.map(data => data._id);
      } else if (req.query.functions) {
        /* if specific function id is provided in the query params */
        functionsData = await functionService.getAllFunction(filterData);
        functionIds = req.query.functions.split(',');
      } else {
        /*  if function is not provided in the query params */
        return res
          .status(200)
          .json(responseUtils.successResponse(constantUtils.PROJECT_TRANING_MATRICS, []));
      }

      delete filterData.project;

      filterData.function = { $in: functionIds };
      let memberIds;
      let memberData;

      if (req.query.user && req.query.user === 'all') {
        /*  if user all is provided in the query params */
        memberData = await memberService.getAllMember(filterData);

        memberIds = memberData.map(data => data.user?._id);
      } else {
        /*  if specific user is provided in the query params */
        filterData.user = commonUtils.toObjectId(req.query.user);

        memberData = await memberService.getAllMember(filterData);

        memberIds = [commonUtils.toObjectId(req.query.user)];
        delete filterData.user;
      }

      delete filterData.function;
      filterData.user = { $in: memberIds };

      let filter = {
        ...accountFilterData,
        user: { $in: memberIds },
        status: 'approved',
        isActive: true,
      };

      filter = await this.checkAndAddFilterForExpireCertificates(expire, filter);

      delete filterData.user;

      trainingMatrix = await projectServices.getAllProjectsDetails(
        filter,
        functionsData,
        memberData
      );

      let requiredFunctions = [];

      trainingMatrix.map(func => requiredFunctions.push(func[Object.keys(func)[0]].id));

      let functionFilter = {
        ...accountFilterData,
        function: { $in: requiredFunctions },
      };

      let certificates = await certificateService.getAllCertificate(functionFilter);

      let requiredCertificate = certificates.reduce((result, requiredData) => {
        let certificateWithFunction = requiredData.certificates.map(certificate => ({
          function: requiredData.function,
          certificate,
        }));

        return result.concat(certificateWithFunction);
      }, []);

      response = { trainingMatrix, requiredCertificate };
    } else {
      let filter = { ...accountFilterData };

      let profileFunctions = await profileFunctionService.getAllProfileFunction(filter, '', '', 1);

      delete filter.profileFunctions;

      let profileFunctionIds = profileFunctions.map(profileFunction => profileFunction._id);
      filter.profileFunction = { $in: profileFunctionIds };

      let userProfileFunctionData = await userService.getUserProfileFunction({
        ...accountFilterData,
        profileFunction: { $in: profileFunctionIds },
      });

      let userIds = userProfileFunctionData.map(user => user._id);
      let filterData = {
        ...accountFilterData,
        user: { $in: userIds },
        status: 'approved',
        isActive: true,
      };

      filterData = await this.checkAndAddFilterForExpireCertificates(expire, filterData);

      trainingMatrix = await projectServices.getProjectsDetails(
        filterData,
        profileFunctions,
        userProfileFunctionData
      );
      response = { trainingMatrix, requiredCertificate: [] };
    }

    await getTrainingMatrixExcel(
      response,
      res,
      'training_matrix',
      personalDetails,
      contractualDetails,
      req.query.project,
      req.userData.account,
      req.userTimezone
    );
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

exports.commonUpdateSyncApiManage = async account => {
  await commonFunctionsUtils.updateSyncApiManage({
    syncApis: ['mainConfig', 'projects', 'toolboxConfig', 'reportConfig'],
    account,
  });
};
