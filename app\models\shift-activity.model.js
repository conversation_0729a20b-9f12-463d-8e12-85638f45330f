const mongoose = require('mongoose');

const shiftActivity = new mongoose.Schema(
  {
    activity: {
      type: mongoose.Types.ObjectId,
      ref: 'activity',
    },
    location: {
      type: mongoose.Types.ObjectId,
      ref: 'location',
    },
    endTime: {
      type: String,
      default: null,
    },
    completed: {
      type: Number,
      default: null,
    },
    comments: {
      type: String,
    },
    shift: {
      type: mongoose.Types.ObjectId,
      ref: 'shift',
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
    isDeleted: {
      type: Boolean,
      default: false,
    },
  },
  { timestamps: true }
);

shiftActivity.index({ shift: 1, deletedAt: 1 });

module.exports = mongoose.model('shift-activity', shiftActivity);
