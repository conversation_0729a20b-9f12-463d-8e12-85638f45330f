// library
const express = require('express');
const routes = express.Router();

// middleware
const { verifyToken, authAccount, deletedAt } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const categoryController = require('../controllers/category.controller');

// Validator
const validator = require('../validators/category.validator');

// create action
routes.post(
  '',
  verifyToken,
  authAccount,
  validator.categoryValidationRule(),
  validate,
  categoryController.createCategory
);

// get all action
routes.get('', verifyToken, authAccount, validate, categoryController.getAllCategory);

// update action
routes.patch(
  '/:id',
  verifyToken,
  validator.updateCategoryValidationRule(),
  validate,
  categoryController.updateCategory
);

// delete action
routes.delete('/:id', verifyToken, deletedAt, validate, categoryController.deleteCategory);

module.exports = routes;
