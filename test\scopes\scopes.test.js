const request = require('supertest');
const app = require('../../app/server');

// create scopes
describe('POST /api/scopes', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.I513fTkTfDgrekZVxyysoPEtU5Rmtr3nM-7HB7Hj7hE';
  const scopeData = {
    name: 'Scope 1',
    isDoable: false,
    normDuration: 'testing',
    sortOrder: 'testing123',
    project: '64119ddc6c1d88fde480fd47',
  };

  it('returns 200 and message Scope has been created successfully', async () => {
    const response = await request(app)
      .post('/api/scopes')
      .set('Authorization', `Bearer ${token}`)
      .send(scopeData);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Scope has been created successfully',
      status: true,
    });
  });

  it('returns 422 with message Required field cannot be empty.', async () => {
    const response = await request(app)
      .post('/api/scopes')
      .set('Authorization', `Bearer ${token}`)
      .send(scopeData);
    expect(response.status).toBe(422);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Required field cannot be empty.',
      status: false,
    });
  });

  it('returns 404 with message Scope already exist', async () => {
    const response = await request(app)
      .post('/api/scopes')
      .set('Authorization', `Bearer ${token}`)
      .send(scopeData);
    expect(response.status).toBe(404);
    expect(response.body).toEqual({
      message: 'Scope already exist',
      status: false,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).post('/api/scopes').send(scopeData);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});

// getAll scopes
describe('GET /api/scopes', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.I513fTkTfDgrekZVxyysoPEtU5Rmtr3nM-7HB7Hj7hE';

  it('returns 200 and message Scope has been retireved successfully', async () => {
    const response = await request(app).get('/api/scopes').set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: [{}],
      message: 'Scope has been retireved successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).get('/api/scopes');
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});

//  update scopes
describe('PATCH /api/scopes/:id', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.I513fTkTfDgrekZVxyysoPEtU5Rmtr3nM-7HB7Hj7hE';
  const id = '64182d6daf85666cf96d18dd';
  const updateData = {
    name: 'Scope 45',
    isDoable: true,
    normDuration: 'testing',
    sortOrder: 'testing456',
    project: '64119ddc6c1d88fde480fd47',
  };
  it('returns 200 and message Scope has been updated successfully', async () => {
    const response = await request(app)
      .patch(`/api/scopes/${id}`)
      .set('Authorization', `Bearer ${token}`)
      .send(updateData);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Scope has been updated successfully',
      status: true,
    });
  });

  it('returns 422 with message Required field cannot be empty.', async () => {
    const response = await request(app)
      .post(`/api/scopes/${id}`)
      .set('Authorization', `Bearer ${token}`)
      .send();
    expect(response.status).toBe(422);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Required field cannot be empty.',
      status: false,
    });
  });

  it('returns 404 with message No Scope Found', async () => {
    const response = await request(app)
      .post('/api/scopes/6412a388268e9b25cef57a30')
      .set('Authorization', `Bearer ${token}`)
      .send(updateData);
    expect(response.status).toBe(404);
    expect(response.body).toMatchObject({
      message: 'No Scope Found',
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).patch(`/api/scopes/${id}`).send(updateData);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});

// get list of scope by project
describe('GET /api/scopes/list/:projectId', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.I513fTkTfDgrekZVxyysoPEtU5Rmtr3nM-7HB7Hj7hE';
  const projectId = '64119ddc6c1d88fde480fd47';

  it('returns 200 and message Scope has been retireved successfully', async () => {
    const response = await request(app)
      .get(`/api/scopes/list/${projectId}`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Scope has been retireved successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).get(`/api/scopes/list/${projectId}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });

  it('returns 404 with message No Scope Found', async () => {
    const response = await request(app)
      .get('/api/scopes/list/64119ddc6c1d88fde480fd40')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(404);
    expect(response.body).toEqual({
      message: 'No Scope Found',
      status: false,
    });
  });
});

//  Delete scopesById
describe('DELETE /api/scopes/:id', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.I513fTkTfDgrekZVxyysoPEtU5Rmtr3nM-7HB7Hj7hE';
  const id = '64182e81283ff5998c31620c';

  it('returns 200 and message Scope has been deleted successfully', async () => {
    const response = await request(app)
      .delete(`/api/scopes/${id}`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Scope has been deleted successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).delete(`/api/scopes/${id}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });

  it('returns 404 with message No Scope Found', async () => {
    const response = await request(app)
      .delete('/api/scopes/6412b7a6644aa8e810eb73fb')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(404);
    expect(response.body).toEqual({
      message: 'No Scope Found',
      status: false,
    });
  });
});
