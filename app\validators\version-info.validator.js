const { body, constantUtils } = require('../validators/parent.validator');

exports.createVersionInfoValidationRule = () => {
  return [
    body('platform').isString().notEmpty().withMessage(constantUtils.INVALID_PLATFORM),
    body('versionCode').isNumeric().notEmpty().withMessage(constantUtils.INVALID_VERSION_CODE),
    body('versionName').isString().notEmpty().withMessage(constantUtils.INVALID_VERSION_NAME),
    body('packageName').isString().notEmpty().withMessage(constantUtils.INVALID_PACKAGE_NAME),
  ];
};

exports.checkVersionInfoValidationRule = () => {
  return [
    body('platform').isString().notEmpty().withMessage(constantUtils.INVALID_PLATFORM),
    body('versionCode').isNumeric().notEmpty().withMessage(constantUtils.INVALID_VERSION_CODE),
    body('versionName').isString().notEmpty().withMessage(constantUtils.INVALID_VERSION_NAME),
  ];
};
