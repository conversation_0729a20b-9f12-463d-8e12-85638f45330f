const { body, constantUtils } = require('../validators/parent.validator');

exports.createPMOrderValidationRule = () => {
  return [
    body('project').notEmpty().withMessage(constantUtils.PM_PROJECT_REQUIRED),
    body('equipmentType').isArray().notEmpty().withMessage(constantUtils.PM_EQUIPMENT_TYPE_DATA),
  ];
};

exports.validateUpdateLinkedEquipment = () => {
  return [
    body('quantity')
      .isNumeric()
      .notEmpty()
      .withMessage(constantUtils.QUANTITY_REQUIRED)
      .custom(value => value > 0),
  ];
};
