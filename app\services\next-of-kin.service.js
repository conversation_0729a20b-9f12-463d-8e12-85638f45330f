const NextOfKin = require('../models/next-of-kin.model');

/**
 * Create Next Of Kin
 *
 * @param {*} request
 * @returns
 */
exports.create = async request => {
  return await NextOfKin.create(request);
};

/**
 * Update Next Of Kin
 *
 * @param {*} request
 * @returns
 */
exports.update = async (id, data) => {
  return await NextOfKin.updateOne({ _id: id }, { $set: data });
};

/**
 * Delete Next Of Kin
 *
 * @param {*} request
 * @returns
 */
exports.delete = async (id, deletedAt) => {
  return await NextOfKin.updateOne({ user: id }, { $set: deletedAt });
};

exports.getKinByUserId = async id => {
  return await NextOfKin.find({ user: id });
};
