const mongoose = require('mongoose');

const serialNumberOrderHistory = new mongoose.Schema(
  {
    serialNumberId: {
      type: mongoose.Types.ObjectId,
      ref: 'equipment-serial-number',
    },
    pmOrderId: {
      type: mongoose.Types.ObjectId,
      ref: 'pm-order-request',
    },
    project: {
      type: mongoose.Types.ObjectId,
      ref: 'project',
    },
    equipment: {
      type: mongoose.Types.ObjectId,
      ref: 'equipment',
    },
    wmCheckOutDate: {
      type: Date,
      default: null,
    },
    pmCheckInDate: {
      type: Date,
      default: null,
    },
    pmCheckOutDate: {
      type: Date,
      default: null,
    },
    wmCheckInDate: {
      type: Date,
      default: null,
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('serial-number-order-history', serialNumberOrderHistory);
