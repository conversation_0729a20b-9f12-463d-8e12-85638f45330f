require('dotenv').config();

// Services
const likelihoodService = require('../services/likelihood.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');

/**
 * Get All Likelihood
 *
 * @param {*} req
 * @param {*} res
 */
exports.getAllLikelihood = async (req, res) => {
  try {
    const likelihoodList = await likelihoodService.getAllLikelihood();
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.ALL_LIKELIHOOD_LIST, likelihoodList));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};
