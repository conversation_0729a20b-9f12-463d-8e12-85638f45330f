const shoppingCart = require('../models/shopping-cart.model');

/**
 * Create Shopping Cart
 *
 * @param {*} requestData
 * @param {*} session
 * @returns
 */
exports.createShoppingCart = async (requestData, session) => {
  return await shoppingCart.create([{ ...requestData }], { session });
};

/**
 * Get Shopping Cart
 *
 * @param {*} filter
 * @returns
 */
exports.getShoppingCart = async filter => {
  return await shoppingCart.find(filter);
};

/**
 * Get Shopping Cart
 *
 * @param {*} filter
 * @returns
 */
exports.getShoppingCartByFilter = async filter => {
  return await shoppingCart.findOne(filter).populate([
    {
      path: 'project',
      model: 'project',
      select: 'title _id projectNumber',
      strictPopulate: false,
    },
  ]);
};

/**
 * Update Shopping Cart
 *
 * @param {*} filter
 * @param {*} updateData
 * @returns
 */
exports.updateShoppingCart = async (filter, updateData) => {
  return await shoppingCart.updateOne(filter, updateData);
};

/**
 * Update Shopping Cart By Id
 *
 * @param {*} id
 * @param {*} updateData
 * @returns
 */
exports.updateShoppingCartById = async (id, updateData) => {
  return await shoppingCart.findByIdAndUpdate(id, updateData);
};
