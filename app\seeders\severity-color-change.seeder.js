const Severity = require('../models/severity.model');

exports.up = async () => {
  try {
    const result = await Severity.updateOne({ title: 'Severe' }, { color: '944E4E' });
    console.log(result);
  } catch (error) {
    console.error(error);
  }
};

exports.down = async () => {
  try {
    const result = await Severity.updateOne({ title: 'Severe' }, { color: '9D0202' });
    console.log(result);
  } catch (error) {
    console.error(error);
  }
};
