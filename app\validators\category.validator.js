const { body, constantUtils } = require('../validators/parent.validator');

exports.categoryValidationRule = () => {
  return [
    body('categoryName')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.SELECT_VALID_NAME)
      .isLength({ max: global.constant.QHSE_CATEGORY_LIMIT })
      .withMessage(constantUtils.INVALID_CATEGORY_LENGTH),
  ];
};

exports.updateCategoryValidationRule = () => {
  return [
    body('categoryName')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.SELECT_VALID_NAME)
      .isLength({ max: global.constant.QHSE_CATEGORY_LIMIT })
      .withMessage(constantUtils.INVALID_CATEGORY_LENGTH)
      .optional({ checkFalsy: false }),
  ];
};
