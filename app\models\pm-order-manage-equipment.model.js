const mongoose = require('mongoose');

const PmOrderManageEquipment = new mongoose.Schema(
  {
    pmOrder: {
      type: mongoose.Types.ObjectId,
      default: 'pm-order',
    },
    pmComments: [
      {
        user: {
          type: mongoose.Types.ObjectId,
          ref: 'user',
        },
        time: {
          type: Date,
          default: null,
        },
        status: {
          type: String,
          default: '',
        },
        comment: {
          type: String,
          default: '',
        },
      },
    ],
    equipmentType: {
      type: mongoose.Types.ObjectId,
      ref: 'equipment-type',
    },
    pmRequestedQuantity: {
      type: Number,
      default: 0,
    },
    wmApprovedQuantity: {
      type: Number,
      default: 0,
    },
    wmComments: [
      {
        user: {
          type: mongoose.Types.ObjectId,
          ref: 'user',
        },
        time: {
          type: Date,
          default: null,
        },
        status: {
          type: String,
          default: '',
        },
        comment: {
          type: String,
          default: '',
        },
      },
    ],
    wmDispatchQuantity: {
      type: Number,
      default: 0,
    },
    pmReceivedQuantity: {
      type: Number,
      default: 0,
    },
    pmDispatchQuantity: {
      type: Number,
      default: 0,
    },
    wmReceivedQuantity: {
      type: Number,
      default: 0,
    },
    status: {
      type: String,
      enum: [
        'open',
        'requested',
        'approved',
        'rejected',
        'partially-pre-transit',
        'pre-transit',
        'in-transit',
        'check-in',
        'check-out',
        'pre-check-out',
        'partially-check-in',
        'partially-check-out',
        'partially-in-stock',
        'in-stock',
        'missing',
      ],
      default: 'open',
    },
    equipment: [
      {
        type: mongoose.Types.ObjectId,
        ref: 'equipment',
      },
    ],
    reason: {
      type: String,
      default: null,
    },
    receivedStatus: {
      type: String,
      enum: ['', 'ok', 'quarantine', 'write-off', 'missing', 'transfer', 'received'],
      default: '',
    },
    checkoutStatus: {
      type: String,
      enum: ['', 'ok', 'transfer', 'received', 'quarantine', 'write-off'],
      default: '',
    },
    checkoutReasonStatus: {
      type: String,
      enum: ['', 'repair-required', 'certification-required', 'damaged', 'missing', 'other'],
      default: '',
    },
    checkoutReason: {
      type: String,
      default: null,
    },
    checkinReasonStatus: {
      type: String,
      enum: ['', 'repair-required', 'certification-required', 'damaged', 'missing', 'other'],
      default: '',
    },
    checkinReason: {
      type: String,
      default: null,
    },
    sendingOffQuantity: {
      type: Number,
      default: 0,
    },
    sendingOffStatus: {
      type: String,
      enum: ['', 'ok', 'transfer', 'received', 'quarantine', 'write-off'],
      default: '',
    },
    sendingOffReasonStatus: {
      type: String,
      enum: ['', 'repair-required', 'certification-required', 'damaged', 'missing', 'other'],
      default: '',
    },
    sendingOffReason: {
      type: String,
      default: null,
    },
    fromPeriod: {
      type: Date,
      default: null,
    },
    toPeriod: {
      type: Date,
      default: null,
    },
    remark: [
      {
        user: {
          type: mongoose.Types.ObjectId,
          ref: 'user',
        },
        time: {
          type: Date,
          default: null,
        },
        status: {
          type: String,
          default: '',
        },
        comment: {
          type: String,
          default: '',
        },
      },
    ],
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('pm-order-manage-equipment', PmOrderManageEquipment);
