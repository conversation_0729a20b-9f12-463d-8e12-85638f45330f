const mongoose = require('mongoose');

const EquipmentCertificateType = new mongoose.Schema(
  {
    title: {
      type: String,
      required: true,
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    isValidityDate: {
      type: Boolean,
      default: false,
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('equipment-certificate-type', EquipmentCertificateType);
