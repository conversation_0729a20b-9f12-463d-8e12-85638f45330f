const EquipmentCategory = require('../models/equipment-category.model');

/**
 * Create EquipmentCategory
 *
 * @param {*} requestData
 * @returns
 */
exports.createEquipmentCategory = async requestData => {
  return await EquipmentCategory.create(requestData);
};

/**
 * Filter EquipmentCategories
 *
 * @param {*} filter
 * @param {*} perPage
 * @param {*} page
 * @param {*} sort
 * @returns
 */
exports.getEquipmentCategory = async (filter, page, perPage, sort) => {
  return await EquipmentCategory.find(filter, {
    deletedAt: 0,
    deletedBy: 0,
    __v: 0,
  })
    .sort(sort)
    .limit(perPage)
    .skip(page * perPage)
    .populate([
      {
        path: 'account',
        select: { _id: 1, name: 1 },
        strictPopulate: false,
      },
      {
        path: 'createdBy',
        model: 'user',
        select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      },
      {
        path: 'updatedBy',
        model: 'user',
        select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      },
      {
        path: 'deletedBy',
        model: 'user',
        select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      },
    ]);
};

/**
 * Get EquipmentCategory by Id
 *
 * @param {*} id
 * @returns
 */
exports.getEquipmentCategoryById = async id => {
  return await EquipmentCategory.findOne({ _id: id, deletedAt: null, isActive: true }).populate([
    {
      path: 'account',
      select: { _id: 1, name: 1 },
      strictPopulate: false,
    },
    {
      path: 'createdBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
    {
      path: 'updatedBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
    {
      path: 'deletedBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
  ]);
};

/**
 * Update EquipmentCategory
 *
 * @param {*} id
 * @param {*} requestData
 * @returns
 */
exports.updateEquipmentCategory = async (id, requestData) => {
  return await EquipmentCategory.findByIdAndUpdate(id, { $set: requestData }, { new: true });
};

/**
 * Delete EquipmentCategory
 *
 * @param {*} id
 * @param {*} deletedAt
 * @returns
 */
exports.deleteEquipmentCategory = async (id, deletedAt) => {
  return await EquipmentCategory.findByIdAndUpdate(id, { $set: deletedAt }, { new: true });
};

/**
 * Get Single EquipmentCategory
 *
 * @param {*} filter
 * @returns
 */
exports.getSingleEquipmentCategoryByFilter = async filter => {
  return await EquipmentCategory.findOne(filter);
};
