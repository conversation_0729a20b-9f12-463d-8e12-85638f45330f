// library
const express = require('express');
const routes = express.Router();

// Validator
const validator = require('../validators/toolbox-talk.validator');

// middleware
const { verifyToken, authAccount, deletedAt } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const toolboxTalkController = require('../controllers/toolbox-talk.controller');

// Create Toolbox Talk
routes.post(
  '',
  verifyToken,
  authAccount,
  validator.createToolboxTalkValidationRule(),
  validate,
  toolboxTalkController.createToolboxTalk
);

// Delete Toolbox Talk by Id
routes.delete(
  '/:id',
  verifyToken,
  authAccount,
  deletedAt,
  validate,
  toolboxTalkController.deleteToolboxTalk
);

// Get Toolbox Talk
routes.get('', verifyToken, authAccount, validate, toolboxTalkController.getToolboxTalks);

// Get Toolbox Talk by Id
routes.get('/:id', verifyToken, authAccount, validate, toolboxTalkController.getToolboxTalkById);

// Get Toolbox talk pdf
routes.get(
  '/:toolboxId/export-pdf',
  verifyToken,
  authAccount,
  validate,
  toolboxTalkController.getToolboxTalkPDFDetails
);

module.exports = routes;
