// library
const express = require('express');
const routes = express.Router();

// middleware
const { verifyToken, deletedAt, authAccount } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const activityController = require('../controllers/activity.controller');

// Validator
const validator = require('../validators/activity.validator');

// create activity
routes.post(
  '',
  verifyToken,
  authAccount,
  validator.activityValidationRule(),
  validate,
  activityController.createActivity
);

// update activity
routes.patch(
  '/:id',
  verifyToken,
  authAccount,
  validator.updateActivityValidationRule(),
  validate,
  activityController.updateActivity
);

// delete activity
routes.delete('/:id', verifyToken, deletedAt, validate, activityController.deleteActivity);

module.exports = routes;
