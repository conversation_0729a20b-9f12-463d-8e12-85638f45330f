const cryptoRandomString = require('crypto-random-string');

/* models */
const EquipmentCategory = require('../models/equipment-category.model');
const CurrencyUnit = require('../models/currency-unit.model');
const EquipmentUnit = require('../models/equipment-unit.model');
const EquipmentQuantityType = require('../models/equipment-quantity-type.model');
const HSCode = require('../models/hs-code.model');
const EquipmentType = require('../models/equipment-type.model');

/**
 * Check and get temporary equipment setup data
 *
 * @param {*} account
 * @returns
 */
exports.checkAndGetTempEquipmentSetup = async account => {
  const filter = { isTemporary: true, account, deletedAt: null };
  let equipmentCategory = await EquipmentCategory.findOne(filter).select('_id');
  let currencyUnit = await CurrencyUnit.findOne(filter).select('_id');
  let equipmentUnit = await EquipmentUnit.findOne(filter).select('_id');
  let equipmentQuantityType = await EquipmentQuantityType.findOne(filter).select('_id');
  let hsCode = await HSCode.findOne(filter).select('_id');

  let equipmentSetupData = {
    equipmentCategory: equipmentCategory ? equipmentCategory._id : null,
    currencyUnit: currencyUnit ? currencyUnit._id : null,
    equipmentUnit: equipmentUnit ? equipmentUnit._id : null,
    equipmentQuantityType: equipmentQuantityType ? equipmentQuantityType._id : null,
    hsCode: hsCode ? hsCode._id : null,
  };

  const hasNullValue = Object.values(equipmentSetupData).some(value => value === null);
  if (hasNullValue) {
    return false;
  }

  return equipmentSetupData;
};

/**
 * Create temporary equipment type
 *
 * @param {*} requestData
 * @param {*} setupData
 * @returns
 */
exports.createTempEquipmentType = async (requestData, setupData) => {
  const equipmentTypeName = await this.generateTempEquipmentName(requestData, requestData.account);

  const equipmentType = await EquipmentType.create({
    type: equipmentTypeName,
    equipmentCategory: setupData.equipmentCategory,
    equipmentUnit: setupData.equipmentUnit,
    currencyUnit: setupData.currencyUnit,
    price: 0,
    quantityType: setupData.equipmentQuantityType,
    hsCode: setupData.hsCode,
    isActive: true,
    isTemporary: true,
    account: requestData.account,
    createdBy: requestData.user,
    createdAt: new Date(),
  });

  return equipmentType._id;
};

/**
 * Generate temporary equipment name
 *
 * @param {*} requestData
 * @param {*} account
 * @returns
 */
exports.generateTempEquipmentName = async (requestData, account) => {
  const tempPrefix = global.constant.TEMP_EQUIPMENT_TYPE_PRE_FIX;
  const randomNumber = cryptoRandomString({ length: 6, type: 'numeric' });
  const tempName = `${tempPrefix}${randomNumber}`;
  const equipmentTypeName = `${requestData.productName} - ${tempName}`;

  const equipmentTypeExists = await EquipmentType.findOne({
    type: equipmentTypeName,
    account,
    isTemporary: true,
    deletedAt: null,
  });

  if (equipmentTypeExists) {
    return await this.generateTempEquipmentName(requestData, account);
  }

  return equipmentTypeName;
};

/**
 * Create temporary equipment setup
 *
 * @param {*} reuqestData
 */
exports.createTempEquipmentSetup = async reuqestData => {
  try {
    let defaultParams = {
      isTemporary: true,
      account: reuqestData.account,
      createdBy: reuqestData.user,
      createdAt: new Date(),
    };

    // create equipment category data
    await EquipmentCategory.create({
      name: 'Temporary Category',
      abbreviation: 'TMP',
      isActive: true,
      ...defaultParams,
    });

    // create currency unit data
    await CurrencyUnit.create({
      name: 'Temp',
      symbol: 'TMP',
      ...defaultParams,
    });

    // create equipment unit data
    await EquipmentUnit.create({
      title: 'Temp Unit',
      abbreviation: 'TU',
      isActive: true,
      ...defaultParams,
    });

    // create equipment quantity type data
    await EquipmentQuantityType.create({
      name: 'Temporary Quantity Type',
      abbreviation: 'TQT',
      priceType: 'buy',
      quantityType: 'multiple',
      isActive: true,
      ...defaultParams,
    });

    // create hs code data
    await HSCode.create({
      name: 'Temporary HS Code',
      code: '-',
      isActive: true,
      ...defaultParams,
    });
  } catch (error) {
    // Log error to the console for debugging, not to stop the application
    console.log(error);
  }
};
