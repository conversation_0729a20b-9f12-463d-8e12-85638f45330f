const EquipmentImageCertificate = require('../models/equipment-image-certificate.model');

/**
 * Create EquipmentImageCertificate
 *
 * @param {*} requestData
 * @returns
 */
exports.createEquipmentImageCertificate = async requestData => {
  return await EquipmentImageCertificate.create(requestData);
};

/**
 * Filter EquipmentImageCertificate
 *
 * @param {*} filter
 * @param {*} perPage
 * @param {*} page
 * @param {*} sort
 * @returns
 */
exports.getEquipmentImageCertificate = async (filter, perPage, page, sort) => {
  return await EquipmentImageCertificate.find(filter, {
    deletedAt: 0,
    deletedBy: 0,
    __v: 0,
  })
    .sort({ createdAt: sort })
    .limit(perPage)
    .skip(page * perPage)
    .populate([
      {
        path: 'equipment',
        select: { _id: 1, name: 1 },
      },
      {
        path: 'account',
        select: { _id: 1, name: 1 },
        strictPopulate: false,
      },
      {
        path: 'createdBy',
        model: 'user',
        select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      },
      {
        path: 'updatedBy',
        model: 'user',
        select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      },
      {
        path: 'deletedBy',
        model: 'user',
        select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      },
    ]);
};

/**
 * Get EquipmentImageCertificate by Filter
 *
 * @param {*} id
 * @returns
 */
exports.getSingleEquipmentImageCertificateByFilter = async filter => {
  filter.deletedAt = null;
  filter.isActive = true;
  return await EquipmentImageCertificate.findOne(filter, {
    deletedAt: 0,
    deletedBy: 0,
    __v: 0,
  }).populate([
    {
      path: 'equipment',
      select: { _id: 1, name: 1 },
    },
    {
      path: 'account',
      select: { _id: 1, name: 1 },
      strictPopulate: false,
    },
    {
      path: 'createdBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
    {
      path: 'updatedBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
    {
      path: 'deletedBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
  ]);
};

/**
 * Update EquipmentImageCertificate
 *
 * @param {*} id
 * @param {*} requestData
 * @returns
 */
exports.updateEquipmentImageCertificate = async (id, requestData) => {
  return await EquipmentImageCertificate.findByIdAndUpdate(
    id,
    { $set: requestData },
    { new: true }
  );
};

/**
 * Delete EquipmentImageCertificate
 *
 * @param {*} id
 * @param {*} deletedAt
 * @returns
 */
exports.deleteEquipmentImageCertificate = async (id, deletedAt) => {
  return await EquipmentImageCertificate.findByIdAndUpdate(id, { $set: deletedAt }, { new: true });
};
