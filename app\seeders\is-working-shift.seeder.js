const TeamMember = require('../models/team-members.model');

/**
 * Add isWorking and status fields in team member if they don't exist
 */
exports.up = async () => {
  await TeamMember.updateMany(
    {
      $or: [{ isWorking: { $exists: false } }, { status: { $exists: false } }],
    },
    [
      {
        $set: {
          isWorking: {
            $cond: {
              if: { $eq: [{ $type: '$isWorking' }, 'missing'] },
              then: false,
              else: '$isWorking',
            },
          },
          status: {
            $cond: {
              if: { $eq: [{ $type: '$status' }, 'missing'] },
              then: false,
              else: '$status',
            },
          },
        },
      },
    ]
  );
};
