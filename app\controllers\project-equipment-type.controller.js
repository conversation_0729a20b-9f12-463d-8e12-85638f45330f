// Services
const projectEquipmentTypeService = require('../services/project-equipment-type.service');
const projectService = require('../services/project.service');
const equipmentTypeService = require('../services/equipment-type.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const commonUtils = require('../utils/common.utils');
const HTTP_STATUS = require('../utils/status-codes');

/**
 * Create Project Equipment Types
 *
 * @param {*} project
 * @param {*} equipmentTypes
 * @returns
 * */
exports.createProjectEquipmentType = async (req, res) => {
  try {
    const { project, equipmentType, sortOrder } = req.body;

    if (
      !commonUtils.isValidId(commonUtils.toObjectId(project)) ||
      !commonUtils.isValidId(commonUtils.toObjectId(equipmentType))
    ) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.INVALID_ID));
    }

    let projectExist = await projectService.getProjectById(project, req.userData.account);

    if (!projectExist) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.INVALID_PROJECT_ID));
    }

    let equipmentTypeExist = await equipmentTypeService.getEquipmentTypeByFilter({
      _id: commonUtils.toObjectId(equipmentType),
      account: req.userData.account,
      deletedAt: null,
    });

    if (!equipmentTypeExist) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.INVALID_EQUIPMENT_TYPE_ID));
    }

    const projectEquipmentTypeExist = await projectEquipmentTypeService.getProjectEquipmentTypes({
      project: commonUtils.toObjectId(project),
      equipmentType: commonUtils.toObjectId(equipmentType),
      account: req.userData.account,
      deletedAt: null,
    });

    if (projectEquipmentTypeExist) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.PROJECT_EQUIPMENT_TYPE_EXIST));
    }

    const response = await projectEquipmentTypeService.createProjectEquipmentTypes({
      project: commonUtils.toObjectId(project),
      equipmentType: commonUtils.toObjectId(equipmentType),
      sortOrder,
      account: req.userData.account,
    });

    res
      .status(HTTP_STATUS.CREATED)
      .json(responseUtils.successResponse(constantUtils.CREATE_PROJECT_EQUIPMENT_TYPE, response));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error));
  }
};

/**
 * Remove Project Equipment Types
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.removeProjectEquipmentTypes = async (req, res) => {
  try {
    const { id } = req.params;

    if (!commonUtils.isValidId(id)) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.INVALID_ID));
    }

    let projectEquipmentTypeExist = await projectEquipmentTypeService.getProjectEquipmentTypes({
      _id: commonUtils.toObjectId(id),
      account: req.userData.account,
      deletedAt: null,
    });

    if (!projectEquipmentTypeExist) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.PROJECT_EQUIPMENT_TYPE_DOES_NOT_EXIST));
    }

    const response = await projectEquipmentTypeService.removeProjectEquipmentTypes({
      _id: commonUtils.toObjectId(id),
      account: req.userData.account,
    });

    res
      .status(HTTP_STATUS.CREATED)
      .json(responseUtils.successResponse(constantUtils.REMOVE_PROJECT_EQUIPMENT_TYPE, response));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error));
  }
};

/**
 * Project Equipment List
 *
 * @param {*} req
 * @param {*} res
 */
exports.projectEquipmentList = async (req, res) => {
  try {
    const projectId = req.query.projectId;
    let sort = req.query.sort && req.query.sort === 'asc' ? 1 : -1;

    let filter = {
      account: req.userData.account,
      deletedAt: null,
      project: projectId,
    };

    let response = await projectEquipmentTypeService.projectEquipmentTypeList(filter, sort);

    res
      .status(HTTP_STATUS.CREATED)
      .json(responseUtils.successResponse(constantUtils.PROJECT_EQUIPMENT_TYPE_LIST, response));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error));
  }
};

/**
 * Update Project Equipment Type
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateProjectEquipmentType = async (req, res) => {
  try {
    if (!commonUtils.isValidId(req.params.id)) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.INVALID_ID));
    }

    const isExistEquipmentType = await projectEquipmentTypeService.getProjectEquipmentTypeById(
      req.params.id
    );

    if (!isExistEquipmentType) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.INVALID_ID));
    }

    const filter = {
      project: req.body.projectId,
      equipmentType: req.body.equipmentType,
      _id: { $ne: req.params.id },
      deletedAt: null,
    };

    const existEquipmentTypeWithProject =
      await projectEquipmentTypeService.getProjectEquipmentTypes(filter);

    if (existEquipmentTypeWithProject) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.PROJECT_EQUIPMENT_TYPE_EXIST));
    }

    let response = await projectEquipmentTypeService.updateProjectEquipmentType(
      req.params.id,
      req.body
    );

    res
      .status(HTTP_STATUS.CREATED)
      .json(responseUtils.successResponse(constantUtils.UPDATE_PROJECT_EQUIPMENT_TYPE, response));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error));
  }
};
