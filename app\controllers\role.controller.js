// Responses
const { successResponse, errorResponse } = require('../utils/response.utils');

// Services
const roleService = require('../services/role.service');
const roleAgreementService = require('../services/role-agreement.service');
const accountService = require('../services/account.service');
const authService = require('../services/auth.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const commonUtils = require('../utils/common.utils');
const commonFunctionsUtils = require('../utils/common-function.utils');
const HTTP_STATUS = require('../utils/status-codes');

/**
 * Creates a new role.
 *
 * @param { Object } req - The HTTP request object.
 * @param { Object } res - The HTTP response object.
 * @return { Object } The HTTP response containing the created role or an error message.
 */
exports.createRole = async (req, res) => {
  try {
    const isAdminUser = await accountService.findAccountById(req.userData.account);
    let { title } = req.body;
    title = title.replace(/\s+/g, ' ').trim();
    const regexPattern = global.constant.BANNED_KEYWORD;
    const isAlphaNumericPattern = global.constant.ALPHA_NUMERIC_KEYWORD;

    if (!isAlphaNumericPattern.test(title)) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.ONLY_ALPHABETIC_NUMERIC_KEYWORD));
    }

    let token = await authService.decodeJwtToken(req.headers.authorization);

    if (isAdminUser.email !== req.userData.email && token.role !== 'superadmin') {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.ONLY_ADMIN_ACCESS_TO_RESOURCES));
    }

    // Check if the title contains the banned keyword
    if (regexPattern.test(title)) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.BANNED_KEYWORD));
    }
    title = new RegExp('^' + title + '$', 'i');

    const exist = await roleService.getRoleByName(title, req.userData.account);

    if (exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.ROLE_EXIST));
    }

    let newReq = { ...req.body, account: req.userData.account, createdBy: req.userData.id };

    const role = await roleService.create(newReq);
    await roleAgreementService.createRoleAgreement(req, role);

    return res.status(200).json(successResponse(constantUtils.CREATE_ROLE, role));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Get All Roles For The Account
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getAllRoles = async (req, res) => {
  try {
    let roles;
    let filterData = {
      account: req.userData.account,
      deletedAt: null,
    };

    let { page, perPage, ...queryData } = req.query;

    page = parseInt(page) || 0;
    perPage = parseInt(perPage) || 10;

    if (req.query.allProject || req.query.active || req.query.accessType) {
      let { active, accessType } = queryData;
      if (active && active.toLowerCase() !== 'all') {
        filterData.isActive = active;
      }

      if (accessType && accessType.toLowerCase() !== 'all') {
        filterData.accessType = accessType;
      }

      roles = await roleService.searchRole(req, page, perPage);
    } else {
      roles = await roleService.getAllRoles(req.userData.account, page, perPage);
    }

    // add all records count
    let finalResponse = {
      rolesData: roles,
      currentPage: Number(page),
    };
    finalResponse = await commonUtils.getCountFromQuery('role', filterData, finalResponse);

    return res.status(200).json(successResponse(constantUtils.GET_ALL_ROLES, finalResponse));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Update Role
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateRole = async (req, res) => {
  try {
    const isAdminUser = await accountService.findAccountById(req.userData.account);
    const regexPattern = global.constant.BANNED_KEYWORD;

    let token = await authService.decodeJwtToken(req.headers.authorization);

    if (isAdminUser.email !== req.userData.email && token.role !== 'superadmin') {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.ONLY_ADMIN_ACCESS_TO_RESOURCES));
    }

    const { id } = req.params;
    const exist = await roleService.getRoleById(id, req.userData.account);

    if (!exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.ROLE_NOT_EXIST));
    }
    if ('title' in req.body) {
      // Check if the title contains the banned keyword
      if (regexPattern.test(req.body.title)) {
        return res.status(400).json(responseUtils.errorResponse(constantUtils.BANNED_KEYWORD));
      }

      const exist = await roleService.getRoleByName(req.body.title, req.userData.account);

      if (exist) {
        return res.status(400).json(responseUtils.errorResponse(constantUtils.ROLE_EXIST));
      }
    }
    req.body.updatedBy = req.userData.id;
    await roleService.update(id, req.body);
    await this.commonUpdateSyncApiManage(req.userData.account);
    return res.status(200).json(successResponse(constantUtils.UPDATE_ROLE));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Delete Role
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.deleteRole = async (req, res) => {
  try {
    const { id } = req.params;
    const exist = await roleService.getRoleById(id, req.userData.account);
    if (!exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.ROLE_NOT_EXIST));
    }
    await roleService.delete(id, req.deletedAt);
    return res.status(200).json(successResponse(constantUtils.DELETE_ROLE));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

exports.commonUpdateSyncApiManage = async account => {
  await commonFunctionsUtils.updateSyncApiManage({
    syncApis: [
      'mainConfig',
      'toolboxConfig',
      'reportConfig',
      'reportUsers',
      'reportNewFormConfig',
      'members',
      'allUserReports',
      'shifts',
      'users',
      'shiftActivities',
      'equipmentConfig',
      'projects',
      'projectDocuments',
      'upoloadCertificate',
      'userProfile',
    ],
    account,
  });
};
