const Report = require('../models/report-type.model');

/**
 * Create Report
 *
 * @param {*} report
 * @returns
 */
exports.createReport = async report => {
  return await Report.create(report);
};

/**
 * Get Report By Project id
 *
 * @param {*} report
 * @param {*} page
 * @param {*} perPage
 * @returns
 */
exports.getReportByProjectId = async (report, page, perPage) => {
  return Report.find({
    $and: [{ project: report.project }, { account: report.account }, { deletedAt: null }],
  })
    .populate([
      {
        path: 'project project',
        select: { title: 1, _id: 1 },
        strictPopulate: false,
      },
      {
        path: 'account account',
        select: { name: 1, _id: 1 },
        strictPopulate: false,
      },
    ])
    .limit(perPage)
    .skip(page * perPage);
};

/**
 * Update Report
 *
 * @param {*} id
 * @param {*} report
 * @returns
 */
exports.updateReport = async (id, report) => {
  return Report.findByIdAndUpdate(id, { $set: report }, { new: true }).populate([
    {
      path: 'project project',
      select: { title: 1, _id: 1 },
      strictPopulate: false,
    },
    {
      path: 'account account',
      select: { name: 1, _id: 1 },
      strictPopulate: false,
    },
  ]);
};

/**
 * Delete Report
 *
 * @param {*} id
 * @param {*} deletedAt
 * @returns
 */
exports.deleteReport = async (id, deletedAt) => {
  return Report.findByIdAndUpdate(id, { $set: deletedAt });
};

/**
 * Get All Report
 *
 * @param {*} account
 * @param {*} page
 * @param {*} perPage
 * @returns
 */
exports.getAllReports = async (account, page, perPage) => {
  const reports = Report.find({
    $and: [{ account: account }, { deletedAt: null }],
  }).populate([
    {
      path: 'project project',
      select: { title: 1, _id: 1 },
      strictPopulate: false,
    },
    {
      path: 'account account',
      select: { name: 1, _id: 1 },
      strictPopulate: false,
    },
    {
      path: 'user createdBy',
      select: { callingName: 1, firstName: 1, lastName: 1, _id: 1 },
      strictPopulate: false,
    },
  ]);

  if (page == '' && perPage == '') {
    return reports.sort({ createdAt: -1 });
  }

  return reports
    .limit(perPage)
    .skip(page * perPage)
    .sort({ createdAt: -1 });
};

/**
 * Get Report By Id
 *
 * @param {*} account
 * @param {*} id
 * @returns
 */
exports.getReportById = async (account, id) => {
  return Report.findOne(
    {
      $and: [{ _id: id }, { account: account }],
    },
    { createdAt: 0, updatedAt: 0, __v: 0 }
  ).populate([
    {
      path: 'account account',
      select: { name: 1, _id: 1 },
      strictPopulate: false,
    },
    {
      path: 'project project',
      select: { title: 1, _id: 1 },
      strictPopulate: false,
    },
  ]);
};

/**
 * Update report parameter
 *
 * @param {*} reportTypeId
 * @param {*} parameterId
 * @param {*} parameters
 * @returns
 */
exports.updateReportParameters = async (reportTypeId, parameterId, parameters) => {
  return Report.updateOne(
    { _id: reportTypeId, 'parameters._id': parameterId },
    { $set: parameters }
  );
};

/**
 * Get Report Type By Account Id
 *
 * @param {*} filter
 * @returns
 */
exports.getReportTypeByAccountId = async filter => {
  return Report.find(filter);
};

/**
 * Delete All Project's Report Type
 *
 * @param {*} id
 * @param {*} deletedAt
 * @returns
 */
exports.deleteAllProjectReport = async (projectId, deletedAt) => {
  return Report.updateMany(
    { project: projectId },
    {
      $set: deletedAt,
    }
  );
};
