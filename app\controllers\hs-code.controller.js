// Services
const HSCodeService = require('../services/hs-code.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const commonUtils = require('../utils/common.utils');
const commonFunction = require('../utils/common-function.utils');

/**
 * Create HSCode
 *
 * @param {*} req
 * @param {*} res
 */
exports.createHSCode = async (req, res) => {
  try {
    const requestData = req.body;
    const response = await HSCodeService.createHSCode(requestData);
    if (response) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    res.status(200).json(responseUtils.successResponse(constantUtils.CREATE_HS_CODE, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get EquipmentCategories
 *
 * @param {*} req
 * @param {*} res
 */
exports.getHSCode = async (req, res) => {
  try {
    let { account } = req.userData;
    let page = req.query.page ? req.query.page : '';
    let perPage = req.query.perPage ? req.query.perPage : '';
    let sort = req.query.sort && req.query.sort === 'asc' ? 1 : -1;

    let filterData = {
      account,
      deletedAt: null,
      ...(req.query.code && { code: await commonUtils.convertToCaseInsensetive(req.query.code) }),
    };

    const response = await HSCodeService.getHSCode(filterData, page, perPage, sort);
    res.status(200).json(responseUtils.successResponse(constantUtils.GET_HS_CODE, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Update HSCode
 *
 * @param {*} req
 * @param {*} res
 */
exports.updateHSCode = async (req, res) => {
  try {
    let { id } = req.params;
    const requestData = req.body;
    const response = await HSCodeService.getHSCodeById(id);
    if (!response) {
      return res.status(404).json(responseUtils.errorResponse(constantUtils.HS_CODE_NOT_FOUND));
    }

    const responseUpdate = await HSCodeService.updateHSCode(id, requestData);
    if (responseUpdate) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.UPDATE_HS_CODE, responseUpdate));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Delete HSCode
 *
 * @param {*} req
 * @param {*} res
 */
exports.deleteHSCode = async (req, res) => {
  try {
    let { id } = req.params;
    const response = await HSCodeService.getHSCodeById(id);
    if (!response) {
      return res.status(404).json(responseUtils.errorResponse(constantUtils.HS_CODE_NOT_FOUND));
    }
    const responseDelete = await HSCodeService.deleteHSCode(id, req.deletedAt);

    if (responseDelete) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }

    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.DELETE_HS_CODE, responseDelete));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Check unique HSCode
 *
 * @param {*} requestData
 * @returns
 */
exports.checkUniqueHSCode = async (requestData, hsId = null) => {
  let { code, account } = requestData;
  let filterData = {
    code,
    account,
    deletedAt: null,
  };

  let hsCode = hsId !== null ? await HSCodeService.getHSCodeById(hsId) : null;

  if (hsCode !== null && hsCode.code.toString() === code.toString()) {
    return true;
  }

  const response = await HSCodeService.getHSCode(filterData, '', '', -1);
  if (response.length > 0) {
    throw new Error(constantUtils.HS_CODE_EXIST);
  }
  return true;
};

exports.commonUpdateSyncApiManage = async account => {
  await commonFunction.updateSyncApiManage({
    syncApis: ['equipmentConfig'],
    account,
  });
};
