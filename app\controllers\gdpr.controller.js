const { errorResponse } = require('../utils/response.utils');
const HTTP_STATUS = require('../utils/status-codes');
const commonUtils = require('../utils/common.utils');

exports.getGdprTemplate = async (req, res) => {
  try {
    const htmlContent = await commonUtils.getRequestedFile(
      '../../pdf_templates',
      'gdpr.template.html'
    );

    res.json({ status: true, content: htmlContent });
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(errorResponse(error.message));
  }
};
