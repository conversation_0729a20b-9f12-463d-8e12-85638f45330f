require('dotenv').config();

// using libraries
const sgMail = require('@sendgrid/mail');

// environment variables
const { EMAIL_ID_MAIL, NODE_ENV, SENDGRID_API_KEY } = process.env;

// initialize sendgrid
sgMail.setApiKey(SENDGRID_API_KEY);

/**
 * send email
 *
 * @param {*} toMail
 * @param {*} subject
 * @param {*} template
 * @param {*} urlLink
 * @returns
 */
exports.sendMailer = async (toMail, template, templateData) => {
  if (NODE_ENV === 'development' || NODE_ENV === 'local') {
    const message = {
      from: EMAIL_ID_MAIL,
      to: toMail,
      template_id: template,
      dynamic_template_data: {
        ...templateData,
      },
    };

    return await sgMail
      .send(message)
      .then(response => {
        console.log(response[0].statusCode);
        console.log(response[0].headers);
      })
      .catch(error => {
        console.error(error);
      });
  }
};
