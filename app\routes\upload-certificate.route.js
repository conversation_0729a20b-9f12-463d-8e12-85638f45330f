const express = require('express');
const routes = express.Router();

const uploadCertificateController = require('../controllers/upload-certificate.controller');

// middleware
const { verifyToken, authAccount } = require('../middlewares/auth.middleware');
const { verifyFile } = require('../middlewares/upload.middleware');
const { uploadCertificateValidationRule } = require('../validators/upload-certificate.validator');

//Create
routes.post(
  '',
  verifyToken,
  authAccount,
  verifyFile,
  uploadCertificateValidationRule(),
  uploadCertificateController.createUserCertificate
);

// Update user certificates by id
routes.patch(
  '/:id',
  verifyToken,
  authAccount,
  verifyFile,
  uploadCertificateController.updateUserCertificate
);

routes.get(
  '/getInactiveCertificates',
  verifyToken,
  authAccount,
  verifyFile,
  uploadCertificateController.fixInactiveCertificates
);

// Update user certificates in bulk
routes.put('', verifyToken, uploadCertificateController.updateUserCertificatesInBulk);

//Get userCertificates
routes.get('/certificates', verifyToken, uploadCertificateController.getUserCertificates);

//Certificate Approval list
routes.get(
  '/certificate-list',
  verifyToken,
  authAccount,
  uploadCertificateController.certificateList
);

//upload multiple certificates
routes.post(
  '/upload-multiple-certificates',
  verifyToken,
  authAccount,
  verifyFile,
  uploadCertificateController.uploadMultipleUserCertificate
);

// Hard delete certificate
routes.delete(
  '/hard/delete/:id',
  verifyToken,
  authAccount,
  uploadCertificateController.deleteUserCertificate
);

module.exports = routes;
