require('dotenv').config();
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const randomId = require('rand-token').uid;

// services
const accountService = require('../services/account.service');
const userService = require('../services/user.service');
const projectService = require('../services/project.service');
const accountLicenceService = require('../services/account-licence.service');
const roleService = require('../services/role.service');
const temporaryEquipmentService = require('../services/temporary-equipment.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const defaultDataUtils = require('../utils/default-data.utils');
const roleAgreementUtils = require('../utils/role-agreement.utils');
const mailerUtils = require('../utils/mailer.utils');
const HTTP_STATUS = require('../utils/status-codes');

/**
 * Get All Admin Users
 *
 * @param {*} req
 * @param {*} res
 */
exports.getAdminUsers = async (req, res) => {
  try {
    let page = req.query.page ? req.query.page : 0;
    let perPage = req.query.perPage ? req.query.perPage : 10;
    const accountData = await accountService.getAdminUsers(page, perPage);

    res.status(200).json(responseUtils.successResponse(constantUtils.ALL_ACCOUNT, accountData));
  } catch (err) {
    res.status(500).json(responseUtils.errorResponse(err.message));
  }
};

/**
 * Create Account for client and set admin for account
 *
 * @param {*} req
 * @param {*} res
 */
exports.createAccount = async (req, res) => {
  const session = await mongoose.startSession();
  try {
    session.startTransaction();
    let template;

    let {
      name,
      callingName,
      firstName,
      lastName,
      email,
      password,
      accountOwner,
      logo,
      contactNumber,
      emergencyContactNumber,
      isActive,
      nationality,
      address,
      AccountLicence,
    } = req.body;
    email = email?.toLowerCase();
    /* Check For User Exist */
    const isAlreadyExist = await userService.getByEmail(email);

    if (isAlreadyExist) {
      await session.abortTransaction();
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.EMAIL_ALREADY_EXIST));
    }

    /* Check For Account Exist */
    const isAccountExist = await accountService.findAccountByName(name);

    if (isAccountExist.length > 0) {
      await session.abortTransaction();
      return res.status(400).json(responseUtils.errorResponse(constantUtils.ACCOUNT_EXIST));
    }

    let reqData = {
      callingName,
      firstName,
      lastName,
      email,
      password,
      contactNumber,
      emergencyContactNumber,
    };

    /* 30 min expire time */
    reqData.resetExpiresIn = new Date(Date.now() + parseInt(30 * 60 * 1000));
    /* generate random token */
    reqData.resetToken = randomId(16);

    reqData.password = bcrypt.hashSync(reqData.password);

    const createdUser = await userService.createUser(reqData);
    template = process.env.SENDGRID_NEW_USER_REGISTRATION_EMAIL;

    accountOwner = createdUser._id;
    const accountReq = {
      name,
      accountOwner,
      logo,
      email,
      isActive,
      organizationAddress: address,
      organizationCountry: nationality,
      createdBy: createdUser._id,
    };
    const accountData = await accountService.createAccount(accountReq);
    // create default admin role and update user
    const roleData = await roleService.create({
      title: global.constant.ADMIN_ROLE,
      description: global.constant.ADMIN_ROLE_DESCRIPTION,
      accessType: global.constant.ADMIN_ROLE_WEB,
      account: accountData._id,
      createdBy: createdUser._id,
    });

    const updateUser = {
      role: roleData._id,
      account: accountData._id,
    };

    await userService.updateUserById(createdUser._id, updateUser);

    // Create default project
    const newReq = {
      title: process.env.DEFAULT_PROJECT.replace(/_/g, ' '),
      projectNumber: process.env.DEFAULT_PROJECT_NUMBER,
      client: process.env.CLIENT,
      account: accountData._id,
      defaultIdentifier: global.constant.DEFAULT_DATA_IDENTIFIER,
      standByTypes: process.env.DEFAULT_PROJECT,
      isActive: true,
      isDefault: false,
      isDeletable: false,
    };
    await projectService.createProject(newReq);

    // Get default project
    let defaultProject = await projectService.getDefaultProject(accountData._id);
    // Prepare default project setup data
    let projectSetupData = {
      account: defaultProject.account._id,
      project: defaultProject._id,
      isDefault: true,
    };
    // Create default project setup data
    await defaultDataUtils.createDefaultProjectSetup(projectSetupData);
    /** Account Licence - process */
    if (Array.isArray(AccountLicence) && AccountLicence.length > 0) {
      for (const element of AccountLicence) {
        let { licence, permission } = element;
        if (licence != null && Array.isArray(permission)) {
          const requestData = {
            licence,
            account: accountData._id,
            permission,
            isRequested: true,
            isApproved: true,
          };
          await accountLicenceService.requestAccountLicence(requestData);
        }
      }
    }
    setTimeout(async () => {
      const getRoles = await roleService.getRoleIdOfAccount(accountData._id);
      getRoles.forEach(async element => {
        await roleAgreementUtils.addAdminRoleAgreement(accountData._id, createdUser._id, element);
      });
    }, 1000);

    await mailerUtils.sendMailer(email, template, {
      email,
      password,
      company: name,
      userName: (callingName || firstName) + ' ' + lastName,
      supportTeamEmail: process.env.SUPPORT_TEAM_EMAIL,
      isAdmin: true,
      loginUrl: process.env.BASE_URL + '/authentication/sign-in',
      bestRegards: process.env.BEST_REGARDS,
      currentYear: new Date().getFullYear(),
      logo: global.constant.APP_LOGO,
    });

    // Create temporary equipment setup
    if (accountData) {
      await temporaryEquipmentService.createTempEquipmentSetup({
        account: accountData._id,
        user: createdUser._id,
      });
    }

    res.status(200).json(responseUtils.successResponse(constantUtils.CREATE_ACCOUNT, accountData));
  } catch (err) {
    await session.abortTransaction();
    res.status(500).json(responseUtils.errorResponse(err.message));
  } finally {
    session.endSession();
  }
};

exports.getAccountLicenceByAccount = async (req, res) => {
  try {
    const account = req.userData.account;
    const filterData = {
      account: account,
      isApproved: true,
    };
    const accountLicenceData = await accountLicenceService.getAccountLicenceByAccount(filterData);
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.GET_ALL_LIST, accountLicenceData));
  } catch (err) {
    res.status(500).json(responseUtils.errorResponse(err.message));
  }
};

exports.getAccountLicenceByAccountId = async (req, res) => {
  try {
    const account = req.params.id;
    const filterData = {
      account: account,
      isApproved: true,
    };
    const accountLicenceData = await accountLicenceService.getAccountLicenceByAccount(filterData);
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.GET_ALL_LIST, accountLicenceData));
  } catch (err) {
    res.status(500).json(responseUtils.errorResponse(err.message));
  }
};

/**
 * Update Account
 *
 * @param {*} req
 * @param {*} res
 */
exports.updateAccount = async (req, res) => {
  try {
    const account = req.params.id;
    await accountService.updateAccount(account, req.body);

    res.status(200).json(responseUtils.successResponse(constantUtils.ACCOUNT_UPDATED));
  } catch (err) {
    res.status(500).json(responseUtils.errorResponse(err.message));
  }
};
