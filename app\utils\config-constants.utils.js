// Prepare static drop-downs for feedback form
const feedback = {
  Types: [
    {
      title: 'Complaint',
      id: 'complaint',
    },
    {
      title: 'Compliment',
      id: 'compliment',
    },
    {
      title: 'Feature Request',
      id: 'feature_request',
    },
    {
      title: 'Harassment',
      id: 'harassment',
    },
    {
      title: 'Idea',
      id: 'idea',
    },
    {
      title: 'Problem',
      id: 'problem',
    },
    {
      title: 'Question',
      id: 'question',
    },
  ],
  Subjects: [
    {
      title: 'Employment',
      id: 'employment',
    },
    {
      title: 'Hardware',
      id: 'hardware',
    },
    {
      title: 'Software',
      id: 'software',
    },
  ],
};

// Prepare config screens
let screens = {
  safeScreen: '',
  unsafeScreen: '',
  ncrScreen: '',
  incidentScreen: '',
  submitFeedbackScreen: '',
  openShiftScreen: '',
  reportScreen: '',
  shiftActivityScreen: '',
  settingScreen: '',
  projectDocumentScreen: '',
  userManagementScreen: '',
  certificateApprovalScreen: '',
};

let screenOrder = [
  'safeScreen',
  'unsafeScreen',
  'ncrScreen',
  'incidentScreen',
  'toolBoxTalkScreen',
  'openShiftScreen',
  'reportScreen',
  'projectDocumentScreen',
  'equipmentRequestScreen',
  'equipmentApprovalScreen',
  'equipmentScreen',
  'warehouseScreen',
  'submitFeedbackScreen',
  'settingScreen',
  'shiftActivityScreen',
  'userManagementScreen',
  'dprScreen',
  'certificateApprovalScreen',
];

module.exports = {
  feedback,
  screens,
  screenOrder,
};
