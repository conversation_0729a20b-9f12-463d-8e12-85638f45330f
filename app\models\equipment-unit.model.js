const mongoose = require('mongoose');

const EquipmentUnit = new mongoose.Schema(
  {
    title: {
      type: String,
    },
    abbreviation: {
      type: String,
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    isTemporary: {
      type: Boolean,
      default: false,
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

EquipmentUnit.index({ account: 1, deletedAt: 1 });
EquipmentUnit.index({ account: 1, deletedAt: 1, isActive: 1 });
EquipmentUnit.index({ title: 1 });
EquipmentUnit.index({ isActive: 1 });

module.exports = mongoose.model('equipment-unit', EquipmentUnit);
