const Warehouse = require('../models/warehouse.model');

// utils
const { toObjectId } = require('../utils/common.utils');

/**
 * Create warehouse
 *
 * @param {*} requestData
 * @returns
 */
exports.createWarehouse = async requestData => {
  return await Warehouse.create(requestData);
};

/**
 * Get Warehouses
 *
 * @param {*} filter
 * @param {*} page
 * @param {*} perPage
 * @param {*} sort
 * @returns
 */
exports.getWarehouses = async (filter, page, perPage, sort, otherFilter) => {
  const pipeline = [
    {
      $match: filter,
    },
    {
      $sort: { createdAt: sort },
    },
    {
      $lookup: {
        from: 'equipment',
        let: { warehouseId: '$_id' },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [{ $eq: ['$warehouse', '$$warehouseId'] }, { $eq: ['$deletedAt', null] }],
              },
            },
          },
          {
            $group: {
              _id: null,
              totalProductCount: { $sum: 1 },
            },
          },
        ],
        as: 'equipments',
      },
    },
    {
      $addFields: {
        totalProductCount: { $ifNull: [{ $arrayElemAt: ['$equipments.totalProductCount', 0] }, 0] },
      },
    },
    {
      $lookup: {
        from: 'warehouse-owners',
        localField: '_id',
        foreignField: 'warehouse',
        as: 'warehouseOwners',
        pipeline: [
          {
            $project: {
              user: 1,
            },
          },
          {
            $lookup: {
              from: 'users',
              localField: 'user',
              foreignField: '_id',
              as: 'user',
              pipeline: [
                {
                  $project: {
                    callingName: 1,
                    firstName: 1,
                    lastName: 1,
                    email: 1,
                  },
                },
              ],
            },
          },
          {
            $unwind: '$user',
          },
        ],
      },
    },
    {
      $project: {
        _id: 1,
        name: 1,
        email: 1,
        street: 1,
        city: 1,
        state: 1,
        country: 1,
        zipCode: 1,
        isActive: 1,
        image: 1,
        contactNumber: 1,
        totalProductCount: 1,
        warehouseOwners: 1,
      },
    },
  ];

  // Add other filter for check the login user has access for warehouse
  if (otherFilter?.warehouseOwner) {
    pipeline.push({
      $match: {
        warehouseOwners: {
          $elemMatch: {
            'user._id': otherFilter.warehouseOwner,
          },
        },
      },
    });
  }

  // Pagination handling
  const skip = (parseInt(page) || 0) * (parseInt(perPage) || 0);
  const limit = parseInt(perPage) || 0;

  if (limit > 0) {
    pipeline.push({ $skip: skip }, { $limit: limit });
  }

  return Warehouse.aggregate(pipeline);
};

/**
 * Get Warehouse by Id
 *
 * @param {*} id
 * @returns
 */
exports.getWarehouseById = async id => {
  return await Warehouse.findOne({ _id: id, deletedAt: null }).populate([
    {
      path: 'account',
      select: { _id: 1, name: 1 },
      strictPopulate: false,
    },
  ]);
};

/**
 * Update Warehouse
 *
 * @param {*} id
 * @param {*} requestData
 * @returns
 */
exports.updateWarehouse = async (id, requestData) => {
  return await Warehouse.findByIdAndUpdate(id, requestData, { new: true });
};

/**
 * Soft Delete Warehouse
 *
 * @param {*} id
 * @param {*} deletedAt
 * @returns
 */
exports.deleteWarehouse = async (id, deletedAt) => {
  return await Warehouse.findByIdAndUpdate(id, { $set: deletedAt }, { new: true });
};

/**
 * Get Warehouse by Status
 *
 * @param {*} account
 * @param {*} status
 * @returns
 */
exports.getWarehouseByStatus = async (account, status) => {
  return Warehouse.find({
    $and: [{ isActive: status }, { account }, { deletedAt: null }],
  });
};

/**
 * Get Warehouse by Name
 *
 * @param {*} name
 * @returns
 */
exports.getWarehouseByName = async (name, account) => {
  return Warehouse.findOne({ $and: [{ name }, { account }, { deletedAt: null }] });
};

/**
 * Get Warehouse by Email
 *
 * @param {*} email
 * @returns
 */
exports.getWarehouseByEmail = async email => {
  return Warehouse.findOne({ email, deletedAt: null });
};

/**
 * Get Warehouse Details
 *
 * @param {*} account
 * @param {*} id
 * @returns
 */
exports.getWarehouseDetails = async (account, id, page, perPage, sort) => {
  const matchObj = {
    account: account,
    _id: toObjectId(id),
    deletedAt: null,
  };

  const pipeline = [
    {
      $match: matchObj,
    },
    {
      $lookup: {
        from: 'equipment',
        let: { warehouseId: '$_id' },
        pipeline: [
          {
            $match: {
              $expr: { $eq: ['$warehouse', '$$warehouseId'] },
              deletedAt: null, // Add condition for non-deleted equipment
            },
          },
          {
            $sort: { createdAt: sort },
          },
          {
            $lookup: {
              from: 'equipment-types',
              localField: 'equipmentType',
              foreignField: '_id',
              pipeline: [
                {
                  $lookup: {
                    from: 'equipment-categories',
                    localField: 'equipmentCategory',
                    foreignField: '_id',
                    pipeline: [
                      {
                        $project: {
                          name: 1,
                        },
                      },
                    ],
                    as: 'equipmentCategory',
                  },
                },
                {
                  $unwind: {
                    path: '$equipmentCategory',
                    preserveNullAndEmptyArrays: true,
                  },
                },
                {
                  $lookup: {
                    from: 'currency-units',
                    localField: 'currencyUnit',
                    foreignField: '_id',
                    pipeline: [
                      {
                        $project: {
                          name: 1,
                          symbol: 1,
                        },
                      },
                    ],
                    as: 'currencyUnit',
                  },
                },
                {
                  $unwind: {
                    path: '$currencyUnit',
                    preserveNullAndEmptyArrays: true,
                  },
                },
                {
                  $lookup: {
                    from: 'equipment-units',
                    localField: 'equipmentUnit',
                    foreignField: '_id',
                    pipeline: [
                      {
                        $project: {
                          title: 1,
                          abbreviation: 1,
                        },
                      },
                    ],
                    as: 'equipmentUnit',
                  },
                },
                {
                  $unwind: {
                    path: '$equipmentUnit',
                    preserveNullAndEmptyArrays: true,
                  },
                },
                {
                  $project: {
                    type: 1,
                    hsCode: 1,
                    equipmentCategory: 1,
                    rentalPrice: 1,
                    currencyUnit: 1,
                    equipmentUnit: 1,
                  },
                },
              ],
              as: 'equipmentType',
            },
          },
          {
            $unwind: {
              path: '$equipmentType',
              preserveNullAndEmptyArrays: true,
            },
          },
          {
            $lookup: {
              from: 'warehouses',
              localField: 'warehouse',
              foreignField: '_id',
              pipeline: [
                {
                  $project: {
                    name: 1,
                  },
                },
              ],
              as: 'warehouse',
            },
          },
          {
            $unwind: {
              path: '$warehouse',
              preserveNullAndEmptyArrays: true,
            },
          },
          {
            $skip: parseInt(page) * parseInt(perPage),
          },
          {
            $limit: parseInt(perPage),
          },
          {
            $project: {
              name: 1,
              equipmentNumber: 1,
              productNumber: { $ifNull: ['$productNumber', null] },
              serialNumber: { $ifNull: ['$serialNumber', null] },
              value: 1,
              weight: 1,
              equipmentType: 1,
              warehouse: 1,
              qrCode: 1,
              equipmentImage: 1,
              certificate: 1,
              certificateType: 1,
              certificateValidateDate: 1,
              quantity: 1,
              equipmentLocationInWarehouse: 1,
              equipmentCurrentLocation: 1,
              equipmentLocationFromDate: 1,
              isActive: 1,
            },
          },
        ],
        as: 'equipments',
      },
    },
    {
      $lookup: {
        from: 'warehouse-owners',
        localField: '_id',
        foreignField: 'warehouse',
        as: 'warehouseOwners',
        pipeline: [
          {
            $project: {
              user: 1,
            },
          },
          {
            $lookup: {
              from: 'users',
              localField: 'user',
              foreignField: '_id',
              as: 'user',
              pipeline: [
                {
                  $project: {
                    callingName: 1,
                    firstName: 1,
                    lastName: 1,
                    email: 1,
                  },
                },
              ],
            },
          },
          {
            $unwind: '$user',
          },
        ],
      },
    },
  ];

  return await Warehouse.aggregate(pipeline);
};
