// Services
const equipmentCertificateTypeService = require('../services/equipment-certificate-type.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const commonFunction = require('../utils/common-function.utils');

/**
 * Create EquipmentCertificateType
 *
 * @param {*} req
 * @param {*} res
 */
exports.createEquipmentCertificateType = async (req, res) => {
  try {
    const requestData = req.body;
    // check duplicate entry
    const checkCertificateType =
      await equipmentCertificateTypeService.getCerificateTypeCountByFilter({
        title: requestData.title,
        account: req.userData.account,
        deletedAt: null,
      });
    if (checkCertificateType > 0) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.DUPLICATE_EQUIPMENT_CERTIFICATE_TYPE));
    }
    const response = await equipmentCertificateTypeService.createEquipmentCertificateType(
      requestData
    );

    // update sync api manage data
    if (response) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }

    res
      .status(200)
      .json(
        responseUtils.successResponse(constantUtils.CREATE_EQUIPMENT_CERTIFICATE_TYPE, response)
      );
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get EquipmentCertificateType
 *
 * @param {*} req
 * @param {*} res
 */
exports.getEquipmentCertificateType = async (req, res) => {
  try {
    let { account } = req.userData;
    let page = req.query.page ? req.query.page : '';
    let perPage = req.query.perPage ? req.query.perPage : '';
    let sort = req.query.sort && req.query.sort === 'asc' ? 1 : -1;

    let filterData = {
      account: account,
      deletedAt: null,
    };

    const response = await equipmentCertificateTypeService.getEquipmentCertificateType(
      filterData,
      perPage,
      page,
      sort
    );
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.GET_EQUIPMENT_CERTIFICATE_TYPE, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Update EquipmentCertificateType
 *
 * @param {*} req
 * @param {*} res
 */
exports.updateEquipmentCertificateType = async (req, res) => {
  try {
    let { id } = req.params;
    const requestData = req.body;
    const response = await equipmentCertificateTypeService.getEquipmentCertificateTypeById(id);
    if (!response) {
      return res
        .status(404)
        .json(responseUtils.errorResponse(constantUtils.EQUIPMENT_CERTIFICATE_TYPE_NOT_FOUND));
    }

    // check duplicate entry
    const checkCertificateType =
      await equipmentCertificateTypeService.filterSingleEquipmentCertificateType({
        _id: { $ne: id },
        title: requestData.title,
        account: req.userData.account,
        deletedAt: null,
      });

    if (checkCertificateType) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.DUPLICATE_EQUIPMENT_CERTIFICATE_TYPE));
    }

    const responseUpdate = await equipmentCertificateTypeService.updateEquipmentCertificateType(
      id,
      requestData
    );

    // update sync api manage data
    if (responseUpdate) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }

    res
      .status(200)
      .json(
        responseUtils.successResponse(
          constantUtils.UPDATE_EQUIPMENT_CERTIFICATE_TYPE,
          responseUpdate
        )
      );
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Delete EquipmentCertificateType
 *
 * @param {*} req
 * @param {*} res
 */
exports.deleteEquipmentCertificateType = async (req, res) => {
  try {
    let { id } = req.params;
    const response = await equipmentCertificateTypeService.getEquipmentCertificateTypeById(id);
    if (!response) {
      return res
        .status(404)
        .json(responseUtils.errorResponse(constantUtils.EQUIPMENT_CERTIFICATE_TYPE_NOT_FOUND));
    }
    const responseDelete = await equipmentCertificateTypeService.deleteEquipmentCertificateType(
      id,
      req.deletedAt
    );

    // update sync api manage data
    if (responseDelete) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    res
      .status(200)
      .json(
        responseUtils.successResponse(
          constantUtils.DELETE_EQUIPMENT_CERTIFICATE_TYPE,
          responseDelete
        )
      );
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

exports.commonUpdateSyncApiManage = async account => {
  await commonFunction.updateSyncApiManage({
    syncApis: ['equipmentConfig'],
    account,
  });
};
