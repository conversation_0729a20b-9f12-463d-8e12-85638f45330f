const request = require('supertest');
const app = require('../../app/server');

// create form-builders
describe('POST /api/form-builders', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.m4KGHeX7ut2fRp42YOriWVJB2ruro6Xb1QeN2InZwI4';
  const formBuildersData = {
    fieldName: 'Full Name1',
    fieldType: 'text',
    account: '63f34946ccf1c13060d353ee',
    optionValue: [],
    createdBy: '641170af8e0d16ff25a1b0d0',
  };
  it('returns 200 and message Field created successfully', async () => {
    const response = await request(app)
      .post('/api/form-builders')
      .set('Authorization', `Bearer ${token}`)
      .send(formBuildersData);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Field created successfully',
      status: true,
    });
  });

  it('returns 422 with message Required field cannot be empty.', async () => {
    const response = await request(app)
      .post('/api/form-builders')
      .set('Authorization', `Bearer ${token}`)
      .send(formBuildersData);
    expect(response.status).toBe(422);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Required field cannot be empty.',
      status: false,
    });
  });

  it('returns 400 with message No Field Found', async () => {
    const response = await request(app)
      .post('/api/form-builders')
      .set('Authorization', `Bearer ${token}`)
      .send(formBuildersData);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'No Field Found',
      status: false,
    });
  });
  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).post('/api/form-builders').send(formBuildersData);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});

//  Delete form-buildersById
describe('DELETE /api/form-builders/:id', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.m4KGHeX7ut2fRp42YOriWVJB2ruro6Xb1QeN2InZwI4';
  const id = '6413e9d6982e4493312a36fa';

  it('returns 200 and message Field deleted successfully', async () => {
    const response = await request(app)
      .delete(`/api/form-builders/${id}`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Field deleted successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).delete(`/api/form-builders/${id}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });

  it('returns 400 with message No Field Found', async () => {
    const response = await request(app)
      .delete('/api/form-builders/6412b7a6644aa8e810eb73fb')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'No Field Found',
      status: false,
    });
  });
});

//  Get form-buildersById
describe('GET /api/form-builders/:cardtype', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.TzxEZlLuxA-ok9iYNrzr_hCkEkSM35ruuXE4wvD_Gs0';
  const cardtype = 'safe';

  it('returns 200 and message Dynamic fields', async () => {
    const response = await request(app)
      .get(`/api/form-builders/${cardtype}`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({ data: {}, message: 'Dynamic fields', status: true });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).get(`/api/form-builders/${cardtype}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });

  it('returns 400 with message No Field Found', async () => {
    const response = await request(app)
      .get('/api/form-builders/ncrr')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'No Field Found',
      status: false,
    });
  });
});
