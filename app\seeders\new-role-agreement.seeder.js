/** Services */
const roleService = require('../services/role.service');
const accountLicenceService = require('../services/account-licence.service');
const roleAgreementService = require('../services/role-agreement.service');
const permissionService = require('../services/permission.service');
const accountService = require('../services/account.service');

/** Utils */
const constantUtils = require('../utils/constants.utils');

exports.up = async () => {
  try {
    const accounts = await accountService.getAccounts();
    const getPermissions = await permissionService.getPermissions();
    let processedAccounts = 0;

    for (const account of accounts) {
      let accountLicenceData;

      for (const permission of getPermissions) {
        const exist = await accountLicenceService.getAccountLicence(account._id, permission._id);

        if (!exist) {
          accountLicenceData = await accountLicenceService.createAccountLicence({
            account: account._id,
            licence: permission.licence,
            permission: permission._id,
            isRequested: true,
            isApproved: true,
          });
        }
      }

      const getRoles = await roleService.getRoleIdOfAccount(account._id);
      let userId;

      for (const element of getRoles) {
        userId = element.createdBy;
        await roleAgreementService.create(element, accountLicenceData, account);
      }

      const role = await roleService.create({
        title: 'Crewing',
        description: 'Crewing',
        isActive: true,
        accessType: 'both',
        account: account._id,
        createdBy: userId,
      });

      await roleAgreementService.createCrewingRoleAgreement(
        {
          userData: {
            account: account._id,
            id: userId,
          },
        },
        role,
        accountLicenceData
      );

      processedAccounts++;

      if (processedAccounts === accounts.length) {
        console.log('success:->', constantUtils.UPDATED_SUCCESSFULLY);
      }
    }

    let roleAgreements = await roleAgreementService.getRoleAgreements({
      isActive: true,
      deletedAt: null,
    });

    const adminRoleAgreements = roleAgreements.filter(
      roleAgreement => roleAgreement.role?.title === 'admin'
    );

    for (const roleAgreement of adminRoleAgreements) {
      await roleAgreementService.updateRoleAgreement(roleAgreement._id, {
        agreement: {
          create: true,
          read: true,
          update: true,
          delete: true,
        },
      });
    }
  } catch (error) {
    console.log('error:->', error.message);
  }
};
