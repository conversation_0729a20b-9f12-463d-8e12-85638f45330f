const Scope = require('../models/scope.model');
const { toObjectId } = require('../utils/common.utils');

/**
 * Create Scope
 *
 * @param {*} Scope
 * @returns
 */
exports.createScope = async requestData => await Scope.create(requestData);

/**
 * Update by id
 *
 * @param {*} id
 * @param {*} update
 * @returns
 */
exports.updateScope = async (id, update) =>
  Scope.findByIdAndUpdate(id, update, { new: true }).populate([
    {
      path: 'project',
      select: 'title projectNumber',
    },
    {
      path: 'account',
      select: 'name',
    },
  ]);

/**
 * Delete By Id
 *
 * @param {*} id
 * @returns
 */
exports.deleteScope = async (id, deletedAt) => {
  return await Scope.findByIdAndUpdate(id, { $set: deletedAt }, { new: true });
};

/**
 * Get Scope By Id
 *
 * @param {*} id
 * @returns
 */
exports.getScopeById = async id => Scope.find({ _id: toObjectId(id) });

/**
 * Get scope By projectId
 *
 * @param {*} projectId
 * @returns
 */
exports.getScopeByProjectId = async (
  filter = {},
  page = null,
  perPage = null,
  sort = { sortOrder: 1 }
) => {
  return await Scope.find(filter)
    .collation({ locale: 'en', strength: 2 })
    .sort(sort)
    .limit(perPage)
    .skip(page * perPage)
    .populate([
      {
        path: 'project',
        select: 'title projectNumber',
      },
      {
        path: 'account',
        select: 'name',
      },
    ]);
};

/**
 * Get scope by project id and name
 *
 * @param {*} projectId
 * @param {*} name
 * @returns
 */
exports.getScopeByProjectIdAndName = async filter => Scope.find(filter);

/**
 * Delete All Project's Scope
 *
 * @param {*} id
 * @param {*} deletedAt
 * @returns
 */
exports.deleteAllProjectScope = async (projectId, deletedAt) => {
  return Scope.updateMany(
    { project: projectId },
    {
      $set: deletedAt,
    }
  );
};

exports.getDefaultScope = async filter => {
  return await Scope.findOne(filter);
};

exports.pushReportInScope = async (id, report) => {
  return Scope.findByIdAndUpdate(id, { $push: { reports: report } });
};

exports.pullReportInScope = async (id, report) => {
  return Scope.findByIdAndUpdate(id, { $pull: { reports: report } });
};

/**
 * Get scope, report, location, asset by project
 *
 * @param {*} projectId
 * @param {*} name
 * @returns
 */
exports.getProjectTrackerContent = async filter => {
  const pipeline = [
    {
      $match: filter,
    },
    {
      $sort: { sortOrder: 1 },
    },
    {
      $lookup: {
        from: 'reports',
        localField: 'reports',
        foreignField: '_id',
        as: 'reports',
        pipeline: [
          { $sort: { sortOrder: 1 } },
          {
            $match: { deletedAt: null, isPublish: true, isProgressable: true },
          },
          {
            $lookup: {
              from: 'report-questions',
              localField: '_id',
              foreignField: 'report',
              as: 'reportDurations',
              pipeline: [
                {
                  $match: {
                    deletedAt: null,
                  },
                },
                { $group: { _id: null, total: { $sum: '$duration' } } },
              ],
            },
          },
          {
            $unwind: {
              path: '$reportDurations',
              preserveNullAndEmptyArrays: true,
            },
          },
          {
            $project: {
              _id: 1,
              title: 1,
              createdAt: 1,
              type: 1,
              reportDurations: '$reportDurations.total',
            },
          },
        ],
      },
    },
    {
      $project: {
        _id: 1,
        name: 1,
        sortOrder: 1,
        project: 1,
        reports: 1,
      },
    },
  ];

  return await Scope.aggregate(pipeline);
};
