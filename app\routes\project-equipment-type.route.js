const express = require('express');
const routes = express.Router();

// Validator
// const validator = require('../validators/shift-activity.validator');

// middleware
const { verifyToken, authAccount } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const projectEquipmentTypeController = require('../controllers/project-equipment-type.controller');

routes.get(
  '',
  verifyToken,
  authAccount,
  validate,
  projectEquipmentTypeController.projectEquipmentList
);

routes.post(
  '',
  verifyToken,
  authAccount,
  validate,
  projectEquipmentTypeController.createProjectEquipmentType
);

routes.patch(
  '/:id',
  verifyToken,
  authAccount,
  validate,
  projectEquipmentTypeController.updateProjectEquipmentType
);

routes.delete(
  '/:id',
  verifyToken,
  authAccount,
  validate,
  projectEquipmentTypeController.removeProjectEquipmentTypes
);

module.exports = routes;
