const mongoose = require('mongoose');

const UserReport = mongoose.Schema(
  {
    title: {
      type: String,
    },
    project: {
      type: mongoose.Types.ObjectId,
      ref: 'project',
    },
    report: {
      type: mongoose.Types.ObjectId,
      ref: 'report',
    },
    location: {
      type: mongoose.Types.ObjectId,
      ref: 'location',
    },
    asset: [
      {
        asset: {
          type: mongoose.Types.ObjectId,
          ref: 'asset',
        },
      },
    ],
    status: {
      type: String,
      enum: ['open', 'closed', 'submitted'],
      default: 'open',
    },
    signature: {
      type: String,
      default: null,
    },
    signatureBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    userProjectReport: {
      type: mongoose.Types.ObjectId,
      ref: 'user-project-report',
      default: null,
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

// Add compound index
UserReport.index({
  project: 1,
  report: 1,
  account: 1,
  deletedAt: 1,
  createdBy: 1,
});

module.exports = mongoose.model('user-report', UserReport);
