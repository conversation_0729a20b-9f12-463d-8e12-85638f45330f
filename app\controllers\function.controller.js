require('dotenv').config();

// Services
const functionService = require('../services/function.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const { toObjectId } = require('../utils/common.utils');

/**
 * Create Function
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.createFunction = async (req, res) => {
  try {
    const { functionName, project, certificates, sortOrder } = req.body;
    let reqData = {};
    const newReq = {
      ...reqData,
      account: req.userData.account,
      project: req.body.project,
      sortOrder,
      functionName: functionName,
      certificates: certificates,
    };

    const filterData = {
      functionName,
      project,
      account: req.userData.account.toString(),
      isDefault: false,
      deletedAt: null,
    };

    const exist = await functionService.getFunctionByName(filterData);

    if (exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.FUNCTION_EXIST));
    }
    const createdFunction = await functionService.createFunction(newReq);
    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.CREATE_FUNCTION, createdFunction));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Update Function
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateFunction = async (req, res) => {
  try {
    const id = req.params.id;
    const exist = await functionService.getFunction(id);
    if (!exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_FUNCTION));
    }

    let alreadyExist = await functionService.filterSingleFunction({
      _id: { $ne: toObjectId(id) },
      functionName: req.body.functionName,
      project: toObjectId(req.body.project),
      account: req.userData.account,
      deletedAt: null,
    });

    if (alreadyExist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.FUNCTION_EXIST));
    }

    const response = await functionService.updateFunction(id, req.body);
    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.UPDATE_FUNCTION, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Delete Function
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.deleteFunction = async (req, res) => {
  try {
    let id = req.params.id;
    const exist = await functionService.getFunctionById(id, req.userData.account);

    if (!exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_FUNCTION));
    }
    const response = await functionService.deleteFunction(id, req.deletedAt);

    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.DELETE_FUNCTION, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};
