// services
const warehouseService = require('../services/warehouse.service');
const warehouseOwnerService = require('../services/warehouse-owner.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const commonFunction = require('../utils/common-function.utils');

/**
 * Create warehouse
 *
 * @param {*} req
 * @param {*} res
 */
exports.createWarehouse = async (req, res) => {
  try {
    let reqData = req.body;

    let owner = reqData.owner;

    const exist = await warehouseService.getWarehouseByName(req.body.name, req.userData.account);

    if (exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.WAREHOUSE_EXIST));
    }

    reqData.account = req.userData.account;
    const warehouse = await warehouseService.createWarehouse(req.body);
    owner.forEach(async element => {
      let warehouseOwner = {
        warehouse: warehouse._id,
        user: element,
        account: req.userData.account,
        createdBy: req.userData.id,
        updatedBy: req.userData.id,
      };
      await warehouseOwnerService.createWarehouseOwner(warehouseOwner);
    });
    // update sync api manage data
    if (warehouse) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    res.status(200).json(responseUtils.successResponse(constantUtils.CREATE_WAREHOUSE, warehouse));
  } catch (err) {
    res.status(500).json(responseUtils.errorResponse(err.message));
  }
};

/**
 * Get Warehouses
 *
 * @param {*} req
 * @param {*} res
 */
exports.getWarehouses = async (req, res) => {
  try {
    let warehouse;
    let { account } = req.userData;

    let page = req.query.page ? req.query.page : '';
    let isActive = req.query.isActive;
    let perPage = req.query.perPage ? req.query.perPage : '';
    let sort = req.query.sort && req.query.sort === 'asc' ? 1 : -1;

    let filterData = {
      account: account,
      deletedAt: { $eq: null },
    };

    if (isActive.toLowerCase() !== 'all') {
      filterData.isActive = isActive.toLowerCase() === 'true';
    }

    // check the login user has access for warehouse
    let otherFilter = [global.constant.ADMIN_ROLE, global.constant.SUPER_ADMIN_ROLE].includes(
      req.userData.role.title
    )
      ? {}
      : {
          warehouseOwner: req.userData._id,
        };

    warehouse = await warehouseService.getWarehouses(filterData, page, perPage, sort, otherFilter);

    res.status(200).json(responseUtils.successResponse(constantUtils.GET_WAREHOUSES, warehouse));
  } catch (err) {
    res.status(500).json(responseUtils.errorResponse(err.message));
  }
};

/**
 * Get Warehouse by Id
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getWarehouseById = async (req, res) => {
  try {
    let { id } = req.params;

    const warehouse = await warehouseService.getWarehouseById(id);

    if (!warehouse) {
      return res.status(404).json(responseUtils.errorResponse(constantUtils.WAREHOUSE_NOT_FOUND));
    }

    // check the login user has access for warehouse data
    if (
      ![global.constant.ADMIN_ROLE, global.constant.SUPER_ADMIN_ROLE].includes(
        req.userData.role.title
      )
    ) {
      let filter = {
        warehouse: id,
        user: req.userData._id,
        account: req.userData.account,
      };

      const getWarehouseOwner = await warehouseOwnerService.getSingleDataByFilter(filter);

      if (!getWarehouseOwner) {
        return res.status(404).json(responseUtils.errorResponse(constantUtils.WAREHOUSE_NOT_FOUND));
      }
    }

    res.status(200).json(responseUtils.successResponse(constantUtils.GET_WAREHOUSE, warehouse));
  } catch (err) {
    res.status(500).json(responseUtils.errorResponse(err.message));
  }
};

/**
 * Update Warehouse
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateWarehouse = async (req, res) => {
  try {
    let { id } = req.params;
    let reqData = req.body;

    const exist = await warehouseService.getWarehouseById(id);

    if (!exist) {
      return res.status(404).json(responseUtils.errorResponse(constantUtils.WAREHOUSE_NOT_FOUND));
    }

    if ('owner' in reqData) {
      let owners = await warehouseOwnerService.getOwners(id, req.userData.account);
      // eslint-disable-next-line no-undef
      await Promise.all(owners.map(element => warehouseOwnerService.deleteOwners(element._id)));
      reqData.owner.forEach(async element => {
        let warehouseOwner = {
          warehouse: id,
          user: element,
          account: req.userData.account,
          createdBy: req.userData.id,
          updatedBy: req.userData.id,
        };
        await warehouseOwnerService.createWarehouseOwner(warehouseOwner);
      });
    }

    const warehouse = await warehouseService.updateWarehouse(id, reqData);
    if (warehouse) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    res.status(200).json(responseUtils.successResponse(constantUtils.UPDATE_WAREHOUSE, warehouse));
  } catch (err) {
    res.status(500).json(responseUtils.errorResponse(err.message));
  }
};

/**
 * Delete Warehouse
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.deleteWarehouse = async (req, res) => {
  try {
    let { id } = req.params;

    const exist = await warehouseService.getWarehouseById(id);

    if (!exist) {
      return res.status(404).json(responseUtils.errorResponse(constantUtils.WAREHOUSE_NOT_FOUND));
    }

    const warehouse = await warehouseService.deleteWarehouse(id, req.deletedAt);
    if (warehouse) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    res.status(200).json(responseUtils.successResponse(constantUtils.DELETE_WAREHOUSE, warehouse));
  } catch (err) {
    res.status(500).json(responseUtils.errorResponse(err.message));
  }
};

/**
 * Get Products by Warehouse Id
 *
 * @param {*} req
 * @param {*} res
 */
exports.getProductsByWarehouseId = async (req, res) => {
  try {
    let { id } = req.params;

    const exist = await warehouseService.getWarehouseById(id);

    if (!exist) {
      return res.status(404).json(responseUtils.errorResponse(constantUtils.WAREHOUSE_NOT_FOUND));
    }
    let page = req.query.page ? req.query.page : 0;
    let perPage = req.query.perPage ? req.query.perPage : 50;
    let sort = req.query.sort && req.query.sort === 'asc' ? 1 : -1;

    // check the login user has access for warehouse data
    if (
      ![global.constant.ADMIN_ROLE, global.constant.SUPER_ADMIN_ROLE].includes(
        req.userData.role.title
      )
    ) {
      let filter = {
        warehouse: id,
        user: req.userData._id,
        account: req.userData.account,
      };

      const getWarehouseOwner = await warehouseOwnerService.getSingleDataByFilter(filter);

      if (!getWarehouseOwner) {
        return res.status(404).json(responseUtils.errorResponse(constantUtils.WAREHOUSE_NOT_FOUND));
      }
    }

    const warehouse = await warehouseService.getWarehouseDetails(
      req.userData.account,
      id,
      page,
      perPage,
      sort
    );

    res.status(200).json(responseUtils.successResponse(constantUtils.GET_WAREHOUSE, warehouse));
  } catch (err) {
    res.status(500).json(responseUtils.errorResponse(err.message));
  }
};

exports.commonUpdateSyncApiManage = async account => {
  await commonFunction.updateSyncApiManage({
    syncApis: ['equipmentConfig'],
    account,
  });
};
