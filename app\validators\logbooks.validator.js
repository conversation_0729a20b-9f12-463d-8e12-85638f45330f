const { body, param, constantUtils } = require('../validators/parent.validator');
const { isValidObjectId } = require('mongoose');

const logbookValidationRules = {
  create: [
    body('user')
      .notEmpty()
      .withMessage(constantUtils.LOGBOOK_USER_ID_REQUIRED)
      .custom(value => {
        if (!isValidObjectId(value)) {
          throw new Error(constantUtils.LOGBOOK_INVALID_USER_ID);
        }
        return true;
      }),
    body('project')
      .optional()
      .custom(value => {
        if (!isValidObjectId(value)) {
          throw new Error(constantUtils.LOGBOOK_INVALID_PROJECT_ID);
        }
        return true;
      }),
    body('description')
      .notEmpty()
      .withMessage(constantUtils.LOGBOOK_DESCRIPTION_REQUIRED)
      .isString()
      .withMessage(constantUtils.LOGBOOK_DESCRIPTION_STRING)
      .trim(),
  ],

  getAll: [
    param('user')
      .notEmpty()
      .withMessage(constantUtils.LOGBOOK_USER_ID_REQUIRED)
      .custom(value => {
        if (!isValidObjectId(value)) {
          throw new Error(constantUtils.LOGBOOK_INVALID_USER_ID);
        }
        return true;
      }),
  ],

  update: [
    param('id')
      .notEmpty()
      .withMessage(constantUtils.LOGBOOK_ID_REQUIRED)
      .custom(value => {
        if (!isValidObjectId(value)) {
          throw new Error(constantUtils.LOGBOOK_INVALID_LOGBOOK_ID);
        }
        return true;
      }),
    body('project')
      .optional()
      .custom(value => {
        if (!isValidObjectId(value)) {
          throw new Error(constantUtils.LOGBOOK_INVALID_PROJECT_ID);
        }
        return true;
      }),
    body('description')
      .optional()
      .isString()
      .withMessage(constantUtils.LOGBOOK_DESCRIPTION_STRING)
      .trim(),
  ],

  delete: [
    param('id')
      .notEmpty()
      .withMessage(constantUtils.LOGBOOK_ID_REQUIRED)
      .custom(value => {
        if (!isValidObjectId(value)) {
          throw new Error(constantUtils.LOGBOOK_INVALID_LOGBOOK_ID);
        }
        return true;
      }),
  ],
};

module.exports = logbookValidationRules;
