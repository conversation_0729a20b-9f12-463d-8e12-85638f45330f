// library
const express = require('express');
const routes = express.Router();

// middleware
const { verifyToken, authAccount } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// Validator
const validator = require('../validators/pm-order.validator');

// controller
const pmOrderController = require('../controllers/pm-order.controller');

routes.post(
  '/add-to-queue',
  verifyToken,
  authAccount,
  validator.createPMOrderValidationRule(),
  validate,
  pmOrderController.addToQueuePMOrderV2
);

routes.patch(
  '/request/:pmOrderId',
  verifyToken,
  authAccount,
  validate,
  pmOrderController.pmOrderRequestV2
);

module.exports = routes;
