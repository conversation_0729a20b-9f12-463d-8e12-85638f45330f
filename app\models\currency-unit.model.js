const mongoose = require('mongoose');

const CurrencyUnit = new mongoose.Schema(
  {
    name: {
      type: String,
    },
    symbol: {
      type: String,
    },
    isDefault: {
      type: Boolean,
      default: false,
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    isTemporary: {
      type: Boolean,
      default: false,
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

CurrencyUnit.index({ account: 1, deletedAt: 1 });
CurrencyUnit.index({ account: 1, deletedAt: 1, isActive: 1 });
CurrencyUnit.index({ name: 1 });
CurrencyUnit.index({ isActive: 1 });

module.exports = mongoose.model('currency-unit', CurrencyUnit);
