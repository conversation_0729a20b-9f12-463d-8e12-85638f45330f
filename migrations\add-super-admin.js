const { SUPER_ADMIN_PASSWORD } = process.env;

module.exports = {
  async up(db) {
    await db.collection('users').insertOne({
      firstName: 'Allie',
      lastName: 'Grater',
      email: '<EMAIL>',
      password: SUPER_ADMIN_PASSWORD,
      role: ['superadmin'],
      contactNumber: '**********',
      emergencyContactNumber: '**********',
      account: '63eb3b5e7db105a873d1fc7b',
    });
  },
};
