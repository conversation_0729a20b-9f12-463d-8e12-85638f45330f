const mongoose = require('mongoose');

const ProjectEquipmentType = new mongoose.Schema(
  {
    project: {
      type: mongoose.Types.ObjectId,
      ref: 'project',
    },
    equipmentType: {
      type: mongoose.Types.ObjectId,
      ref: 'equipment-type',
    },
    sortOrder: {
      type: Number,
      default: 0,
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('project-equipment-type', ProjectEquipmentType);
