const mongoose = require('mongoose');

const ProjectString = new mongoose.Schema(
  {
    name: {
      type: String,
    },
    fromLocation: {
      type: mongoose.Types.ObjectId,
      ref: 'location',
    },
    toLocation: {
      type: mongoose.Types.ObjectId,
      ref: 'location',
    },
    project: {
      type: mongoose.Types.ObjectId,
      ref: 'project',
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    isDeletable: {
      type: Boolean,
      default: true,
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    defaultIdentifier: {
      type: String,
      default: global.constant.NORMAL_DATA_IDENTIFIER, // DEFAULT_DATA_IDENTIFIER: for default project, NORMAL_DATA_IDENTIFIER: for normal project
    },
    deletedAt: {
      type: String,
      default: null,
    },
    isDefault: {
      type: Boolean,
      default: false,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('projectstring', ProjectString);
