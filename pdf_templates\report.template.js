exports.soloReport = async () => {
  return `
  <!DOCTYPE html>
<html>
  <head>
    <style>
      html {
        -webkit-print-color-adjust: exact;
      }

      .reynard-critical-pdf-p-1-v-1,
      .reynard-critical-pdf-p-1-v-1 * {
        box-sizing: border-box;
      }

      .reynard-critical-pdf-p-1-v-1 {
        background: #f6f7ff;
        width: 100%;
        height: 842px;
        position: relative;
        overflow: hidden;
      }

      .text-main-header {
        position: relative;
        width: -webkit-fill-available;
        display: flex;
        margin-bottom: 10px;
        top: 10px;
      }

      .text-main-title {
        color: #344054;
        text-align: left;
        font: 700 24px "Inter", sans-serif;
        position: relative;
        left: 30px;
        width: -webkit-fill-available;
      }

      .text-main-logo {
        color: #344054;
        text-align: right;
        font: 700 24px "Inter", sans-serif;
        position: relative;
        right: 30px;
        width: -webkit-fill-available;
      }

      .table-relative {
        width: -webkit-fill-available;
        position: relative;
        margin: 30px 0px 30px 0px;
      }

      .project-value-cell {
        width: 47% !important;
      }

      .table-header-cell {
        background: #d3d3d3;
        border-radius: 8px 8px 0px 0px;
        border-style: solid;
        border-color: transparent;
        border-width: 0px 0px 1px 0px;
        padding: 12px 24px 12px 24px;
        display: flex;
        flex-direction: row;
        gap: 12px;
        align-items: center;
        justify-content: flex-start;
        height: 45px;
        margin: 0px 30px;
      }

      .table-th,
      .table-td {
        width: 50%;
      }

      .table-td-l {
        width: 33.33%;
      }

      .table-header {
        display: flex;
        flex-direction: row;
        gap: 4px;
        align-items: center;
        justify-content: flex-start;
        flex-shrink: 0;
        position: relative;
      }

      .text2 {
        color: #ffffff;
        text-align: left;
        font: 700 14px/18px "Inter", sans-serif;
        position: relative;
      }

      .rectangle-178 {
        background: rgba(224, 230, 245, 0.1);
        width: 1px;
        height: 45px;
        position: absolute;
        left: 297px;
        top: 95px;
      }

      .text3 {
        color: #ffffff;
        text-align: left;
        font: 700 14px/18px "Inter", sans-serif;
        position: absolute;
        left: 50%;
        top: 108px;
        padding-left: 22px;
      }

      .table-cell {
        background: #ffffff;
        border-radius: 0px 0px 0px 8px;
        border-style: solid;
        border-color: #e0e6f5;
        border-width: 0px 1px 0px 0px;
        padding: 16px 24px 16px 24px;
        display: flex;
        flex-direction: row;
        gap: 0px;
        align-items: center;
        justify-content: flex-start;
        height: 44px;
        left: 30px;
        position: relative;
      }

      .text4 {
        color: #475467;
        text-align: left;
        font: 500 14px/20px "Inter", sans-serif;
        position: relative;
      }

      .table-img {
        width: 76.67px;
        height: 76.67px;
        margin-top: 40px;
        position: relative;
        display: flex;
      }
    </style>
  </head>
  <body>
    <div class="reynard-critical-pdf-p-1-v-1">
      <div class="text-main-header">
        <div class="text-main-title">Termination Report</div>
        <div class="text-main-logo">
          <img
            src="https://reynard-dev-fb469d2.sfo3.cdn.digitaloceanspaces.com/images/Company_Registration/BG4SIWq0SpPSBkk_company.png"
            th="100"
            height="40"
          />
        </div>
      </div>
      <table class="table-relative">
        <tr class="table-header-cell">
          <th class="table-th">
            <div class="table-header">
              <div class="text2">Project Name</div>
            </div>
          </th>
          <th class="table-th">
            <div class="table-header">
              <div class="text2">Location</div>
            </div>
          </th>
        </tr>
        <tr class="table-cell">
          <td class="table-th project-value-cell">
            <div class="text4">Sample Project</div>
          </td>
          <td class="table-th">
            <div class="text4">Sample Locations</div>
          </td>
        </tr>
      </table>

      <table class="table-relative">
        <tr class="table-header-cell">
          <th>
            <div class="table-header">
              <div class="text2">Cable ID</div>
            </div>
          </th>
        </tr>
        <tr>
          <td class="table-cell">
            <div class="text4">Cable-123</div>
          </td>
        </tr>
      </table>
      <table class="table-relative">
        <tr class="table-header-cell">
          <th>
            <div class="table-header">
              <div class="text2">Detail 1</div>
            </div>
          </th>
        </tr>
        <tr>
          <td class="table-cell">
            <div class="text4">Detail 1 Value</div>
          </td>
        </tr>
      </table>
      <table class="table-relative">
        <tr class="table-header-cell">
          <th>
            <div class="table-header">
              <div class="text2">Detail 2</div>
            </div>
          </th>
        </tr>
        <tr>
          <td class="table-cell">
            <div class="text4">Detail 2 Value</div>
          </td>
        </tr>
      </table>
      <table class="table-relative">
        <tr class="table-header-cell">
          <th>
            <div class="table-header">
              <div class="text2">Three Phase Detail</div>
            </div>
          </th>
        </tr>
        <tr class="table-cell">
          <th class="table-td-l">
            <div class="text4">l1</div>
          </th>
          <th class="table-td-l">
            <div class="text4">l2</div>
          </th>
          <th class="table-td-l">
            <div class="text4">l3</div>
          </th>
        </tr>
        <tr class="table-cell">
          <td class="table-td-l">
            <div class="text4">
              <img
                class="table-img"
                src="https://reynard-dev-fb469d2.sfo3.cdn.digitaloceanspaces.com/images/Company_Registration/BG4SIWq0SpPSBkk_company.png"
              />
            </div>
          </td>
          <td class="table-td-l">
            <div class="text4">
              <img
                class="table-img"
                src="https://reynard-dev-fb469d2.sfo3.cdn.digitaloceanspaces.com/images/Company_Registration/BG4SIWq0SpPSBkk_company.png"
              />
            </div>
          </td>
          <td class="table-td-l">
            <div class="text4">
              <img
                class="table-img"
                src="https://reynard-dev-fb469d2.sfo3.cdn.digitaloceanspaces.com/images/Company_Registration/BG4SIWq0SpPSBkk_company.png"
              />
            </div>
          </td>
        </tr>
      </table>
    </div>
  </body>
</html>

  `;
};
