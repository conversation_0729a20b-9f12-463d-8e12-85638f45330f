const mongoose = require('mongoose');

const Category = new mongoose.Schema(
  {
    categoryName: {
      type: String,
    },
    isVisibleForIncidentCard: {
      type: Boolean,
      default: false,
    },
    isVisibleForSafeCard: {
      type: Boolean,
      default: false,
    },
    isVisibleForUnsafeCard: {
      type: Boolean,
      default: false,
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('category', Category);
