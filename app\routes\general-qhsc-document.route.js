const routes = require('express').Router();
const generalQhscDocumentController = require('../controllers/general-qhsc-document.controller');

// middleware
const { verifyToken, authAccount, deletedAt } = require('../middlewares/auth.middleware');

const { validate } = require('../middlewares/validate.middleware');

// Validator

const {
  createGeneralQhscDocumentValidationRule,
  updateGeneralQhscDocumentValidationRule,
} = require('../validators/general-qhsc-document.validator');

// controller
routes.get('', verifyToken, authAccount, validate, generalQhscDocumentController.getAllDocuments);

routes.post(
  '',
  verifyToken,
  authAccount,
  createGeneralQhscDocumentValidationRule(),
  validate,
  generalQhscDocumentController.createDocuments
);

routes.get('/:id', verifyToken, authAccount, validate, generalQhscDocumentController.getDocument);

routes.patch(
  '/:id',
  verifyToken,
  authAccount,
  updateGeneralQhscDocumentValidationRule(),
  validate,
  generalQhscDocumentController.updateDocument
);

routes.delete(
  '/:id',
  verifyToken,
  authAccount,
  validate,
  deletedAt,
  generalQhscDocumentController.deleteDocument
);

module.exports = routes;
