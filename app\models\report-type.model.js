const mongoose = require('mongoose');

const reportType = new mongoose.Schema(
  {
    terminationTypeName: {
      type: String,
      default: '',
    },
    project: {
      type: mongoose.Types.ObjectId,
      ref: 'project',
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    parameters: [
      {
        name: {
          type: String,
          required: false,
          default: '',
        },
        type: {
          type: String,
          required: false,
          default: '',
        },
        measurement: {
          type: String,
          required: false,
          default: '',
        },
        sortOrder: {
          type: Number,
          required: false,
          default: 0,
        },
        hasThreePhase: {
          type: Boolean,
          required: false,
          default: false,
        },
        isRequired: {
          type: Boolean,
          required: false,
          default: false,
        },
        duration: {
          type: Number,
          required: false,
          default: 0,
        },
        weightage: {
          type: Number,
          required: false,
          default: 1,
        },
        l1: {
          type: String,
          required: false,
          default: '',
        },
        l2: {
          type: String,
          required: false,
          default: '',
        },
        l3: {
          type: String,
          required: false,
          default: '',
        },
        description: {
          type: String,
          required: false,
          default: '',
        },
        optionValue: [
          {
            optionText: String,
          },
        ],
        range: {
          type: Object,
          default: { min: 0, max: 0 },
        },
      },
    ],
    isPublish: {
      type: Boolean,
      default: false,
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: String,
      default: null,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('report-type', reportType);
