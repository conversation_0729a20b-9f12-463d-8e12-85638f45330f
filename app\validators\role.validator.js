const { body, constantUtils } = require('../validators/parent.validator');

exports.roleValidationRule = () => {
  return [
    body('title').notEmpty().withMessage(constantUtils.WAREHOUSE_NAME_REQUIRED),
    body('isActive')
      .notEmpty()
      .withMessage(constantUtils.ISACTIVE_REQUIRED)
      .optional({ checkFalsy: false }),
    body('isAssignAllProjects').notEmpty().withMessage(constantUtils.ISASSIGNALLPROJECTS_REQUIRED),
  ];
};

exports.updateRoleValidationRule = () => {
  return [
    body('title')
      .notEmpty()
      .withMessage(constantUtils.WAREHOUSE_NAME_REQUIRED)
      .optional({ checkFalsy: false }),
    body('isActive')
      .notEmpty()
      .withMessage(constantUtils.ISACTIVE_REQUIRED)
      .optional({ checkFalsy: false }),
  ];
};
