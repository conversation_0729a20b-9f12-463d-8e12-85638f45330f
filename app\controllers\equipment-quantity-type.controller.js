// Services
const equipmentQuantityTypeService = require('../services/equipment-quantity-type.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const commonFunction = require('../utils/common-function.utils');

/**
 * Create EquipmentQuantityType
 *
 * @param {*} req
 * @param {*} res
 */
exports.createEquipmentQuantityType = async (req, res) => {
  try {
    const requestData = req.body;
    const response = await equipmentQuantityTypeService.createEquipmentQuantityType(requestData);
    if (response) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.CREATE_EQUIPMENT_QUANTITY_TYPE, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get EquipmentQuantityType
 *
 * @param {*} req
 * @param {*} res
 */
exports.getEquipmentQuantityType = async (req, res) => {
  try {
    let { account } = req.userData;
    let page = req.query.page ? req.query.page : '';
    let perPage = req.query.perPage ? req.query.perPage : '';
    let sort = req.query.sort && req.query.sort === 'asc' ? 1 : -1;

    let filterData = {
      account: account,
      deletedAt: null,
    };

    const response = await equipmentQuantityTypeService.getEquipmentQuantityType(
      filterData,
      page,
      perPage,
      sort
    );
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.GET_EQUIPMENT_QUANTITY_TYPE, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Update EquipmentQuantityType
 *
 * @param {*} req
 * @param {*} res
 */
exports.updateEquipmentQuantityType = async (req, res) => {
  try {
    let { id } = req.params;
    const requestData = req.body;
    const response = await equipmentQuantityTypeService.getEquipmentQuantityTypeById(id);
    if (!response) {
      return res
        .status(404)
        .json(responseUtils.errorResponse(constantUtils.EQUIPMENT_QUANTITY_TYPE_NOT_FOUND));
    }

    const responseUpdate = await equipmentQuantityTypeService.updateEquipmentQuantityType(
      id,
      requestData
    );
    if (responseUpdate) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    res
      .status(200)
      .json(
        responseUtils.successResponse(constantUtils.UPDATE_EQUIPMENT_QUANTITY_TYPE, responseUpdate)
      );
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Delete EquipmentQuantityType
 *
 * @param {*} req
 * @param {*} res
 */
exports.deleteEquipmentQuantityType = async (req, res) => {
  try {
    let { id } = req.params;
    const response = await equipmentQuantityTypeService.getEquipmentQuantityTypeById(id);
    if (!response) {
      return res
        .status(404)
        .json(responseUtils.errorResponse(constantUtils.EQUIPMENT_QUANTITY_TYPE_NOT_FOUND));
    }
    const responseDelete = await equipmentQuantityTypeService.deleteEquipmentQuantityType(
      id,
      req.deletedAt
    );
    if (responseDelete) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    res
      .status(200)
      .json(
        responseUtils.successResponse(constantUtils.DELETE_EQUIPMENT_QUANTITY_TYPE, responseDelete)
      );
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

exports.commonUpdateSyncApiManage = async account => {
  await commonFunction.updateSyncApiManage({
    syncApis: ['equipmentConfig'],
    account,
  });
};
