const commonUtils = require('../app/utils/common.utils');
const commonFunctionsUtils = require('../app/utils/common-function.utils');
/**
 * Generate Toolbox Talk details pdf
 *
 * @param {*} templateData
 * @returns
 */
exports.toolboxTemplate = async templateData => {
  const { companyPrimaryColor, requestData } = templateData;
  const projectTitle = await commonUtils.alterStringFromRequestString(
    requestData.project.projectNumber
      ? `${requestData.project.projectNumber} - ${requestData.project.title}`
      : requestData.project.title ?? 'N/A'
  );
  const team = await commonUtils.alterStringFromRequestString(requestData.team.teamsWfmName);
  const location = await commonUtils.alterStringFromRequestString(requestData.location.title);
  const submmitedAt = await commonFunctionsUtils.formatDateUTC(requestData.createdAt);
  const notes = requestData.note;
  const hostedBy =
    (await commonUtils.alterStringFromRequestString(
      requestData?.hostedBy?.callingName
        ? requestData.hostedBy.callingName
        : requestData.hostedBy.firstName
    )) +
    ' ' +
    (await commonUtils.alterStringFromRequestString(requestData.hostedBy.lastName));
  const getTableRows = await this.generateTableRows(requestData.memberSignature);
  const getTableImage = await this.generateTableImage(requestData.photos);

  return `
          <!DOCTYPE html>
  <html lang="en">
  
  <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Order Details</title>
      <style>
          html {
              box-sizing: border-box;
          }
  
          *,
          *::before,
          *::after {
              box-sizing: inherit;
          }
  
          :root {
              --primary-color: ${companyPrimaryColor};
              --info-title-color: #4D5464;
              --info-desc-color: #333843;
              --status-color: #9D0202;
              --circle-bg-color: #D9D9D9;
              --circle-text-color: #000000;
              --table-header-border: #E0E6F51A;
              --font-color: #323232;
              --table-border: #E0E6F5;
          }
  
         body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            font-family: Arial, sans-serif;
            width: 100%;
            height: 100%;
            font-size: 12px;
            font-weight: 500;
        }

        .main {
           width: 100%;
            box-sizing: border-box;
            page-break-after: always;
        }

        .header {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;

        }

        #header-text {
            font-size: 20px;
            font-weight: 600;
            color: var(--font-color);
        }

        .vertical-align {
            display: flex;
            flex-direction: column;
            justify-content: center;
            /* gap: 0; */
        }

        .vertical-align p {
            margin: 0;
            padding: 0;
        }

        .custom-table {
         margin-top: 20px;
         width: 100%;
         border-collapse: separate;
         border-spacing: 0;
        }
         .custom-table th {
            padding: 8px;
            text-align: left;
            border: 1px solid var(--table-header-border);
            padding-left: 10px;

        }

        .custom-table td {
            padding: 8px;
            text-align: left;
            border: 1px solid var(--circle-bg-color);
            padding: 10px;
        }

        .table-header th:first-child {
            border-top-left-radius: 4px;
        }

        .table-header th:last-child {
            border-top-right-radius: 4px;
        }

        .custom-table tbody tr:last-child td:first-child {
            border-bottom-left-radius: 4px;
        }

        .custom-table tbody tr:last-child td:last-child {
            border-bottom-right-radius: 4px;
        }

        .header-box1 {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            padding: 10px;
        }

        .header-box {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }

        .title {
            font-size: 12px;
            font-weight: 700;
            color: var(--font-color);
            margin: 0;
        }

        .answer {
            font-size: 12px;
            font-weight: 500;
            color: var(--font-color);
            margin: 0;
        }

        .table-header {
            background-color: var(--primary-color);
            color: var(--circle-bg-color);
        }

        .text-line {
            display: flex;
            align-items: center;
            margin-top: 20px;
        }

        .text-line p {
            margin: 0;
            padding-right: 10px;
            white-space: nowrap;
            font-size: 15px;
            font-weight: 600;
            color: var(--primary-color);
        }

        .text-line hr {
            flex-grow: 1;
            border: none;
            border-top: 1px solid var(--primary-color);
            margin: 0;
            margin-left: 5px;

        }

        .header-title {
            font-size: 12px;
            font-weight: 600;
            color: #FFFFFF;
        }
        .image-bg-container {
              display: flex;
              justify-content: center;
              align-items: center;
              width: 100%;
              height: 100%; /* Changed from fixed height to 100% */
              border-radius: 8px;
              overflow: hidden;
            }
        .image-bg-container-img {
                max-width: 100%; 
                max-height: 100%;
                width: 100%;
                height: 100%;
                border-radius: 8px;
            }

        @media print {
            body {
                width: 100%;
                height: auto;
                display: block;
            }

            .main {
               width: 100%;
                page-break-after: always;
                box-sizing: border-box;
            }

            .header {
                page-break-inside: avoid;
                display: flex;
                align-items: center;

            }

            .Status-section {
                page-break-inside: avoid;
            }

            .table-header {
                background-color: var(--primary-color) !important;
                color: var(--circle-bg-color);
            }

        }
    </style>
  </head>
  
  <body>
      <div class="main">
          <div class="header">
              <div class="vertical-align">
  
                  <p id="header-text">Toolbox talk</p>
              </div>
          </div>
          <div>
              <table class="custom-table">
                  <thead>
                      <tr class="table-header">
                          <th>
                              <div class="header-box">
                                  <p class="header-title">Project</p>
                              </div>
                          </th>
                          <th>
                              <div class="header-box">
                                  <p class="header-title">Location</p>
                              </div>
                          </th>
                          <th>
                              <div class="header-box">
                                  <p class="header-title">Team</p>
                              </div>
  
                          </th>
                          <th>
                              <div class="header-box">
                                  <p class="header-title">Submitted Date</p>
                              </div>
                          </th>
                      </tr>
                  </thead>
                  <tbody>
                      <tr>
                          <td>
                              <p class="answer">${projectTitle}</p>
                          </td>
                          <td>
                              <p class="answer">${location}</p>
                          </td>
                          <td>
                              <p class="answer">${team}</p>
                          </td>
                          <td>
                              <p class="answer">${submmitedAt}</p>
                          </td>
                      </tr>
                  </tbody>
              </table>
  
              <div class="text-line">
                  <p>Notes</p>
                  <hr>
              </div>
              <div style="font-size: 15px;
              font-weight: 500;
              color: var(--font-color);">
                  <p style="line-height: 1.5;">${notes}</p>
                  <div class="text-line">
                      <p>Documents</p>
                  </div>
                   <table class="image-table">
                        ${getTableImage}
                    </table>
          </div>
          <table class="custom-table">
              <thead>
                  <tr class="table-header">
                      <th>
                          <div class="header-box">
                              <p class="header-title">Host Signature</p>
                          </div>
                      </th>
                  </tr>
              </thead>
              <tbody>
                  <tr>
                      <td>
                          <div style="display: flex; justify-content: space-between;">
                              <p class="answer" style="margin-top: 10px;">${hostedBy}</p>
                              <img style="width: 70px; height: 30px;"
                                  src=${requestData.createdBySignature}>
                          </div>
  
                      </td>
                  </tr>
              </tbody>
          </table>
          <table class="custom-table">
              <thead>
                  <tr class="table-header">
                      <th colspan="4" >
                          <div class="header-box">
                              <p class="header-title">Attendance Details</p>
                          </div>
                      </th>
                  </tr>
              </thead>
              <tbody>
                  ${getTableRows}
              </tbody>
          </table>
  
      </div>
  </body>
  
  </html>
        `;
};

/**
 * Get Attendence Details
 *
 * @param {*} tableData
 * @returns
 */
exports.generateTableRows = async tableData => {
  let count = 0;
  let rowtd = '';
  let rows = '';
  for (let data of tableData) {
    if (count !== 0 && count % 4 === 0) {
      rows += `<tr>${rowtd}</tr>`;
      rowtd = '';
    }
    rowtd += `<td>
                    <div style="display: flex; justify-content: space-between;">
                              <p class="answer">${await commonUtils.alterStringFromRequestString(
                                data?.user?.callingName
                                  ? data.user.callingName
                                  : data.user.firstName
                              )} ${await commonUtils.alterStringFromRequestString(
      data.user.lastName
    )}</p>
                              <img style="width: 70px; height: 30px;"
                                  src=${data.signature}>
                    </div>
                </td>`;
    count++;
  }
  if (rowtd) {
    rows += `<tr>${rowtd} </tr>`;
  }
  return rows;
};

/**
 * Get Image Details
 *
 * @param {*} tableData
 * @returns
 */

exports.generateTableImage = async tableData => {
  let count = 0;
  let rowtd = '';
  let rows = '';
  for (let data of tableData) {
    if (count !== 0 && count % 3 === 0) {
      rows += `<tr>${rowtd}</tr>`;
      rowtd = '';
    }

    let compressImg = await commonFunctionsUtils.urlReplacement(data);
    compressImg = await commonFunctionsUtils.convertImageUrlToBase64Image(compressImg);

    if (!compressImg) {
      compressImg = await commonFunctionsUtils.convertImageUrlToBase64Image(data);
    }

    rowtd += `<td style="width: 33.33%;
                    height: 330px;
                    padding: 10px;
                    text-align: center;
                    vertical-align: middle;">
                <div class="image-bg-container">
                    <img class="image-bg-container-img" src=${compressImg} 
                        alt="logo">
                 </div>

            </td>`;
    count++;
  }
  if (rowtd) {
    rows += `<tr>${rowtd} </tr>`;
  }
  return rows;
};
