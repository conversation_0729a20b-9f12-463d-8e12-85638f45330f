// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const HTTP_STATUS = require('../utils/status-codes');

// Service
const syncService = require('../services/sync.service');
const authService = require('../services/auth.service');
const jobService = require('../services/job.service');
const commonFunction = require('../utils/common-function.utils');

/**
 * Sync Up
 *
 * @param {*} req
 * @param {*} res
 */
exports.syncUp = async (req, res) => {
  try {
    let { type, data } = req.body;
    const tokenData = await authService.decodeJwtToken(req.headers.authorization);

    if (!tokenData) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.SEND_AUTHENTICATION_TOKEN));
    }

    type = type !== undefined && type !== '' ? type.toUpperCase() : '';
    const jobData = await jobService.createJob({ jobId: req.body.jobId, type: type });
    let subId = [];
    let modifiedResponse;

    switch (type) {
      case 'HSECARD':
        modifiedResponse = await syncService.hscCardSync(data, req, subId, jobData, tokenData);

        if (modifiedResponse) {
          await commonFunction.updateSyncApiManage({
            syncApis: ['mainConfig'],
            account: req.userData.account,
          });
        }

        return res
          .status(200)
          .json(responseUtils.successResponse(constantUtils.CARD_SYNC_UP, modifiedResponse));

      case 'REPORT':
        modifiedResponse = await syncService.manageReportSync(data, req, subId, jobData, tokenData);

        if (modifiedResponse) {
          await commonFunction.updateSyncApiManage({
            syncApis: ['reportConfig', 'reportUsers'],
            account: req.userData.account,
          });
        }

        return res
          .status(200)
          .json(responseUtils.successResponse(constantUtils.REPORT_SYNC_UP, modifiedResponse));

      case 'SHIFT':
        modifiedResponse = await syncService.shiftSync(data, req, subId, jobData);
        if (modifiedResponse) {
          await commonFunction.updateSyncApiManage({
            syncApis: ['shifts', 'shiftActivities'],
            account: req.userData.account,
          });
        }
        return res
          .status(200)
          .json(responseUtils.successResponse(constantUtils.SHIFT_SYNC_UP, modifiedResponse));

      case 'SHIFT-ACTIVITY':
        modifiedResponse = await syncService.shiftActivitySync(data, req, subId, jobData);

        if (modifiedResponse) {
          await commonFunction.updateSyncApiManage({
            syncApis: ['shifts', 'shiftActivities'],
            account: req.userData.account,
          });
        }

        return res
          .status(200)
          .json(
            responseUtils.successResponse(constantUtils.SHIFT_ACTIVITY_SYNC_UP, modifiedResponse)
          );

      case 'TOOLBOXTALK':
        modifiedResponse = await syncService.toolboxTalkSync(data, req, subId, jobData);

        if (modifiedResponse) {
          await commonFunction.updateSyncApiManage({
            syncApis: ['toolboxConfig'],
            account: req.userData.account,
          });
        }

        return res
          .status(200)
          .json(
            responseUtils.successResponse(constantUtils.TOOLBOX_TALK_SYNC_UP, modifiedResponse)
          );

      default:
        return res.status(400).json(responseUtils.errorResponse(constantUtils.TYPE_NOT_DEFINED));
    }
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Sync Up
 *
 * @param {*} req
 * @param {*} res
 */
exports.syncUpV2 = async (req, res) => {
  try {
    let { type, data } = req.body;
    const tokenData = await authService.decodeJwtToken(req.headers.authorization);

    if (!tokenData) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.SEND_AUTHENTICATION_TOKEN));
    }

    type = type !== undefined && type !== '' ? type.toUpperCase() : '';
    const jobData = await jobService.createJob({ jobId: req.body.jobId, type: type });
    let subId = [];
    let modifiedResponse;

    switch (type) {
      case 'HSECARD':
        modifiedResponse = await syncService.hscCardSync(data, req, subId, jobData, tokenData);

        return res
          .status(HTTP_STATUS.OK)
          .json(responseUtils.successResponse(constantUtils.CARD_SYNC_UP, modifiedResponse));

      case 'REPORT':
        modifiedResponse = await syncService.manageReportSync(data, req, subId, jobData, tokenData);

        return res
          .status(HTTP_STATUS.OK)
          .json(responseUtils.successResponse(constantUtils.REPORT_SYNC_UP, modifiedResponse));

      case 'SHIFT':
        modifiedResponse = await syncService.shiftSyncV2(data, req, subId, jobData);

        return res
          .status(HTTP_STATUS.OK)
          .json(responseUtils.successResponse(constantUtils.SHIFT_SYNC_UP, modifiedResponse));

      case 'SHIFT-ACTIVITY':
        modifiedResponse = await syncService.shiftActivitySync(data, req, subId, jobData);

        return res
          .status(HTTP_STATUS.OK)
          .json(
            responseUtils.successResponse(constantUtils.SHIFT_ACTIVITY_SYNC_UP, modifiedResponse)
          );

      case 'TOOLBOXTALK':
        modifiedResponse = await syncService.toolboxTalkSync(data, req, subId, jobData);

        return res
          .status(HTTP_STATUS.OK)
          .json(
            responseUtils.successResponse(constantUtils.TOOLBOX_TALK_SYNC_UP, modifiedResponse)
          );

      default:
        return res
          .status(HTTP_STATUS.BAD_REQUEST)
          .json(responseUtils.errorResponse(constantUtils.TYPE_NOT_DEFINED));
    }
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error.message));
  }
};
