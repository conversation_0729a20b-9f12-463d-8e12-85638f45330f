// Services
const equipmentUnitService = require('../services/equipment-unit.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const commonFunction = require('../utils/common-function.utils');

/**
 * Create EquipmentUnit
 *
 * @param {*} req
 * @param {*} res
 */
exports.createEquipmentUnit = async (req, res) => {
  try {
    const requestData = req.body;
    const response = await equipmentUnitService.createEquipmentUnit(requestData);
    if (response) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.CREATE_EQUIPMENT_UNIT, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get EquipmentUnit
 *
 * @param {*} req
 * @param {*} res
 */
exports.getEquipmentUnit = async (req, res) => {
  try {
    let { account } = req.userData;
    let page = req.query.page ? req.query.page : '';
    let perPage = req.query.perPage ? req.query.perPage : '';
    let sort = req.query.sort && req.query.sort === 'asc' ? 1 : -1;

    let filterData = {
      account: account,
      deletedAt: null,
    };

    const response = await equipmentUnitService.getEquipmentUnit(filterData, page, perPage, sort);
    res.status(200).json(responseUtils.successResponse(constantUtils.GET_EQUIPMENT_UNIT, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Update EquipmentUnit
 *
 * @param {*} req
 * @param {*} res
 */
exports.updateEquipmentUnit = async (req, res) => {
  try {
    let { id } = req.params;
    const requestData = req.body;
    const response = await equipmentUnitService.getEquipmentUnitById(id);
    if (!response) {
      return res
        .status(404)
        .json(responseUtils.errorResponse(constantUtils.EQUIPMENT_UNIT_NOT_FOUND));
    }

    const responseUpdate = await equipmentUnitService.updateEquipmentUnit(id, requestData);
    if (responseUpdate) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.UPDATE_EQUIPMENT_UNIT, responseUpdate));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Delete EquipmentUnit
 *
 * @param {*} req
 * @param {*} res
 */
exports.deleteEquipmentUnit = async (req, res) => {
  try {
    let { id } = req.params;
    const response = await equipmentUnitService.getEquipmentUnitById(id);
    if (!response) {
      return res
        .status(404)
        .json(responseUtils.errorResponse(constantUtils.EQUIPMENT_UNIT_NOT_FOUND));
    }
    const responseDelete = await equipmentUnitService.deleteEquipmentUnit(id, req.deletedAt);
    if (responseDelete) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.DELETE_EQUIPMENT_UNIT, responseDelete));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

exports.commonUpdateSyncApiManage = async account => {
  await commonFunction.updateSyncApiManage({
    syncApis: ['equipmentConfig'],
    account,
  });
};
