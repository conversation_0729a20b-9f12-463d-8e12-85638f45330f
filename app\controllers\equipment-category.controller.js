// Services
const equipmentCategoryService = require('../services/equipment-category.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const commonUtils = require('../utils/common.utils');
const commonFunction = require('../utils/common-function.utils');

/**
 * Create EquipmentCategory
 *
 * @param {*} req
 * @param {*} res
 */
exports.createEquipmentCategory = async (req, res) => {
  try {
    const requestData = req.body;
    const exist = await equipmentCategoryService.getSingleEquipmentCategoryByFilter({
      $or: [{ name: requestData.name }, { abbreviation: requestData.abbreviation }],
      account: req.userData.account,
      deletedAt: null,
    });

    if (exist) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.EQUIPMENT_CATEGORY_EXIST));
    }

    const response = await equipmentCategoryService.createEquipmentCategory(requestData);
    // update sync api manage data
    if (response) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.CREATE_EQUIPMENT_CATEGORY, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get EquipmentCategories
 *
 * @param {*} req
 * @param {*} res
 */
exports.getEquipmentCategory = async (req, res) => {
  try {
    let { account } = req.userData;
    let page = req.query.page ? req.query.page : '';
    let perPage = req.query.perPage ? req.query.perPage : '';
    let sort;

    if (!req.query?.sortOrder) {
      sort = req.query.sort && req.query.sort === 'asc' ? { createdAt: 1 } : { createdAt: -1 };
    } else {
      sort = req.query.sortOrder === 'asc' ? { name: 1 } : { name: -1 };
    }

    let filterData = {
      account: account,
      deletedAt: null,
      ...(req.query.name && { name: await commonUtils.convertToCaseInsensetive(req.query.name) }),
    };

    const response = await equipmentCategoryService.getEquipmentCategory(
      filterData,
      page,
      perPage,
      sort
    );
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.GET_EQUIPMENT_CATEGORY, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Update EquipmentCategory
 *
 * @param {*} req
 * @param {*} res
 */
exports.updateEquipmentCategory = async (req, res) => {
  try {
    let { id } = req.params;
    const requestData = req.body;
    const response = await equipmentCategoryService.getEquipmentCategoryById(id);
    if (!response) {
      return res
        .status(404)
        .json(responseUtils.errorResponse(constantUtils.EQUIPMENT_CATEGORY_NOT_FOUND));
    }

    const alreadyExist = await equipmentCategoryService.getSingleEquipmentCategoryByFilter({
      _id: { $ne: id },
      abbreviation: requestData.abbreviation,
      account: req.userData.account,
      deletedAt: null,
    });

    if (alreadyExist) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.EQUIPMENT_CATEGORY_EXIST));
    }

    const responseUpdate = await equipmentCategoryService.updateEquipmentCategory(id, requestData);
    // update sync api manage data
    if (responseUpdate) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.UPDATE_EQUIPMENT_CATEGORY, responseUpdate));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Delete EquipmentCategory
 *
 * @param {*} req
 * @param {*} res
 */
exports.deleteEquipmentCategory = async (req, res) => {
  try {
    let { id } = req.params;
    const response = await equipmentCategoryService.getEquipmentCategoryById(id);
    if (!response) {
      return res
        .status(404)
        .json(responseUtils.errorResponse(constantUtils.EQUIPMENT_CATEGORY_NOT_FOUND));
    }
    const responseDelete = await equipmentCategoryService.deleteEquipmentCategory(
      id,
      req.deletedAt
    );
    // update sync api manage data
    if (responseDelete) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.DELETE_EQUIPMENT_CATEGORY, responseDelete));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

exports.commonUpdateSyncApiManage = async account => {
  await commonFunction.updateSyncApiManage({
    syncApis: ['equipmentConfig'],
    account,
  });
};
