// library
const express = require('express');
const routes = express.Router();

// middleware
const {
  verifyToken,
  authAccount,
  deletedAt,
  defaultCreatedDetails,
  updatedBy,
} = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');
const {
  createEquipmentValidationRule,
  updateEquipmentValidationRule,
  validateParamIds,
} = require('../validators/equipment.validator');

// controller
const equipmentController = require('../controllers/equipment.controller');

routes.get('/summary', verifyToken, equipmentController.getEquipmentSummaryView);

routes.patch(
  '/add-stock',
  verifyToken,
  validate,
  authAccount,
  updatedBy,
  equipmentController.additionalStock
);

//Create
routes.post(
  '',
  verifyToken,
  authAccount,
  createEquipmentValidationRule(),
  validate,
  defaultCreatedDetails,
  equipmentController.createEquipment
);

// Get Equipments
routes.get('', verifyToken, authAccount, validate, equipmentController.getEquipments);

// Get Equipment by Search
routes.get(
  '/front-search',
  verifyToken,
  authAccount,
  validate,
  equipmentController.equipmentFrontSearch
);

// Get Equipment by QR Code
routes.get('/qr-code/:qrCode', verifyToken, validate, equipmentController.getEquipmentByQRCode);

routes.post('/bind/qr-code', verifyToken, validate, equipmentController.bindQRCode);

// Get Equipment by Id
routes.get('/:id', verifyToken, authAccount, validate, equipmentController.getEquipmentById);

// Update Equipment
routes.patch(
  '/:id',
  verifyToken,
  updateEquipmentValidationRule(),
  validate,
  updatedBy,
  equipmentController.updateEquipment
);

// Delete EquipmentType
routes.delete('/:id', verifyToken, deletedAt, validate, equipmentController.deleteEquipment);

// Get inventory excel
routes.post(
  '/inventory/export-excel',
  verifyToken,
  authAccount,
  validate,
  equipmentController.getInventoryExcel
);

// Get multiple type equipment quantity detail
routes.get(
  '/:id/multiple-type-equipment-quantity-detail',
  verifyToken,
  authAccount,
  validate,
  equipmentController.getMultipleTypeEquipmentDetail
);

routes.get(
  '/equipment-summary/:dprId',
  verifyToken,
  authAccount,
  equipmentController.getEquipmentSummary
);

// Update Equipment Type
routes.patch(
  '/:id/update-equipment-type',
  verifyToken,
  validateParamIds(),
  validate,
  updatedBy,
  equipmentController.updateEquipmentType
);

module.exports = routes;
