const { createLogger, format, transports } = require('winston');
const { MongoDB } = require('winston-mongodb');
const { combine, timestamp, printf, prettyPrint, errors, splat } = format;
const { DB_NAME, DATABASE_URL } = process.env;

const myFormat = printf(({ level, message, timestamp, stack, url }) => {
  return `${level} : ${url ?? ''} ${timestamp} ${message} ${stack ?? ''}`;
});

const customFormat = printf(({ level, message, timestamp, meta }) => {
  return `${timestamp} [${level.toUpperCase()}]: ${message} ${meta ? JSON.stringify(meta) : ''}`;
});

const loggerMain = () => {
  return createLogger({
    level: 'info',
    format: combine(
      splat(), // Necessary to produce the 'meta' property
      timestamp(),
      prettyPrint(),
      errors({ stack: true }) // <-- use errors format
    ),
    transports: [
      new transports.Console({
        expressFormat: true,
        format: myFormat,
      }), // print the logs in console
    ],
  });
};

// manage the logs in db
const apiLogger = () => {
  let db = DATABASE_URL || `mongodb://127.0.0.1:27017/${DB_NAME}`;
  return createLogger({
    format: combine(timestamp(), customFormat),
    transports: [
      new MongoDB({
        level: 'info',
        db,
        options: { maxPoolSize: 2 },
        collection: 'logs',
        capped: true,
        metaKey: 'meta',
      }),
    ],
  });
};

let logger = loggerMain();
let apiLog = apiLogger();

module.exports = { logger, apiLog };
