// library
const express = require('express');
const routes = express.Router();

// middleware
const {
  verifyToken,
  authAccount,
  deletedAt,
  defaultCreatedDetails,
} = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const certificateController = require('../controllers/certificate.controller');

//Create certificate
routes.post(
  '',
  verifyToken,
  authAccount,
  validate,
  defaultCreatedDetails,
  certificateController.createCertificate
);

// Get certificate
routes.get('', verifyToken, authAccount, validate, certificateController.getAllCertificate);

// Get certificate by Functions
routes.get(
  '/function-certificate',
  verifyToken,
  authAccount,
  validate,
  certificateController.getAllCertificatesForFunction
);

// delete certificate by Id
routes.delete(
  '/:id',
  verifyToken,
  authAccount,
  deletedAt,
  validate,
  certificateController.deleteCertificate
);

module.exports = routes;
