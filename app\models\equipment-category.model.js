const mongoose = require('mongoose');

const EquipmentCategory = new mongoose.Schema(
  {
    name: {
      type: String,
    },
    abbreviation: {
      type: String,
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    isTemporary: {
      type: Boolean,
      default: false,
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

EquipmentCategory.index({ account: 1, deletedAt: 1 });
EquipmentCategory.index({ account: 1, deletedAt: 1, isActive: 1 });
EquipmentCategory.index({ name: 1 });
EquipmentCategory.index({ isActive: 1 });

module.exports = mongoose.model('equipment-category', EquipmentCategory);
