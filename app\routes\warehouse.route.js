// library
const express = require('express');
const routes = express.Router();

// middleware
const {
  verifyToken,
  authAccount,
  deletedAt,
  defaultCreatedDetails,
  updatedBy,
} = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');
const {
  warehouseValidationRule,
  updateWarehouseValidationRule,
} = require('../validators/warehouse.validator');

// controller
const warehouseController = require('../controllers/warehouse.controller');

//create
routes.post(
  '',
  verifyToken,
  authAccount,
  warehouseValidationRule(),
  defaultCreatedDetails,
  validate,
  warehouseController.createWarehouse
);

//get list
routes.get('', verifyToken, authAccount, validate, warehouseController.getWarehouses);

//get warehouse by id
routes.get('/:id', verifyToken, authAccount, validate, warehouseController.getWarehouseById);
routes.get(
  '/:id/equipment',
  verifyToken,
  authAccount,
  validate,
  warehouseController.getProductsByWarehouseId
);

//update warehouse
routes.patch(
  '/:id',
  verifyToken,
  updateWarehouseValidationRule(),
  validate,
  updatedBy,
  warehouseController.updateWarehouse
);

//delete warehouse
routes.delete('/:id', verifyToken, deletedAt, validate, warehouseController.deleteWarehouse);

module.exports = routes;
