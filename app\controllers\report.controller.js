const dateFormat = require('dateformat');

// Responses
const { successResponse, errorResponse } = require('../utils/response.utils');

// Services
const reportService = require('../services/report.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const commonUtils = require('../utils/common.utils');
const responseUtils = require('../utils/response.utils');
const exportPDFUtils = require('../utils/export-pdf.utils');

/**
 * Create Report
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.createReport = async (req, res) => {
  try {
    let reqData = req.body;

    reqData.account = req.userData.account;
    reqData.createdBy = req.userData._id;

    const responseData = await reportService.createReport(reqData);

    return res.status(201).json(successResponse(constantUtils.CREATE_REPORT, responseData));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Filter Report
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.filterReportData = async (req, res) => {
  try {
    let page = req.query.page ? Number(req.query.page) : '';
    let perPage = req.query.perPage ? Number(req.query.perPage) : '';

    let filterData = req.query;
    let removeKeys = ['page', 'perPage', 'sort'];
    filterData = await commonUtils.filterParamsModify(filterData, removeKeys);
    filterData = await commonUtils.getCreatedDateFilter(filterData);

    Object.keys(filterData).forEach(key => {
      filterData[key] = ['project', 'reportType', 'createdBy'].includes(key)
        ? commonUtils.toObjectId(filterData[key])
        : filterData[key];
    });

    filterData.account = req.userData.account;

    const responseData = await reportService.filterReport(filterData, page, perPage);

    return res.status(200).json(successResponse(constantUtils.REPORT_LIST, responseData));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Get report by id
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getDataByReportId = async (req, res) => {
  try {
    let reportId = req.params.id;

    let exist = await reportService.getReportById(reportId);

    if (!exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.REPORT_NOT_EXIST));
    }

    let filterData = { _id: commonUtils.toObjectId(reportId) };

    const responseData = await reportService.filterReport(filterData);

    return res.status(200).json(successResponse(constantUtils.GET_REPORT, responseData[0]));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Update report details - params
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateReportParams = async (req, res) => {
  try {
    let { id, detailId } = req.params;

    let reqData = req.body;

    let exist = await reportService.getReportById(id);

    if (!exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.REPORT_NOT_EXIST));
    }

    let setData = {};

    Object.keys(reqData).forEach(key => {
      setData[`details.$.${key}`] = reqData[key];
    });

    const updateData = await reportService.updateReportParams(id, detailId, setData);

    if (updateData.modifiedCount === 0) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.REPORT_DETAIL_NOT_EXIST));
    }

    const responseData = await reportService.getReportParam(id, detailId);

    return res.status(200).json(successResponse(constantUtils.UPDATE_REPORT, responseData));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Update report
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateReport = async (req, res) => {
  try {
    let id = req.params.id;

    let exist = await reportService.getReportById(id);

    if (!exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.REPORT_NOT_EXIST));
    }

    const responseData = await reportService.updateReport(id, req.body);

    return res.status(200).json(successResponse(constantUtils.UPDATE_REPORT, responseData));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Remove report detail - param
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.removeReportDetail = async (req, res) => {
  try {
    let { id, detailId } = req.params;

    let exist = await reportService.getReportById(id);

    if (!exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.REPORT_NOT_EXIST));
    }

    const responseData = await reportService.removeReportParamDetail(id, detailId);

    return res.status(200).json(successResponse(constantUtils.DELETE_REPORT, responseData));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Delete Report
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.deleteReport = async (req, res) => {
  try {
    let id = req.params.id;

    let exist = await reportService.getReportById(id);

    if (!exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.REPORT_NOT_EXIST));
    }

    let reqData = {
      deletedBy: req.userData._id,
      deletedAt: new Date().toISOString(),
    };

    const responseData = await reportService.deleteReport(id, reqData);

    return res.status(200).json(successResponse(constantUtils.DELETE_REPORT, responseData));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

exports.exportReport = async (req, res) => {
  try {
    let { id } = req.params;
    const nowDate = dateFormat(new Date(), global.constant.DATE_FORMAT_FOR_EXPORT);
    let fileName = `Report-${nowDate}`;

    let exist = await reportService.getReportById(id);

    if (!exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.REPORT_NOT_EXIST));
    }

    let filterData = { _id: commonUtils.toObjectId(id) };

    const responseData = await reportService.filterReport(filterData);

    responseData[0].logo = global.constant.APP_LOGO_PATH;

    const reportPDF = await reportService.createDataForReportPDF(responseData[0]);

    return await exportPDFUtils.exportReportPDF(
      res,
      reportPDF,
      fileName,
      responseData[0].reportType.terminationTypeName,
      global.constant.PDF_TABLE_WIDTH
    );
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Insert Report Params
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.addReportParams = async (req, res) => {
  try {
    let { id } = req.params;

    let reqData = req.body;

    let exist = await reportService.getReportById(id);

    if (!exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.REPORT_NOT_EXIST));
    }

    const insertData = await reportService.addReportParamDetail(id, reqData);

    return res.status(200).json(successResponse(constantUtils.UPDATE_REPORT, insertData));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};
