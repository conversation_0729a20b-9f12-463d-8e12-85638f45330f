const { body, validateParamIds, constantUtils } = require('./parent.validator');

const createReportDocumentValidationRule = () => {
  return [
    body('name').notEmpty().withMessage(constantUtils.DOCUMENT_NAME_REQUIRED),
    body('document').notEmpty().withMessage(constantUtils.CONTRACTUAL_DOCUMENT_REQUIRED),
    body('userProjectReport').notEmpty().withMessage(constantUtils.USER_REPORT_REQUIRED),
  ];
};

module.exports = {
  createReportDocumentValidationRule,
  validateParamIds,
};
