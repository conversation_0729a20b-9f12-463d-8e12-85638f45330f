const mongoose = require('mongoose');

const SyncApiManage = new mongoose.Schema(
  {
    syncApi: {
      type: mongoose.Types.ObjectId,
      ref: 'sync-api',
    },
    hashKey: {
      type: String,
      default: null,
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    deviceId: {
      type: String, // or mongoose.Types.ObjectId if device is its own model
      required: true,
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('sync-api-manage', SyncApiManage);
