const { body, constantUtils } = require('../validators/parent.validator');

exports.projectStringValidationRule = () => {
  return [
    body('name').isString().notEmpty().withMessage(constantUtils.SELECT_VALID_NAME),
    body('fromLocation')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.SELECT_VALID_FROM_LOCATION),
    body('toLocation').isString().notEmpty().withMessage(constantUtils.SELECT_VALID_TO_LOCATION),
    body('project').isString().notEmpty().withMessage(constantUtils.SELECT_VALID_PROJECT),
  ];
};

exports.updateProjectStringValidationRule = () => {
  return [
    body('name')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.SELECT_VALID_NAME)
      .optional({ checkFalsy: false }),
    body('fromLocation')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.SELECT_VALID_FROM_LOCATION)
      .optional({ checkFalsy: false }),
    body('toLocation')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.SELECT_VALID_TO_LOCATION)
      .optional({ checkFalsy: false }),
    body('project')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.SELECT_VALID_PROJECT)
      .optional({ checkFalsy: false }),
  ];
};
