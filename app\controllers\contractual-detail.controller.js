// services
const contractualDetailService = require('../services/contractual-detail.service');
const { successResponse, errorResponse } = require('../utils/response.utils');
const constants = require('../utils/constants.utils');
const userService = require('../services/user.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');

/**
 * Create User
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.create = async (req, res) => {
  try {
    let reqData = req.body;
    let UserId =
      Object.keys(reqData).indexOf('personnelUserId') !== -1 && reqData.personnelUserId !== ''
        ? reqData.personnelUserId
        : req.userData._id;

    const getRecord = await contractualDetailService.getSingleRecord({ userId: UserId });

    if (getRecord) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.CONTRACTUAL_DETAIL_EXIST));
    }

    reqData.userId = UserId;
    reqData.createdBy = UserId;
    reqData.updatedBy = UserId;

    const createData = await contractualDetailService.createData(reqData);

    if (createData) {
      /* update id in users */
      let userData = {
        contractualDetailId: createData._id,
      };

      await userService.updateUser(UserId, userData);

      if (req.body?.drivingLicence || req.body?.seamansBook) {
        await userService.updateUser(reqData.userId, {
          drivingLicence: req.body.drivingLicence,
          seamansBook: req.body.seamansBook,
        });
      }

      return res.status(200).json(successResponse(constants.CREATE_CONTRACTUAL_DETAIL, createData));
    }
  } catch (err) {
    return res.status(500).json(
      errorResponse(constants.SOMETHING_WENT_WRONG, {
        message: err.message,
      })
    );
  }
};

/**
 * Update Records
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateDetail = async (req, res) => {
  try {
    const id = req.params.id;
    const isExist = await contractualDetailService.getSingleRecord({ _id: id });

    if (!isExist) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.CONTRACTUAL_DETAIL_NOT_EXIST));
    }

    const response = await contractualDetailService.updateDetail(id, req.body);
    if (req.body?.drivingLicence || req.body?.seamansBook) {
      await userService.updateUser(isExist.userId, {
        drivingLicence: req.body.drivingLicence,
        seamansBook: req.body.seamansBook,
      });
    }

    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.UPDATE_CONTRACTUAL_DETAIL, response));
  } catch (err) {
    return res
      .status(500)
      .json(errorResponse(constants.SOMETHING_WENT_WRONG, { message: err.message }));
  }
};

/**
 * Remove document link
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.removeFile = async (req, res) => {
  try {
    const id = req.params.id;
    const { file } = req.body;
    const isExist = await contractualDetailService.getSingleRecord({ _id: id });

    if (!isExist) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.CONTRACTUAL_DETAIL_NOT_EXIST));
    }

    await contractualDetailService.removeFile(id, file);

    return res.status(200).json(responseUtils.successResponse(constantUtils.REMOVE_DOCUMENT));
  } catch (err) {
    return res
      .status(500)
      .json(errorResponse(constants.SOMETHING_WENT_WRONG, { message: err.message }));
  }
};

/**
 * Update File Data
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateContractFileData = async (req, res) => {
  try {
    let { id, fileId } = req.params;
    let reqData = req.body;

    // check the data exist
    const isExist = await contractualDetailService.getSingleRecord({ _id: id });

    if (!isExist) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.CONTRACTUAL_DETAIL_NOT_EXIST));
    }

    // set the parameters for update
    let setData = {};

    Object.keys(reqData).forEach(key => {
      setData[`identityProof.$.${key}`] = reqData[key];
    });

    // set the parameter for search
    let fieldSearch = { _id: id, 'identityProof._id': fileId };

    // update the file data
    const updateData = await contractualDetailService.updateFileData(fieldSearch, setData);

    // check if record not updated
    if (updateData.modifiedCount === 0) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.DOCUMENT_NOT_EXIST));
    }

    // set the paramters for seach in document
    let filerFiled = {};
    filerFiled['identityProof'] = { $elemMatch: { _id: fileId } };

    // get the updated record
    const responseData = await contractualDetailService.getFileData(id, filerFiled);

    return res
      .status(200)
      .json(successResponse(constantUtils.UPDATE_CONTRACTUAL_DETAIL, responseData));
  } catch (err) {
    return res
      .status(500)
      .json(errorResponse(constants.SOMETHING_WENT_WRONG, { message: err.message }));
  }
};
