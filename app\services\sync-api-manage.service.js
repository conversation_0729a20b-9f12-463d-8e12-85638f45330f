const SyncApiManage = require('../models/sync-api-manage.model');

/**
 * Create Sync API Manage
 *
 * @param {*} category
 * @returns
 */
exports.createSyncApiManage = async (requestData, session) => {
  const syncApiManage = new SyncApiManage(requestData);
  return await syncApiManage.save({ session });
};

exports.findOneSyncApiManage = async requestData => {
  return await SyncApiManage.findOne(requestData);
};

exports.findSyncApiManage = async requestData => {
  return await SyncApiManage.find(requestData);
};

exports.updateSyncApiManage = async (filter, requestData) => {
  return await SyncApiManage.updateMany(filter, requestData);
};
