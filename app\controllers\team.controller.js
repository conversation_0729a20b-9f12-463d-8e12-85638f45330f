require('dotenv').config();

// Services
const teamService = require('../services/team.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const commonFunction = require('../utils/common-function.utils');

/**
 * Create Team
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.createTeam = async (req, res) => {
  try {
    const { project } = req.body;
    const reqData = req.body;

    const exist = await teamService.getTeamByName(
      req.userData.account,
      project,
      reqData.teamsWfmName
    );

    if (exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.TEAM_EXIST));
    }

    reqData.account = req.userData.account;
    const createdTeam = await teamService.createTeam(reqData);

    // update sync api manage data
    if (createdTeam) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }

    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.CREATE_TEAM, createdTeam));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Update Team
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateTeam = async (req, res) => {
  try {
    let id = req.params.id;
    const exist = await teamService.getTeamById(id, req.body);

    if (!exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_TEAM));
    }
    const response = await teamService.updateTeam(id, req.body);
    // update sync api manage data
    if (response) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    return res.status(200).json(responseUtils.successResponse(constantUtils.UPDATE_TEAM, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Delete Team
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.deleteTeam = async (req, res) => {
  try {
    let id = req.params.id;
    const exist = await teamService.getTeamById(id, req.body);

    if (!exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_TEAM));
    }
    const response = await teamService.deleteTeam(id, req.deletedAt);
    // update sync api manage data
    if (response) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    return res.status(200).json(responseUtils.successResponse(constantUtils.DELETE_TEAM, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

exports.commonUpdateSyncApiManage = async account => {
  await commonFunction.updateSyncApiManage({
    syncApis: ['toolboxConfig'],
    account,
  });
};
