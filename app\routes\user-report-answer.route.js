// library
const express = require('express');
const routes = express.Router();

// Validator
const validator = require('../validators/user-report-answer.validator');

// middleware
const { verifyToken, authAccount } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const userReportAnswerController = require('../controllers/user-report-answer.controller');

routes.patch(
  '/assign-title',
  verifyToken,
  authAccount,
  validate,
  userReportAnswerController.assignAnswerTitle
);
// Create User Report Answer
routes.post(
  '',
  verifyToken,
  authAccount,
  validator.userReportAnswerValidationRule(),
  validate,
  userReportAnswerController.saveUserReportAnswer
);

// Update User Report Answer
routes.patch(
  '/:id',
  verifyToken,
  authAccount,
  validator.validateParamIds(),
  validator.updateUserReportAnswerValidationRule(),
  validate,
  userReportAnswerController.updateUserReportAnswer
);

routes.patch(
  '/:submittedAnswerTitleId/unassign/:userReportAnswerId',
  verifyToken,
  authAccount,
  validate,
  userReportAnswerController.unAssignAnswerTitle
);

routes.post(
  '/update-is-printable',
  verifyToken,
  authAccount,
  validator.updateIsPrintableValidationRule(),
  validate,
  userReportAnswerController.updateIsPrintableValue
);

routes.get(
  '/migrate-answer-titles',
  verifyToken,
  authAccount,
  validate,
  userReportAnswerController.assignUnassignMigrationQuery
);

module.exports = routes;
