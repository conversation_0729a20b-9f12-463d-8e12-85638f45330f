const os = require('os');
const fs = require('fs');

/**
 * Get comprehensive health status of the Node.js application
 */
exports.getHealthStatus = async (req, res) => {
  try {
    const startTime = process.hrtime.bigint();
    const isProduction = process.env.NODE_ENV === 'production';

    // Get all metrics
    const memory = await getMemoryMetrics();
    const cpu = getCpuMetrics();
    const disk = getDiskMetrics(isProduction);
    const network = getNetworkMetrics(isProduction);
    const database = await getDatabaseHealth();
    const eventLoop = await getEventLoopMetrics();
    const buildInfo = getBuildInfo();

    // Calculate overall status based on component health
    let overallStatus = 'healthy';

    // Determine overall status from component statuses
    if (
      memory.processMemory.status === 'critical' ||
      memory.systemMemory.status === 'critical' ||
      cpu.systemCPU.status === 'critical' ||
      eventLoop.status === 'critical' ||
      database.status === 'critical'
    ) {
      overallStatus = 'critical';
    } else if (
      memory.processMemory.status === 'warning' ||
      memory.systemMemory.status === 'warning' ||
      cpu.systemCPU.status === 'warning' ||
      eventLoop.status === 'warning'
    ) {
      overallStatus = 'warning';
    }

    const endTime = process.hrtime.bigint();
    const responseTime = Number(endTime - startTime) / 1000000;

    const healthData = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      responseTime: `${responseTime.toFixed(2)}ms`,
      uptime: {
        process: formatUptime(process.uptime()),
        system: formatUptime(os.uptime()),
      },
      version: buildInfo.version,
      gitCommit: buildInfo.gitCommit,
      buildTimestamp: buildInfo.buildTimestamp,
      memory,
      cpu,
      disk,
      network,
      database,
      eventLoop,
      environment: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        nodeEnv: process.env.NODE_ENV || 'development',
      },
    };

    res.status(200).json(healthData);
  } catch (error) {
    res.status(500).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message,
      uptime: {
        process: formatUptime(process.uptime()),
        system: formatUptime(os.uptime()),
      },
    });
  }
};

/**
 * Get memory metrics with human-readable format and status indicators
 */
async function getMemoryMetrics() {
  const memUsage = process.memoryUsage();
  const totalMem = os.totalmem();
  const freeMem = os.freemem();
  const usedMem = totalMem - freeMem;

  const heapUsagePercent = Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100);
  const systemMemoryPercent = Math.round((usedMem / totalMem) * 100);

  return {
    processMemory: {
      rss: formatBytes(memUsage.rss),
      heapTotal: formatBytes(memUsage.heapTotal),
      heapUsed: formatBytes(memUsage.heapUsed),
      heapUsagePercent,
      external: formatBytes(memUsage.external),
      status: heapUsagePercent > 95 ? 'critical' : heapUsagePercent > 80 ? 'warning' : 'healthy',
    },
    systemMemory: {
      total: formatBytes(totalMem),
      used: formatBytes(usedMem),
      free: formatBytes(freeMem),
      usagePercent: systemMemoryPercent,
      status:
        systemMemoryPercent > 90 ? 'critical' : systemMemoryPercent > 75 ? 'warning' : 'healthy',
    },
  };
}

/**
 * Get CPU metrics with proper cores vs threads distinction
 */
function getCpuMetrics() {
  const cpuUsage = process.cpuUsage();
  const loadAvg = os.loadavg();
  const cpus = os.cpus();

  // Calculate CPU usage percentage
  const cpuUsagePercent = calculateCpuUsage(cpus);

  // os.cpus() returns logical cores (threads), not physical cores
  // Most modern CPUs have 2 threads per core (hyper-threading)
  const threads = cpus.length;
  const estimatedCores = Math.ceil(threads / 2); // Rough estimation

  return {
    processCPU: {
      user: Math.round(cpuUsage.user / 1000), // Convert to milliseconds
      system: Math.round(cpuUsage.system / 1000),
      total: Math.round((cpuUsage.user + cpuUsage.system) / 1000),
    },
    systemCPU: {
      loadAverage: {
        '1min': Math.round(loadAvg[0] * 100) / 100,
        '5min': Math.round(loadAvg[1] * 100) / 100,
        '15min': Math.round(loadAvg[2] * 100) / 100,
      },
      usagePercent: cpuUsagePercent.percentage,
      status: cpuUsagePercent.status,
    },
    cores: {
      threads, // Logical cores (what os.cpus() actually returns)
      estimatedPhysicalCores: estimatedCores, // Estimated physical cores
      model: cpus[0]?.model || 'Unknown',
      speed: cpus[0]?.speed || 0,
    },
  };
}

/**
 * Get disk metrics with production sanitization
 */
function getDiskMetrics(isProduction = false) {
  try {
    const stats = fs.statSync(process.cwd());

    if (isProduction) {
      return {
        accessible: true,
        status: 'healthy',
      };
    }

    return {
      currentDirectory: process.cwd(),
      accessible: true,
      status: 'healthy',
      lastModified: stats.mtime,
    };
  } catch (error) {
    return {
      accessible: false,
      status: 'critical',
      error: isProduction ? 'Access denied' : error.message,
    };
  }
}

/**
 * Get network metrics with production sanitization
 */
function getNetworkMetrics(isProduction = false) {
  const networkInterfaces = os.networkInterfaces();
  const hostname = os.hostname();

  if (isProduction) {
    // Only show basic info in production
    const interfaceCount = Object.keys(networkInterfaces).length;
    return {
      hostname: hostname.includes('.') ? hostname.split('.')[0] : hostname, // Sanitize hostname
      interfaceCount,
      status: 'healthy',
    };
  }

  // Show more details in non-production
  const publicIPs = [];
  for (const [, addresses] of Object.entries(networkInterfaces)) {
    addresses.forEach(addr => {
      if (!addr.internal && addr.family === 'IPv4') {
        publicIPs.push(addr.address);
      }
    });
  }

  return {
    hostname,
    interfaceCount: Object.keys(networkInterfaces).length,
    publicIPs: publicIPs.length > 0 ? publicIPs : ['No public IPs'],
    status: 'healthy',
  };
}

/**
 * Get database health status with enhanced information
 */
async function getDatabaseHealth() {
  try {
    const mongoose = require('mongoose');
    const connectionState = mongoose.connection.readyState;

    const states = {
      0: 'disconnected',
      1: 'connected',
      2: 'connecting',
      3: 'disconnecting',
    };

    const isHealthy = connectionState === 1;

    return {
      mongodb: {
        status: isHealthy ? 'healthy' : 'critical',
        state: states[connectionState] || 'unknown',
        host: mongoose.connection.host || 'unknown',
        name: mongoose.connection.name || 'unknown',
        collections: mongoose.connection.db
          ? Object.keys(mongoose.connection.db.collections || {}).length
          : 0,
        readyState: connectionState,
      },
    };
  } catch (error) {
    return {
      mongodb: {
        status: 'critical',
        error: error.message,
      },
      status: 'critical',
    };
  }
}

/**
 * Get event loop metrics with enhanced status
 */
function getEventLoopMetrics() {
  const startTime = process.hrtime.bigint();

  // eslint-disable-next-line no-undef
  return new Promise(resolve => {
    setImmediate(() => {
      const endTime = process.hrtime.bigint();
      const lag = Number(endTime - startTime) / 1000000; // Convert to milliseconds

      resolve({
        lagMs: Math.round(lag * 100) / 100,
        status: lag > 500 ? 'critical' : lag > 100 ? 'warning' : 'healthy',
      });
    });
  });
}

/**
 * Get build and version information
 */
function getBuildInfo() {
  try {
    // Try to get git commit hash
    let gitCommit = 'unknown';
    let buildTimestamp = null;

    try {
      const { execSync } = require('child_process');
      gitCommit = execSync('git rev-parse HEAD', {
        encoding: 'utf8',
        timeout: 1000,
      }).trim();
      const gitDate = execSync('git log -1 --format="%ci"', {
        encoding: 'utf8',
        timeout: 1000,
      }).trim();
      buildTimestamp = new Date(gitDate).toISOString();
    } catch (error) {
      // Git not available or not a git repository
      gitCommit = 'unavailable';
    }

    // Get version from package.json
    let version = '1.0.1'; // fallback
    try {
      const packageJson = require('../../package.json');
      version = packageJson.version;
    } catch (error) {
      // package.json not found
    }

    return {
      version,
      gitCommit: gitCommit.substring(0, 8), // Short hash
      buildTimestamp: buildTimestamp || new Date().toISOString(),
    };
  } catch (error) {
    return {
      version: '1.0.1',
      gitCommit: 'error',
      buildTimestamp: new Date().toISOString(),
    };
  }
}

/**
 * Format bytes to human-readable format with appropriate units
 */
function formatBytes(bytes) {
  const mb = bytes / (1024 * 1024);
  const gb = bytes / (1024 * 1024 * 1024);

  if (gb >= 1) {
    return `${Math.round(gb * 100) / 100} GB`;
  } else {
    return `${Math.round(mb * 100) / 100} MB`;
  }
}

/**
 * Calculate CPU usage percentage
 */
function calculateCpuUsage(cpus) {
  let totalIdle = 0;
  let totalTick = 0;

  cpus.forEach(cpu => {
    for (const type in cpu.times) {
      totalTick += cpu.times[type];
    }
    totalIdle += cpu.times.idle;
  });

  const idle = totalIdle / cpus.length;
  const total = totalTick / cpus.length;
  const usage = 100 - ~~((100 * idle) / total);

  return {
    percentage: usage,
    status: usage > 80 ? 'critical' : usage > 60 ? 'warning' : 'healthy',
  };
}

/**
 * Format uptime in human readable format
 */
function formatUptime(seconds) {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  return {
    raw: seconds,
    formatted: `${days}d ${hours}h ${minutes}m ${secs}s`,
    days,
    hours,
    minutes,
    seconds: secs,
  };
}
