// Services
const certificateService = require('../services/certificate.service');
// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const commonUtils = require('../utils/common.utils');

/**
 * Get All Certificate
 *
 * @param {*} req
 * @param {*} res
 */
exports.getAllCertificate = async (req, res) => {
  try {
    const newReq = {
      account: req.userData.account,
      project: req.query.project,
      function: req.query.function,
      deletedAt: null,
    };
    const certificateList = await certificateService.getAllCertificate(newReq);
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.USER_CERTIFICATE_LIST, certificateList));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Create Certificate
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.createCertificate = async (req, res) => {
  try {
    const { project, certificates, functionId } = req.body;
    const newReq = {
      account: req.userData.account,
      project: project,
      function: functionId,
      certificates: certificates,
    };
    const filterData = {
      project,
      account: req.userData.account,
      function: functionId,
      isDefault: false,
      deletedAt: null,
    };
    const alreadyExist = await certificateService.getCertificateByName(filterData);
    if (alreadyExist) {
      const updatedData = await certificateService.updateCertificate(alreadyExist._id, {
        certificates: certificates,
      });
      return res
        .status(200)
        .json(responseUtils.successResponse(constantUtils.CREATE_CERTIFICATE, updatedData));
    }
    const createdCertifcate = await certificateService.createCertificate(newReq);
    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.CREATE_CERTIFICATE, createdCertifcate));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Delete Single Certificate
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.deleteCertificate = async (req, res) => {
  try {
    const id = commonUtils.toObjectId(req.params.id);
    const alreadyExist = await certificateService.getCertificate(
      req.userData.account,
      req.query.project,
      req.query.function
    );
    if (!alreadyExist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.CERTIFICATE_NOT_EXIST));
    }
    for (let index = 0; index < alreadyExist.certificates.length; index++) {
      if (alreadyExist.certificates[index].equals(id)) {
        alreadyExist.certificates.splice(index, 1);
      }
    }
    const updatedData = await certificateService.updateCertificate(alreadyExist.id, {
      certificates: alreadyExist.certificates,
    });
    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.REMOVE_DOCUMENT, updatedData));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get All Certificate
 *
 * @param {*} req
 * @param {*} res
 */

exports.getAllCertificatesForFunction = async (req, res) => {
  try {
    if (!commonUtils.isValidId(req.query.project)) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.INVALID_PROJECT_ID));
    }
    let page = req.query.page ? Number(req.query.page) : null;
    let perPage = req.query.perPage ? Number(req.query.perPage) : null;
    let sortOrder;
    let sortOrderField;
    if (!req.query.sortOrder) {
      sortOrder = req.query.sort === 'asc' ? 1 : -1;
      sortOrderField = false;
    } else {
      sortOrderField = true;
      sortOrder = req.query.sortOrder === 'asc' ? 1 : -1;
    }

    const newReq = {
      account: commonUtils.toObjectId(req.userData.account),
      project: commonUtils.toObjectId(req.query.project),
      deletedAt: null,
    };
    let certificateList = await certificateService.getFunctionCertificatesByProjectId(
      newReq,
      sortOrderField,
      sortOrder,
      page,
      perPage,
      req.query.name
    );

    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.USER_CERTIFICATE_LIST, certificateList));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};
