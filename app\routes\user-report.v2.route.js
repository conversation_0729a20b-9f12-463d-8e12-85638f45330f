const routes = require('express').Router();

// middleware
const { verifyToken, authAccount } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const userReportController = require('../controllers/user-report.controller');

// get report by project, manage-report, location, assets
routes.get(
  '/final-reports',
  verifyToken,
  authAccount,
  validate,
  userReportController.getReportsOptimized
);

module.exports = routes;
