const request = require('supertest');
const app = require('../../app/server');

// create safety-cards
describe('POST /api/safety-cards', () => {
  const token = test.constant.TOKEN;
  const safetyCardsData = test.constant.SAFETY_CARD_DATA;

  it('returns 200 and message Safety Card has been created successfully', async () => {
    const response = await request(app)
      .post('/api/safety-cards')
      .set('Authorization', `Bearer ${token}`)
      .send(safetyCardsData);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Safety Card has been created successfully',
      status: true,
    });
  });

  it('returns 422 with message Required field cannot be empty.', async () => {
    const response = await request(app)
      .post('/api/safety-cards')
      .set('Authorization', `Bearer ${token}`)
      .send(safetyCardsData);
    expect(response.status).toBe(422);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Required field cannot be empty.',
      status: false,
    });
  });

  it('returns 400 with message safety card already exist', async () => {
    const response = await request(app)
      .post('/api/safety-cards')
      .set('Authorization', `Bearer ${token}`)
      .send(safetyCardsData);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Safety card already exist',
      status: false,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).post('/api/safety-cards').send(safetyCardsData);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});

// get AllSafetyCard
describe('GET /api/safety-cards', () => {
  const token = test.constant.TOKEN;

  it('returns 200 and message get all safety-cards List', async () => {
    const response = await request(app)
      .get('/api/safety-cards')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: [{}, {}],
      message: 'Safety Card list was retireved successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).get('/api/safety-cards');
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});

//  update safety-cards
describe('PATCH /api/safety-cards/:id', () => {
  const token = test.constant.TOKEN;
  const id = test.constant.ID;
  const updateData = test.constant.SAFETY_CARD_UPDATE_DATA;

  it('returns 200 and message update safety-cards', async () => {
    const response = await request(app)
      .patch(`/api/safety-cards/${id}`)
      .set('Authorization', `Bearer ${token}`)
      .send(updateData);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Safety Card has been updated successfully',
      status: true,
    });
  });

  it('returns 422 with message Required field cannot be empty.', async () => {
    const response = await request(app)
      .post(`/api/safety-cards/${id}`)
      .set('Authorization', `Bearer ${token}`)
      .send();
    expect(response.status).toBe(422);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Required field cannot be empty.',
      status: false,
    });
  });

  // Site does not exist

  it('returns 400 with message No Safety Card', async () => {
    const response = await request(app)
      .post('/api/safety-cards/6412a388268e9b25cef57a30')
      .set('Authorization', `Bearer ${token}`)
      .send(updateData);
    expect(response.status).toBe(404);
    expect(response.body).toMatchObject({});
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).patch(`/api/safety-cards/${id}`).send(updateData);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});

//  Get safety-cards
describe('GET /api/safety-cards/:id', () => {
  const token = test.constant.TOKEN;
  const id = test.constant.ID;

  it('returns 200 and message Safety Card was retrieved by id successfully', async () => {
    const response = await request(app)
      .get(`/api/safety-cards/${id}`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Safety Card was retrieved by id successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).get(`/api/safety-cards/${id}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });

  it('returns 400 with message No Safety Card', async () => {
    const response = await request(app)
      .get('/api/safety-cards/64119ddc6c1d88fde480fd40')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'No Safety Card',
      status: false,
    });
  });
});

//  Delete safety-cards by id
describe('DELETE /api/safety-cards/:id', () => {
  const token = test.constant.TOKEN;
  const id = test.constant.SAFETY_CARD_ID;

  it('returns 200 and message Safety Card deleted successfully', async () => {
    const response = await request(app)
      .delete(`/api/safety-cards/${id}`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Safety Card deleted successfully',
      status: true,
    });
  });

  it('returns 400 with message No Safety Card exist', async () => {
    const response = await request(app)
      .delete('/api/safety-cards/6412b7a6644aa8e810eb73fb')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'No Safety Card',
      status: false,
    });
  });
});

//  Get safety-cards
describe('GET /api/safety-cards/form/:id/:cardtype', () => {
  const token = test.constant.TOKEN;
  const cardtype = 'safe';
  const id = test.constant.SAFETY_CARD_ID;

  it('returns 200 and message Safety Card update form', async () => {
    const response = await request(app)
      .get(`/api/safety-cards/form/${id}/${cardtype}`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Safety Card update form',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).get(`/api/safety-cards/form/${id}/${cardtype}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });

  it('returns 400 with message No Safety Card', async () => {
    const response = await request(app)
      .get(`/api/safety-cards/form/${id}/${cardtype}`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'No Safety Card',
      status: false,
    });
  });
});

// get AllSafetyCard filter
describe('POST /api/safety-cards/filter', () => {
  const token = test.constant.TOKEN;
  const filterData = {
    project: '64119ddc6c1d88fde480fd47',
    created: 'this_week',
  };

  it('returns 200 and message Safety Card list was retireved successfully', async () => {
    const response = await request(app)
      .post('/api/safety-cards/filter')
      .set('Authorization', `Bearer ${token}`)
      .send(filterData);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: [{}, {}],
      message: 'Safety Card list was retireved successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).post('/api/safety-cards/filter');
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});

// Post bulk data

describe('POST /api/safety-cards/bulk', () => {
  const token = test.constant.TOKEN;
  const bulkData = test.constant.BULK_DATA;

  it('returns 200 and message Safety Card has been created successfully', async () => {
    const response = await request(app)
      .post('/api/safety-cards/bulk')
      .set('Authorization', `Bearer ${token}`)
      .send(bulkData);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: [{}, {}],
      message: 'Safety Card has been created successfully',
      status: true,
    });
  });

  it('returns 400 with message safety card already exist', async () => {
    const response = await request(app)
      .post('/api/safety-cards/bulk')
      .set('Authorization', `Bearer ${token}`)
      .send(bulkData);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Safety card already exist',
      status: false,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).post('/api/safety-cards/bulk');
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});
