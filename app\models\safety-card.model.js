const mongoose = require('mongoose');

const safetyCard = new mongoose.Schema(
  {
    title: {
      type: String,
      default: null,
    },
    project: {
      type: mongoose.Types.ObjectId,
      ref: 'project',
      default: null,
    },
    location: {
      type: mongoose.Types.ObjectId,
      ref: 'location',
      default: null,
    },
    // default project name
    defaultProject: {
      type: mongoose.Types.ObjectId,
      ref: 'project',
      default: null,
    },
    // default project flag
    isDefault: {
      type: Boolean,
      default: false,
    },
    riskFactor: {
      type: Number,
      default: 0,
    },
    description: {
      type: String,
      required: false,
      default: null,
    },
    dynamicFields: [
      {
        title: {
          type: String,
          required: false,
        },
        value: {
          type: Array,
          required: false,
        },
        fieldId: {
          type: mongoose.Types.ObjectId,
          ref: 'form-builder',
        },
      },
    ],
    cardType: {
      type: String,
      required: false,
      default: null,
    },
    severity: {
      type: mongoose.Types.ObjectId,
      ref: 'severity',
      default: null,
    },
    likelihood: {
      type: mongoose.Types.ObjectId,
      ref: 'likelihood',
      default: null,
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    subject: {
      type: String,
      default: null,
    },
    category: {
      type: mongoose.Types.ObjectId,
      ref: 'category',
      default: null,
    },
    type: {
      type: mongoose.Types.ObjectId,
      ref: 'type',
      default: null,
    },
    item: {
      type: String,
      required: false,
      default: '',
    },
    actionsTaken: {
      type: String,
      required: false,
      default: '',
    },
    correctiveAction: {
      type: String,
      required: false,
      default: '',
    },
    preventiveAction: {
      type: String,
      required: false,
      default: '',
    },
    estimatedDelayCost: {
      type: String,
      required: false,
      default: '',
    },
    statusUpdate: {
      type: String,
      required: false,
      default: '',
    },
    productQuality: {
      type: Boolean,
      required: false,
      default: false,
    },
    status: {
      type: String,
      enum: ['open', 'submitted', 'in_discussion', 'closed', 'archived'],
      default: 'open',
    },
    images: [
      {
        name: {
          type: String,
          required: false,
        },
        url: {
          type: String,
          required: false,
        },
      },
    ],
    time: {
      type: Date,
      default: new Date(),
    },
    explanationOfChanges: {
      type: String,
      required: false,
      default: '',
    },
    cardLogs: [
      {
        user: {
          type: mongoose.Types.ObjectId,
          ref: 'user',
        },
        time: {
          type: Date,
          default: null,
        },
        status: {
          type: String,
          default: '',
        },
        action: {
          type: String,
          default: '',
        },
        version: {
          type: String,
          default: '',
        },
      },
    ],
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    updatedAt: {
      type: Date,
      default: null,
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
    isDeleted: {
      type: Boolean,
      default: false,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('safetycard', safetyCard);
