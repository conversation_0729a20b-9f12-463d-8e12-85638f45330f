// library
const express = require('express');
const routes = express.Router();

// middleware
const {
  verifyToken,
  authAccount,
  deletedAt,
  defaultCreatedDetails,
  updatedBy,
} = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');
const {
  createHSCodeValidationRule,
  updateHSCodeValidationRule,
} = require('../validators/hs-code.validator');

// controller
const hsCodeController = require('../controllers/hs-code.controller');

//Create
routes.post(
  '',
  verifyToken,
  authAccount,
  createHSCodeValidationRule(),
  validate,
  defaultCreatedDetails,
  hsCodeController.createHSCode
);

// Get HSCode
routes.get('', verifyToken, authAccount, validate, hsCodeController.getHSCode);

// Update HSCode
routes.patch(
  '/:id',
  verifyToken,
  updateHSCodeValidationRule(),
  validate,
  updatedBy,
  hsCodeController.updateHSCode
);

// Delete HSCode
routes.delete('/:id', verifyToken, deletedAt, validate, hsCodeController.deleteHSCode);

module.exports = routes;
