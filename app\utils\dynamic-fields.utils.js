// Sevices
const formBuilderService = require('../services/form-builder.service');
const accountLicenceService = require('../services/account-licence.service');
const permissionService = require('../services/permission.service');
const roleAgreementService = require('../services/role-agreement.service');

// Json format
const { properties, option } = require('./json-format.utils');

//Congig Constant
const { screens, screenOrder } = require('./config-constants.utils');

const IMAGE_URL = process.env.IMAGE_URL;

const GetAllSafetyCardDynamicFeilds = async (user, type) => {
  const dynamicFields = await formBuilderService.getAllFields(user.account, type);
  let dynamicFormFieldsArray = dynamicFields.map(field => {
    let fieldProperties = { ...properties };
    fieldProperties.title = field.fieldName;
    fieldProperties.type = field.fieldType;
    fieldProperties.fieldSortOrder = field.fieldSortOrder;
    fieldProperties.id = field.fieldName.replace(/\s/g, '_');
    fieldProperties.questionId = field._id;
    fieldProperties.IsRequired = field.isRequired;
    fieldProperties.iconUrl = IMAGE_URL + '/pencil-sharp.png';
    if (field.fieldType === 'options' || field.fieldType === 'checkbox') {
      fieldProperties.options = field.optionValue.map((item, i) => {
        let tempOption = { ...option };
        tempOption.title = item.optionText;
        tempOption.id = (i + 1).toString();
        return tempOption;
      });
    }
    if (field.fieldType === 'slider') {
      fieldProperties.range = field.range[0];
    }
    return fieldProperties;
  });

  return dynamicFormFieldsArray;
};

const GetSafetyCardDynamicFeildsByIds = async ids => {
  const dynamicFields = await formBuilderService.getFields(ids);
  let dynamicFormFieldsArray = dynamicFields.map(field => {
    let fieldProperties = { ...properties };
    fieldProperties.title = field.fieldName;
    fieldProperties.type = field.fieldType;
    fieldProperties.fieldSortOrder = field.fieldSortOrder;
    fieldProperties.isActive = field.isActive;
    fieldProperties.id = field.fieldName.replace(/ +/g, '');
    fieldProperties.questionId = field._id;
    fieldProperties.IsRequired = field.IsRequired;
    fieldProperties.iconUrl = IMAGE_URL + '/pencil-sharp.png';
    if (field.fieldType === 'options' || field.fieldType === 'checkbox') {
      fieldProperties.options = field.optionValue.map((item, i) => {
        let tempOption = { ...option };
        tempOption.title = item.optionText;
        tempOption.id = (i + 1).toString();
        return tempOption;
      });
    }
    if (field.fieldType === 'slider') {
      fieldProperties.range = field.range[0];
    }
    return fieldProperties;
  });

  return dynamicFormFieldsArray;
};

/**
 * Check and get the licence access by account
 *
 * @param {*} account
 * @returns
 */
const accountLicences = async account => {
  // Get licences and permissoins of account
  const accountLicenceData = await accountLicenceService.getAccountLicenceByAccount({
    account,
    isApproved: true,
    isRejected: false,
  });

  //Get All permissions and prepare the permission data
  const getAllPermissions = await permissionService.getAllPermissions();
  let permissionsData = ['default'];
  Object.keys(getAllPermissions).forEach(key => {
    permissionsData = [...permissionsData, getAllPermissions[key].name];
  });

  // Prepare the all screens by permissions
  permissionsData.forEach(ele => {
    switch (ele) {
      case 'Safe':
        screens.safeScreen = ele;
        break;
      case 'Unsafe':
        screens.unsafeScreen = ele;
        break;
      case 'NCR':
        screens.ncrScreen = ele;
        break;
      case 'Incident':
        screens.incidentScreen = ele;
        break;
      case ele.indexOf('Shift') !== -1 ? ele : false:
        screens.openShiftScreen = ele;
        screens.shiftActivityScreen = ele;
        break;
      case 'Report':
        screens.reportScreen = ele;
        break;
      case 'Inventory':
        screens.equipmentScreen = ele;
        break;
      case 'Warehouse':
        screens.warehouseScreen = ele;
        break;
      case 'Request':
        screens.equipmentRequestScreen = ele;
        break;
      case 'Order Approval':
        screens.equipmentApprovalScreen = ele;
        break;
      case 'Toolbox Talk':
        screens.toolBoxTalkScreen = ele;
        break;
      case 'Personnel':
        screens.userManagementScreen = ele;
        break;
      case 'Personnel Basic':
        screens.personnelBasicScreen = ele;
        break;
      case 'DPR':
        screens.dprScreen = ele;
        break;
      case 'Training Matrix':
        screens.trainingMatricsScreen = ele;
        break;
      case 'Report Setup':
        screens.reportSetupScreen = ele;
        break;
      case 'Project Setup':
        screens.projectSetupScreen = ele;
        break;
      case 'Certificate Approval':
        screens.certificateApprovalScreen = ele;
        break;
      case 'default':
        screens.submitFeedbackScreen = ele;
        screens.settingScreen = ele;
        screens.projectDocumentScreen = ele;
        break;
    }
  });

  // Prepare array of accessible licences
  let licences = ['default'];
  Object.keys(accountLicenceData).forEach(key => {
    licences = [...licences, accountLicenceData[key].permission.name];
  });

  // Prepare the screens as per the accessible licences
  let accessScreens = [];
  Object.keys(screens).forEach(key => {
    if (licences.includes(screens[key])) {
      accessScreens = [...accessScreens, key];
    }
  });

  accessScreens = accessScreens.sort((ele, elemt) => {
    return screenOrder.indexOf(ele) - screenOrder.indexOf(elemt);
  });

  return accessScreens;
};

/**
 * Get role agreement of the screen
 *
 * @param {*} type
 * @param {*} user
 * @returns
 */
const GetRoleAgreement = async (type, user) => {
  let falseAgreement = {
    create: false,
    read: false,
    update: false,
    delete: false,
  };
  try {
    if (type === 'default')
      return {
        create: true,
        read: true,
        update: true,
        delete: true,
      };

    let { roleId, account } = user;
    const permission = await permissionService.getPermissionByName(type);
    if (permission.length === 0) return falseAgreement;
    const accountLicenceData = await accountLicenceService.getAccountLicenceByAccount({
      account: account,
      permission: permission[0]._id,
      isApproved: true,
      isRejected: false,
    });
    if (accountLicenceData.length === 0) return falseAgreement;
    const roleAgreement = await roleAgreementService.getRoleAgreementByRequestData({
      role: roleId._id,
      account: account,
      accountLicence: accountLicenceData[0]._id,
    });
    return roleAgreement[0].agreement;
  } catch (error) {
    return falseAgreement;
  }
};

module.exports = {
  GetAllSafetyCardDynamicFeilds,
  GetSafetyCardDynamicFeildsByIds,
  accountLicences,
  GetRoleAgreement,
};
