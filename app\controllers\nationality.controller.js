// service
const nationalityService = require('../services/nationality.service');

// utils
const responseUtils = require('../utils/response.utils');
const constantUtils = require('../utils/constants.utils');
const HTTP_STATUS = require('../utils/status-codes');

/**
 * Get Nationality
 *
 * @param {*} req
 * @param {*} res
 */
exports.getNationality = async (req, res) => {
  try {
    const nationalityData = await nationalityService.getNationality();

    res
      .status(HTTP_STATUS.OK)
      .json(responseUtils.successResponse(constantUtils.GET_NATIONALITY, nationalityData));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Create Nationality
 *
 * @param {*} req
 * @param {*} res
 */
exports.createNationality = async (req, res) => {
  try {
    const {
      code = '',
      alphaCode2 = '',
      alphaCode3 = '',
      country = '',
      nationality = '',
    } = req.body;

    let nationalityData = await nationalityService.createNationality({
      code,
      alphaCode2,
      alphaCode3,
      country,
      nationality,
    });

    res
      .status(HTTP_STATUS.OK)
      .json(responseUtils.successResponse(constantUtils.CREATE_NATIONALITY, nationalityData));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error.message));
  }
};
