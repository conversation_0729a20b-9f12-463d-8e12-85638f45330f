exports.header = async templateData => {
  return `<div style="width: 100%; padding: 3mm 6mm; font-size: 12px;">
    <img src=${templateData.base64CompanyLogo} alt="company_logo" height="35px" width="120px" />
   </div>`;
};

exports.footer = async templateData => {
  return `
    <div style="width: 100%; display: flex; justify-content: space-between; align-items: center; padding: 0 9mm">
        <div style="height: 12px; display: flex; align-items: center; gap: 5px; font-size: 8px; color: #545353">Powered by: <img src=${templateData.base64ReynardLogo} alt="Reynard_logo" height="12px"></div>
        <div style="font-size: 8px; color: #545353;">Page <span class="pageNumber"></span> of
            <span class="totalPages"></span>
        </div>
    </div>
    `;
};
