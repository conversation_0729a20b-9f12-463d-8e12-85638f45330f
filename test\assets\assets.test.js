const request = require('supertest');
const app = require('../../app/server');

// create assets
describe('POST /api/assets', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.GIDsM2SC5QM7vXnjNkgAJg9cum_AGJLxBxG5PCZ5lcU';
  const assetsData = {
    project: '64119ddc6c1d88fde480fd47',
    fromLocation: '6412bafe963bd50565409893',
    toLocation: '6412bafe963bd50565409893',
    manufacturer: 'efgh2',
    typeMm2: 'test 412',
    string: '63f73da1ee2b3ce103152dc7',
    cableName: 'cable 4112',
  };

  it('returns 200 and message Asset has been created successfully', async () => {
    const response = await request(app)
      .post('/api/assets')
      .set('Authorization', `Bearer ${token}`)
      .send(assetsData);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Asset has been created successfully',
      status: true,
    });
  });

  it('returns 422 with message Required field cannot be empty.', async () => {
    const response = await request(app)
      .post('/api/assets')
      .set('Authorization', `Bearer ${token}`)
      .send(assetsData);
    expect(response.status).toBe(422);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Required field cannot be empty.',
      status: false,
    });
  });

  it('returns 404 with message Asset already exist', async () => {
    const response = await request(app)
      .post('/api/assets')
      .set('Authorization', `Bearer ${token}`)
      .send(assetsData);
    expect(response.status).toBe(404);
    expect(response.body).toEqual({
      message: 'Asset already exist',
      status: false,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).post('/api/assets').send(assetsData);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});

// getAll assets
describe('GET /api/assets?project=${projectId}', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.GIDsM2SC5QM7vXnjNkgAJg9cum_AGJLxBxG5PCZ5lcU';
  const projectId = '64119ddc6c1d88fde480fd47';

  it('returns 200 and message Asset list was retireved successfully', async () => {
    const response = await request(app)
      .get(`/api/assets?project=${projectId}`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: [{}],
      message: 'Asset list was retireved successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).get('/api/assets');
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});

//  update assets
describe('PATCH /api/assets/:id', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.GIDsM2SC5QM7vXnjNkgAJg9cum_AGJLxBxG5PCZ5lcU';
  const id = '6417ff175baff1ce28a9e6d7';
  const updateData = {
    cableName: 'cable 6',
  };
  it('returns 200 and message Asset has been updated successfully', async () => {
    const response = await request(app)
      .patch(`/api/assets/${id}`)
      .set('Authorization', `Bearer ${token}`)
      .send(updateData);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Asset has been updated successfully',
      status: true,
    });
  });

  it('returns 422 with message Required field cannot be empty.', async () => {
    const response = await request(app)
      .post(`/api/assets/${id}`)
      .set('Authorization', `Bearer ${token}`)
      .send();
    expect(response.status).toBe(422);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Required field cannot be empty.',
      status: false,
    });
  });

  it('returns 404 with message Asset does not exist', async () => {
    const response = await request(app)
      .post('/api/assets/6412a388268e9b25cef57a30')
      .set('Authorization', `Bearer ${token}`)
      .send(updateData);
    expect(response.status).toBe(404);
    expect(response.body).toMatchObject({
      message: 'Asset does not exist',
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).patch(`/api/assets/${id}`).send(updateData);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
  it('returns 400 with message Asset already exist', async () => {
    const response = await request(app)
      .patch(`/api/assets/${id}`)
      .set('Authorization', `Bearer ${token}`)
      .send(updateData);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Asset already exist',
      status: false,
    });
  });
});

//  Get assetsById
describe('GET /api/assets/:id', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.GIDsM2SC5QM7vXnjNkgAJg9cum_AGJLxBxG5PCZ5lcU';
  const id = '6417ff175baff1ce28a9e6d7';

  it('returns 200 and message Asset fetched Successfully', async () => {
    const response = await request(app)
      .get(`/api/assets/${id}`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Asset fetched Successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).get(`/api/assets/${id}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });

  it('returns 404 with message Asset does not exist', async () => {
    const response = await request(app)
      .get('/api/assets/64119ddc6c1d88fde480fd40')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(404);
    expect(response.body).toEqual({
      message: 'Asset does not exist',
      status: false,
    });
  });
});

//  Delete assetsById
describe('DELETE /api/assets/:id', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.GIDsM2SC5QM7vXnjNkgAJg9cum_AGJLxBxG5PCZ5lcU';
  const id = '64180332339ed165aa37fc6c';

  it('returns 200 and message Asset has been deleted successfully', async () => {
    const response = await request(app)
      .delete(`/api/assets/${id}`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Asset has been deleted successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).delete(`/api/assets/${id}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });

  it('returns 404 with message Asset does not exist', async () => {
    const response = await request(app)
      .delete('/api/assets/6412b7a6644aa8e810eb73fb')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(404);
    expect(response.body).toEqual({
      message: 'Asset does not exist',
      status: false,
    });
  });
});
