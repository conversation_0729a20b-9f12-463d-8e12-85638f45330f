const mongoose = require('mongoose');

const ReportQuestion = mongoose.Schema(
  {
    title: {
      type: String,
    },
    sortOrder: {
      type: Number,
      default: 0,
    },
    duration: {
      type: Number,
      default: 0,
    },
    isRequired: {
      type: Boolean,
      default: true,
    },
    weight: {
      type: Number,
      default: 0,
    },
    supportedContent: [
      {
        subText: {
          type: String,
        },
        path: {
          type: String,
        },
        isPrintable: {
          type: Boolean,
          default: true,
        },
        sortOrder: {
          type: Number,
          default: 0,
        },
      },
    ],
    report: {
      type: mongoose.Types.ObjectId,
      ref: 'report',
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('report-question', ReportQuestion);
