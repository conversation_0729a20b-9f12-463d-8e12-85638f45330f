// library
const express = require('express');
const routes = express.Router();

// middleware
const { verifyToken, deletedAt, authAccount } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const questionController = require('../controllers/question.controller');

// Validator
const validator = require('../validators/question.validator');

// create question
routes.post(
  '',
  verifyToken,
  authAccount,
  validator.questionValidationRule(),
  validate,
  questionController.createQuestion
);

// get all questions
routes.get('', verifyToken, authAccount, validate, questionController.getAllQuestions);

// update question
routes.patch(
  '/:id',
  verifyToken,
  validator.updateQuestionValidationRule(),
  validate,
  questionController.updateQuestion
);

// delete question
routes.delete('/:id', verifyToken, deletedAt, validate, questionController.deleteQuestion);

module.exports = routes;
