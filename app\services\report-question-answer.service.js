/* models */
const ReportQuestionAnswer = require('../models/report-question-answer.model');

/**
 * Create Report Questions Answers
 *
 * @param {*} requestData
 * @returns
 */
exports.createReportQuestionAnswer = async requestData => ReportQuestionAnswer.create(requestData);

exports.insertManyReportQuestionAnswer = async (requestData, session) => {
  return ReportQuestionAnswer.insertMany(requestData, { session });
};

exports.updateReportQuestionAnswer = async (id, update, session) => {
  return ReportQuestionAnswer.findByIdAndUpdate(id, update, { new: true, session });
};

exports.updateManyReportQuestionAnswer = async (filter, update, session) => {
  return ReportQuestionAnswer.updateMany(filter, update, { new: true, session });
};

exports.updateQuestionAnswerTitle = async (answerId, titleId, update) => {
  return ReportQuestionAnswer.findOneAndUpdate(
    { _id: answerId, 'title._id': titleId },
    { $set: update }
  );
};

exports.addQuestionAnswerTitle = async (answerId, requestedData) => {
  return ReportQuestionAnswer.updateOne({ _id: answerId }, { $push: { title: requestedData } });
};

/**
 * Update Db Migration
 *
 * @param {*} table
 * @param {*} filter
 * @param {*} update
 * @returns
 */
exports.updateReportQuestionAnswerDb = async (filter, update) => {
  return ReportQuestionAnswer.updateMany(filter, update);
};

/**
 * Get Report Question Answer
 *
 * @param {*} ids
 */
exports.getReportQuestionAnswer = async ids => {
  let aggregationFunction = [
    { $match: { reportQuestion: ids, deletedAt: null } },
    {
      $lookup: {
        from: 'parameter-types',
        localField: 'parameterType',
        foreignField: '_id',
        pipeline: [{ $project: { name: 1, uniqueKey: 1, isActive: 1 } }],
        as: 'parameterType',
      },
    },
    {
      $unwind: {
        path: '$parameterType',
      },
    },
    { $sort: { sortOrder: 1 } },
    {
      $project: {
        _id: 1,
        title: {
          $filter: {
            input: '$title',
            as: 'titleObj',
            cond: { $eq: ['$$titleObj.isActive', true] },
          },
        },
        parameterType: 1,
        option: 1,
        range: 1,
        numberOfAnswers: 1,
        sortOrder: 1,
      },
    },
  ];
  return await ReportQuestionAnswer.aggregate(aggregationFunction);
};

/**
 * Get Report Question Answer
 *
 * @param {*} ids
 */
exports.getPrintableReportQuestionAnswer = async (ids, isPrintable) => {
  let aggregationFunction = [
    { $match: { reportQuestion: ids, deletedAt: null } },
    {
      $lookup: {
        from: 'parameter-types',
        localField: 'parameterType',
        foreignField: '_id',
        pipeline: [{ $project: { name: 1, uniqueKey: 1, isActive: 1 } }],
        as: 'parameterType',
      },
    },
    {
      $unwind: {
        path: '$parameterType',
      },
    },
    { $sort: { sortOrder: 1 } },
    {
      $project: {
        _id: 0,
        questionId: '$_id',
        title: {
          $filter: {
            input: '$title',
            as: 'titleObj',
            cond: {
              $and: [
                { $eq: ['$$titleObj.isActive', true] },
                { $in: ['$$titleObj.isPrintable', isPrintable] },
              ],
            },
          },
        },
        parameterType: 1,
        option: 1,
        range: 1,
        numberOfAnswers: 1,
        sortOrder: 1,
      },
    },
  ];
  return await ReportQuestionAnswer.aggregate(aggregationFunction);
};

exports.getReportQuestionAnswerById = async id => ReportQuestionAnswer.findById(id);

exports.assignUnassignMigrationQuery = async () => {
  return ReportQuestionAnswer.updateMany(
    {
      'title.isPrintable': { $exists: false },
      'title.isRequired': { $exists: false },
    },
    {
      $set: { 'title.$[].isPrintable': true, 'title.$[].isRequired': true },
    }
  );
};
