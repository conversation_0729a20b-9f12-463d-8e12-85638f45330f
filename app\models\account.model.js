const mongoose = require('mongoose');

const Account = new mongoose.Schema(
  {
    name: {
      type: String,
    },
    accountOwner: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    logo: {
      type: String,
    },
    organizationAddress: {
      type: String,
      default: '',
    },
    organizationCountry: {
      type: String,
      default: '',
    },
    globalConfig: {
      type: String,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    email: {
      type: String,
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    licence: {
      type: mongoose.Types.ObjectId,
      ref: 'licence',
    },
    syncUpTime: {
      type: Object,
      default: {
        hours: parseInt(process.env.DEFAULT_SYNCUP_HOURS),
        min: parseInt(process.env.DEFAULT_SYNCUP_MINUTES),
      },
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('account', Account);
