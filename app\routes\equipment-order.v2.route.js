// library
const express = require('express');
const routes = express.Router();

// middleware
const { verifyToken } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const equipmentOrderController = require('../controllers/equipment-order.controller');

// create equipment Order Request
routes.post('', verifyToken, validate, equipmentOrderController.createEquipmentOrderV2);

routes.post(
  '/shopping-cart',
  verifyToken,
  equipmentOrderController.createEquipmentOrderFromShoppingCartV2
);

// change order status
routes.patch('/change-status', verifyToken, validate, equipmentOrderController.changeOrderStatusV2);

module.exports = routes;
