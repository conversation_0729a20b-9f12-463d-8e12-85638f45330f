const request = require('supertest');
const app = require('../../app/server');

// create likelihoods
describe('POST /api/likelihoods', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.6O0ROXpENOlmf3PIz45n_UBxGoCb0DMSKVKKCMPLP6w';
  const likelihoodsData = {
    title: 'Negligible2',
  };
  it('returns 200 and message Likelihood has been created successfully', async () => {
    const response = await request(app)
      .post('/api/likelihoods')
      .set('Authorization', `Bearer ${token}`)
      .send(likelihoodsData);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Likelihood has been created successfully',
      status: true,
    });
  });

  it('returns 422 with message Required field cannot be empty.', async () => {
    const response = await request(app)
      .post('/api/likelihoods')
      .set('Authorization', `Bearer ${token}`)
      .send(likelihoodsData);
    expect(response.status).toBe(422);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Required field cannot be empty.',
      status: false,
    });
  });

  it('returns 404 with message Likelihood already exist', async () => {
    const response = await request(app)
      .post('/api/likelihoods')
      .set('Authorization', `Bearer ${token}`)
      .send(likelihoodsData);
    expect(response.status).toBe(404);
    expect(response.body).toEqual({
      message: 'Likelihood already exist',
      status: false,
    });
  });
  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).post('/api/likelihoods').send(likelihoodsData);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});

// getAll likelihoods
describe('GET /api/likelihoods', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.I513fTkTfDgrekZVxyysoPEtU5Rmtr3nM-7HB7Hj7hE';

  it('returns 200 and message Likelihood list was retireved successfully', async () => {
    const response = await request(app)
      .get('/api/likelihoods')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: [{}, {}],
      message: 'Likelihood list was retireved successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).get('/api/likelihoods');
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});

//  update likelihoods
describe('PATCH /api/likelihoods/:id', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.I513fTkTfDgrekZVxyysoPEtU5Rmtr3nM-7HB7Hj7hE';
  const id = '64144de709fea1e7edffd123';
  const updateData = {
    title: 'Negligible3',
  };
  it('returns 200 and message Likelihood has been updated successfully', async () => {
    const response = await request(app)
      .patch(`/api/likelihoods/${id}`)
      .set('Authorization', `Bearer ${token}`)
      .send(updateData);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Likelihood has been updated successfully',
      status: true,
    });
  });

  it('returns 422 with message Required field cannot be empty.', async () => {
    const response = await request(app)
      .post(`/api/likelihoods/${id}`)
      .set('Authorization', `Bearer ${token}`)
      .send();
    expect(response.status).toBe(422);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Required field cannot be empty.',
      status: false,
    });
  });

  it('returns 404 with message Likelihood does not exist', async () => {
    const response = await request(app)
      .post('/api/likelihoods/6412a388268e9b25cef57a30')
      .set('Authorization', `Bearer ${token}`)
      .send(updateData);
    expect(response.status).toBe(404);
    expect(response.body).toMatchObject({
      message: 'Likelihood does not exist',
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).patch(`/api/likelihoods/${id}`).send(updateData);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});

//  Get likelihoodsById
describe('GET /api/likelihoods/:id', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.I513fTkTfDgrekZVxyysoPEtU5Rmtr3nM-7HB7Hj7hE';
  const id = '64144de709fea1e7edffd123';

  it('returns 200 and message Likelihood fetched successfully', async () => {
    const response = await request(app)
      .get(`/api/likelihoods/${id}`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Likelihood fetched successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).get(`/api/likelihoods/${id}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });

  it('returns 404 with message Likelihood does not exist', async () => {
    const response = await request(app)
      .get('/api/likelihoods/64119ddc6c1d88fde480fd40')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(404);
    expect(response.body).toEqual({
      message: 'Likelihood does not exist',
      status: false,
    });
  });
});

//  Delete likelihoodsById
describe('DELETE /api/likelihoods/:id', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.I513fTkTfDgrekZVxyysoPEtU5Rmtr3nM-7HB7Hj7hE';
  const id = '64144de709fea1e7edffd123';

  it('returns 200 and message Likelihood has been deleted successfully', async () => {
    const response = await request(app)
      .delete(`/api/likelihoods/${id}`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Likelihood has been deleted successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).delete(`/api/likelihoods/${id}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });

  it('returns 404 with message Likelihood does not exist', async () => {
    const response = await request(app)
      .delete('/api/likelihoods/6412b7a6644aa8e810eb73fb')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(404);
    expect(response.body).toEqual({
      message: 'Likelihood does not exist',
      status: false,
    });
  });
});
