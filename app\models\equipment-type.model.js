const mongoose = require('mongoose');

const EquipmentType = new mongoose.Schema(
  {
    type: {
      type: String,
    },
    equipmentCategory: {
      type: mongoose.Types.ObjectId,
      ref: 'equipment-category',
    },
    equipmentUnit: {
      type: mongoose.Types.ObjectId,
      ref: 'equipment-unit',
    },
    currencyUnit: {
      type: mongoose.Types.ObjectId,
      ref: 'currency-unit',
    },
    price: {
      type: Number,
    },
    quantityType: {
      type: mongoose.Types.ObjectId,
      ref: 'equipment-quantity-type',
    },
    hsCode: {
      type: mongoose.Types.ObjectId,
      ref: 'hs-code',
    },
    certificateTypes: [
      {
        type: mongoose.Types.ObjectId,
      },
    ],
    showOnDpr: {
      type: Boolean,
      default: false,
    },
    ceNorms: {
      type: mongoose.Types.ObjectId,
      default: null,
      ref: 'ce-norms',
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    isTemporary: {
      type: Boolean,
      default: false,
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

EquipmentType.index({ account: 1, deletedAt: 1 });
EquipmentType.index({ account: 1, deletedAt: 1, isActive: 1 });
EquipmentType.index({ account: 1, deletedAt: 1, equipmentCategory: 1 });
EquipmentType.index({ type: 1 });
EquipmentType.index({ equipmentCategory: 1 });
EquipmentType.index({ equipmentUnit: 1 });
EquipmentType.index({ currencyUnit: 1 });
EquipmentType.index({ quantityType: 1 });
EquipmentType.index({ hsCode: 1 });
EquipmentType.index({ isActive: 1 });
EquipmentType.index({ isTemporary: 1 });
EquipmentType.index({ createdAt: -1 });

module.exports = mongoose.model('equipment-type', EquipmentType);
