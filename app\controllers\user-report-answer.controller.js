// Services
const userReportAnswerService = require('../services/user-report-answer.service');
const reportQuestionAnswerService = require('../services/report-question-answer.service');
const userReportService = require('../services/user-report.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const commonUtils = require('../utils/common.utils');
const { successResponse, errorResponse } = require('../utils/response.utils');
const commonfunctionUtils = require('../utils/common-function.utils');

/**
 * Create User Report Answer
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.saveUserReportAnswer = async (req, res) => {
  try {
    let { reportQuestion, report, userReport, reportQuestionAnswers } = req.body;

    for (let element of reportQuestionAnswers) {
      let { id, answers } = element;

      // Check Report Question Answer has Printable value
      answers = await this.checkReportQuestionAnswer(id, answers, userReport);
      let filter = {
        reportQuestion,
        report,
        userReport,
        reportQuestionAnswer: id,
        account: req.userData.account,
        createdBy: req.userData._id,
        deletedAt: null,
      };
      const exists = await userReportAnswerService.getSingleUserReportAnswer(filter);
      if (exists) {
        // Check Is Printable exist in user report answer
        answers = await this.checkIsPrintableValueInExistingAnswer(exists, answers);
        // Update Answers
        let updateData = {
          answers,
          updatedBy: req.userData._id,
          updatedAt: new Date(),
        };
        await userReportAnswerService.updateUserReportAnswer(exists._id, updateData);
      } else {
        // Create Answers
        let prepareAnswerData = {
          reportQuestion,
          report,
          userReport,
          reportQuestionAnswer: id,
          answers,
          account: req.userData.account,
          createdBy: req.userData._id,
          createdAt: new Date(),
        };

        await userReportAnswerService.createUserReportAnswer(prepareAnswerData);
      }
    }

    await this.commonUpdateSyncApiManage(req.userData.account);

    return res.status(200).json(successResponse(constantUtils.CREATE_USER_REPORT_ANSWER));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

exports.checkReportQuestionAnswer = async (id, answers, userReport, session = null) => {
  const response = await reportQuestionAnswerService.getReportQuestionAnswerById(id);

  const getUserReport = await userReportService.getUserReportById(userReport, session);

  const getSimilerReport = await userReportService.getUserReportByFilter({
    userProjectReport: getUserReport.userProjectReport,
  });

  const remainingReport = getSimilerReport.filter(
    element => element._id.toString() !== getUserReport._id.toString()
  );

  for (let data of answers) {
    let answerData = response.title.find(
      element => element._id.toString() === data.answerTitleId.toString()
    );

    data.isPrintable = true;
    if (answerData && 'isPrintable' in answerData) {
      data.isPrintable = answerData.isPrintable;
    }
  }

  for (let report of remainingReport) {
    const userReportAnswer = await userReportAnswerService.getSingleUserReportAnswer({
      userReport: report._id,
      reportQuestionAnswer: id,
      deletedAt: null,
    });

    if (userReportAnswer !== null) {
      for (let data of answers) {
        let answerData = userReportAnswer.answers.find(
          element => element.answerTitleId.toString() === data.answerTitleId.toString()
        );

        if (answerData && 'isPrintable' in answerData) {
          data.isPrintable = answerData.isPrintable;
        }
      }
    }
  }

  return answers;
};

exports.checkIsPrintableValueInExistingAnswer = async (exist, answers) => {
  for (let data of answers) {
    const answerData = exist.answers.find(element =>
      element.answerTitleId.equals(data.answerTitleId)
    );

    if (answerData && 'isPrintable' in answerData) {
      data.isPrintable = answerData.isPrintable;
    }
  }

  return answers;
};

/**
 * Update User Report Answer (Single Answer)
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateUserReportAnswer = async (req, res) => {
  try {
    let { id } = req.params;
    let reqData = req.body;
    let userId = req.userData._id;

    let filter = {
      _id: id,
      account: req.userData.account,
      deletedAt: null,
    };

    const exists = await userReportAnswerService.getSingleUserReportAnswer(filter);
    if (!exists) {
      return res.status(400).json(errorResponse(constantUtils.NO_USER_REPORT_ANSWER));
    }

    let { answerTitleId, answer, isPrintable } = reqData;
    await userReportAnswerService.updateUserReportAnswerByTitle(
      id,
      answerTitleId,
      answer,
      isPrintable
    );

    let updateData = {
      updatedBy: userId,
      updatedAt: new Date(),
    };
    const responseData = await userReportAnswerService.updateUserReportAnswer(id, updateData);

    if (responseData) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }

    return res
      .status(200)
      .json(successResponse(constantUtils.UPDATE_USER_REPORT_ANSWER, responseData));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Unassign Answer Title
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.unAssignAnswerTitle = async (req, res) => {
  try {
    const { userReportAnswerId, submittedAnswerTitleId } = req.params;

    if (
      !commonUtils.isValidId(userReportAnswerId) ||
      !commonUtils.isValidId(submittedAnswerTitleId)
    ) {
      res.status(400).json(errorResponse(constantUtils.INVALID_ID));
    }

    const response = await userReportAnswerService.unAssignAnswerTitle(
      userReportAnswerId,
      submittedAnswerTitleId,
      req.body.answerTitleId
    );

    if (!response) {
      return res.status(400).json(errorResponse(constantUtils.NO_USER_REPORT_ANSWER));
    }

    return res.status(200).json(successResponse(constantUtils.ANSWER_TITLE_UNASSIGNED));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Assign Answer Title
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.assignAnswerTitle = async (req, res) => {
  try {
    const {
      userReportAnswerId,
      submittedAnswerTitleId,
      userAnswerTitleNewId,
      answer,
      answerId,
      reportQuestion,
      reportQuestionAnswer,
      createdBy,
    } = req.body;
    if (
      !commonUtils.isValidId(userReportAnswerId) ||
      !commonUtils.isValidId(submittedAnswerTitleId) ||
      !commonUtils.isValidId(userAnswerTitleNewId) ||
      !commonUtils.isValidId(reportQuestion) ||
      !commonUtils.isValidId(reportQuestionAnswer) ||
      !commonUtils.isValidId(createdBy)
    ) {
      return res.status(400).json(errorResponse(constantUtils.INVALID_ID));
    }

    const exist = await userReportAnswerService.getActiveAnswerTitle(
      userAnswerTitleNewId,
      reportQuestion,
      reportQuestionAnswer,
      createdBy
    );

    if (exist) {
      return res.status(400).json(errorResponse(constantUtils.ALREADY_EXIST));
    }

    const response = await userReportAnswerService.assignAnswerTitle(
      userReportAnswerId,
      submittedAnswerTitleId,
      userAnswerTitleNewId,
      answer,
      answerId,
      reportQuestion,
      reportQuestionAnswer
    );

    if (!response) {
      return res.status(400).json(errorResponse(constantUtils.NO_USER_REPORT_ANSWER));
    }

    return res.status(200).json(successResponse(constantUtils.ANSWER_TITLE_ASSIGNED));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Update Is Printable Value
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateIsPrintableValue = async (req, res) => {
  try {
    let { userProjectReport, reportQuestionAnswer, answerTitleId, isPrintable } = req.body;

    let filter = {
      userProjectReport,
      account: req.userData.account,
      deletedAt: null,
    };

    const getUserReports = await userReportService.getUserReportByFilter(filter);
    if (getUserReports.length === 0) {
      return res.status(400).json(errorResponse(constantUtils.USER_REPORT_NOT_EXIST));
    }

    for (let userReport of getUserReports) {
      const getUserReportAns = await userReportAnswerService.getUserReportAnswers({
        userReport: userReport._id,
        reportQuestionAnswer,
      });

      for (let answerData of getUserReportAns) {
        await userReportAnswerService.updateUserReportAnswerByTitle(
          answerData._id,
          answerTitleId,
          null,
          isPrintable
        );
      }
    }

    return res.status(200).json(successResponse(constantUtils.UPDATE_USER_REPORT_ANSWER));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

exports.assignUnassignMigrationQuery = async (req, res) => {
  try {
    await userReportAnswerService.assignUnassignMigrationQuery();
    await reportQuestionAnswerService.assignUnassignMigrationQuery();

    return res.status(200).json(successResponse(constantUtils.UPDATE_USER_REPORT_ANSWER));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

exports.commonUpdateSyncApiManage = async account => {
  await commonfunctionUtils.updateSyncApiManage({
    syncApis: ['reportUsers', 'reportConfig', 'allUserReports', 'reportNewFormConfig'],
    account,
  });
};
