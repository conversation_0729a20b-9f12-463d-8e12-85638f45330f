// library
const express = require('express');
const routes = express.Router();

// Validator
const validator = require('../validators/shift-activity.validator');

// middleware
const { verifyToken, authAccount } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const shiftActivityController = require('../controllers/shift-activity.controller');

routes.get(
  '/:dprId/members-summary',
  verifyToken,
  authAccount,
  validate,
  shiftActivityController.getPersonnelListSummary
);

routes.get(
  '/:dprId/daily-activity-log',
  verifyToken,
  authAccount,
  validate,
  shiftActivityController.dailyActivityLogs
);

// Create Shift Activity
routes.post(
  '',
  verifyToken,
  authAccount,
  validator.shiftActivityValidationRule(),
  validate,
  shiftActivityController.createShiftActivity
);
// Update Data
routes.patch(
  '/:id',
  verifyToken,
  authAccount,
  validate,
  shiftActivityController.updateShiftActivity
);
// Deleted Data
routes.delete(
  '/:id',
  verifyToken,
  authAccount,
  validate,
  shiftActivityController.deleteShiftActivity
);

routes.get(
  '/:dprId/summary',
  verifyToken,
  authAccount,
  validate,
  shiftActivityController.getActivitySummary
);

module.exports = routes;
