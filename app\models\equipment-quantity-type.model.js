const mongoose = require('mongoose');

const EquipmentQuantityType = new mongoose.Schema(
  {
    name: {
      type: String,
    },
    priceType: {
      type: String,
      enum: ['buy', 'rental'],
    },
    quantityType: {
      type: String,
      enum: ['unique', 'multiple'],
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    isTemporary: {
      type: Boolean,
      default: false,
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

EquipmentQuantityType.index({ account: 1, deletedAt: 1 });
EquipmentQuantityType.index({ account: 1, deletedAt: 1, isActive: 1 });
EquipmentQuantityType.index({ quantityType: 1 });
EquipmentQuantityType.index({ priceType: 1 });
EquipmentQuantityType.index({ isActive: 1 });

module.exports = mongoose.model('equipment-quantity-type', EquipmentQuantityType);
