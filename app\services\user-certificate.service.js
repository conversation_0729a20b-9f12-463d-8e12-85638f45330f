/* models */
const UserCertificate = require('../models/user-certificate.model');

/**
 * Create Data
 *
 * @param {*} requestData
 * @returns
 */
exports.createData = async requestData => UserCertificate.create(requestData);

/**
 * Find single record by filter
 *
 * @param {*} filter
 * @returns
 */
exports.getSingleRecord = async filter => {
  return UserCertificate.findOne(filter, { createdAt: 0, updatedAt: 0, __v: 0 });
};

/**
 * Update Details
 *
 * @param {*} id
 * @param {*} update
 * @returns
 */
exports.updateDetail = async (id, update) =>
  UserCertificate.findByIdAndUpdate(id, update, { new: true });

/**
 * Remove the file from collection array
 *
 * @param {*} id
 * @param {*} reqData
 * @returns
 */
exports.removeFile = async (id, reqData) => {
  return UserCertificate.updateOne({ _id: id }, { $pull: reqData });
};

/**
 * Update file data
 *
 * @param {*} fieldSearch
 * @param {*} setData
 * @returns
 */
exports.updateFileData = (fieldSearch, setData) => {
  return UserCertificate.updateOne(fieldSearch, { $set: setData });
};

/**
 * Get File Data
 *
 * @param {*} reportId
 * @param {*} filerFiled
 * @returns
 */
exports.getFileData = async (reportId, filerFiled) => {
  return UserCertificate.findById(reportId, filerFiled);
};
