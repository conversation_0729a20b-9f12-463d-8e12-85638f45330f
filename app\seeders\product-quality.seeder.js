const safetyCard = require('../models/safety-card.model');

/**
 * Run the migration
 *
 * @returns
 */
exports.up = async () => {
  try {
    const result = await safetyCard.updateMany(
      { productQuality: { $exists: false } },
      { $set: { productQuality: false } }
    );
    console.log(`Successfully migrated ${result.modifiedCount} records`);
    return true;
  } catch (error) {
    console.error('Error in product-quality seeder', error);
  }
};

/**
 * Rollback the migration
 *
 * @returns
 */
exports.down = async () => {
  try {
    await safetyCard.updateMany(
      { productQuality: { $exists: true } },
      { $unset: { productQuality: 1 } }
    );
  } catch (error) {
    console.error('Error in product-quality down seeder', error);
  }
};
