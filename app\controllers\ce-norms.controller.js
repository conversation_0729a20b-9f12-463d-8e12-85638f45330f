const HTTP_STATUS = require('../utils/status-codes');
const responseUtils = require('../utils/response.utils');
const constantUtils = require('../utils/constants.utils');
const commonFunction = require('../utils/common-function.utils');

// Services
const ceNormsService = require('../services/ce-norms.service');

/**
 * Create Ce-Norms
 *
 * @param {*} req
 * @param {*} res
 */
exports.createCeNorms = async (req, res) => {
  try {
    const requestData = req.body;
    const response = await ceNormsService.createCeNorms(requestData);
    if (response) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    res
      .status(HTTP_STATUS.OK)
      .json(responseUtils.successResponse(constantUtils.CE_NORMS_CREATED, response));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Update Ce Norms
 *
 * @param {*} req
 * @param {*} res
 */
exports.updateCeNorms = async (req, res) => {
  try {
    let { id } = req.params;
    const requestData = req.body;

    const response = await ceNormsService.getCeNormsById(id);

    if (!response) {
      return res
        .status(HTTP_STATUS.NOT_FOUND)
        .json(responseUtils.errorResponse(constantUtils.CE_NORMS_NOT_FOUND));
    }

    const responseUpdate = await ceNormsService.updateCeNorms(id, requestData);

    if (responseUpdate) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }

    res
      .status(HTTP_STATUS.OK)
      .json(responseUtils.successResponse(constantUtils.CE_NORMS_UPDATED, responseUpdate));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get CurrencyUnit
 *
 * @param {*} req
 * @param {*} res
 */
exports.getCeNorms = async (req, res) => {
  try {
    let { account } = req.userData;
    let page = req.query.page ? req.query.page : '';
    let perPage = req.query.perPage ? req.query.perPage : '';
    let sort = req.query.sort && req.query.sort === 'asc' ? 1 : -1;

    let filterData = {
      account: account,
      deletedAt: null,
    };

    const response = await ceNormsService.getCeNorms(filterData, page, perPage, sort);
    res
      .status(HTTP_STATUS.OK)
      .json(responseUtils.successResponse(constantUtils.GET_CE_NORMS, response));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Delete Ce Norms
 *
 * @param {*} req
 * @param {*} res
 */
exports.deleteCeNorms = async (req, res) => {
  try {
    let { id } = req.params;
    const response = await ceNormsService.getCeNormsById(id);
    if (!response) {
      return res
        .status(HTTP_STATUS.NOT_FOUND)
        .json(responseUtils.errorResponse(constantUtils.CE_NORMS_NOT_FOUND));
    }
    const responseDelete = await ceNormsService.updateCeNorms(id, req.deletedAt);
    if (responseDelete) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    res
      .status(HTTP_STATUS.OK)
      .json(responseUtils.successResponse(constantUtils.CE_NORMS_DELETED, responseDelete));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error.message));
  }
};

exports.commonUpdateSyncApiManage = async account => {
  await commonFunction.updateSyncApiManage({
    syncApis: ['equipmentConfig'],
    account,
  });
};
