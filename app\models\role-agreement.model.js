const mongoose = require('mongoose');

const RoleAgreement = new mongoose.Schema(
  {
    role: {
      type: mongoose.Types.ObjectId,
      ref: 'role',
      default: null,
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
      default: null,
    },
    accountLicence: {
      type: mongoose.Types.ObjectId,
      ref: 'accountLicence',
      default: null,
    },
    agreement: {
      create: {
        type: Boolean,
        default: false,
      },
      read: {
        type: Boolean,
        default: false,
      },
      update: {
        type: Boolean,
        default: false,
      },
      delete: {
        type: Boolean,
        default: false,
      },
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('role-agreement', RoleAgreement);
