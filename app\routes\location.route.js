// library
const express = require('express');
const routes = express.Router();

// middleware
const { verifyToken, deletedAt, authAccount } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const locationController = require('../controllers/location.controller');

// Validator
const validator = require('../validators/location.validator');

// create projects
routes.post(
  '',
  verifyToken,
  authAccount,
  validator.locationValidationRule(),
  validate,
  locationController.createLocation
);

// get all locations
routes.get('', verifyToken, authAccount, validate, locationController.getAllData);

// update location
routes.patch(
  '/:id',
  verifyToken,
  authAccount,
  validator.updateLocationValidationRule(),
  validate,
  locationController.updateLocation
);

// delete location
routes.delete(
  '/:id',
  verifyToken,
  authAccount,
  deletedAt,
  validate,
  locationController.deleteLocation
);

// location Progress
routes.get(
  '/progress/:id',
  verifyToken,
  authAccount,
  validate,
  locationController.getLocationProgress
);

routes.get(
  '/progress-pdf/:id',
  verifyToken,
  authAccount,
  validate,
  locationController.getLocationProgressPDF
);

module.exports = routes;
