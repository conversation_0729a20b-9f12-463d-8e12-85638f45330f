const { body, validationResult, constantUtils } = require('../validators/parent.validator');

// catch validation error.
const validate = (req, res, next) => {
  const errors = validationResult(req);
  if (errors.isEmpty()) {
    return next();
  }
  const extractedErrors = [];
  errors.array().map(err =>
    extractedErrors.push({
      [err.param]: err.msg,
    })
  );

  return res.status(422).json({
    status: false,
    data: {
      error: extractedErrors,
    },
  });
};

const loginValidationRule = () => {
  return [
    body('email').isString().isEmail().withMessage(constantUtils.INVALID_EMAIL),
    body('password').isString().isLength({ min: 6 }).withMessage(constantUtils.PASSWORD_MIN_LENGTH),
  ];
};

const emailValidationRule = () => {
  return [body('email').isString().isEmail().withMessage(constantUtils.INVALID_EMAIL)];
};

const resetPassValidateRule = () => {
  return [
    body('newPassword')
      .isString()
      .isLength({ min: 6 })
      .withMessage(constantUtils.ENTER_VALID_NEW_PASSWORD)
      .matches(/^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])[a-zA-Z\d@$.!%*#?&]/)
      .withMessage(constantUtils.INVALID_PASSWORD),
    body('confirmPassword')
      .isString()
      .isLength({ min: 6 })
      .withMessage(constantUtils.ENTER_VALID_CONFIRM_PASSWORD),
  ];
};

module.exports = {
  validate,
  loginValidationRule,
  resetPassValidateRule,
  emailValidationRule,
};
