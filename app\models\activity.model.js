const mongoose = require('mongoose');

const Activity = new mongoose.Schema(
  {
    name: {
      type: String,
    },
    scopeId: {
      type: mongoose.Types.ObjectId,
      ref: 'scope',
    },
    sortOrder: {
      type: Number,
      default: 0,
    },
    project: {
      type: mongoose.Types.ObjectId,
      ref: 'project',
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    isDeletable: {
      type: Boolean,
      default: true,
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
    isDefault: {
      type: Boolean,
      default: false,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('activity', Activity);
