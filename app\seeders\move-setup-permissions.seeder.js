const Licence = require('../models/licence.model');
const Permission = require('../models/permission.model');
const AccountLicence = require('../models/account-licence.model');
const { licenceData } = require('../utils/licence-permission.utils');

/**
 * Update permissions between licenses
 *
 * @returns
 */
exports.up = async () => {
  try {
    // STEP 1: Get current database state
    const currentMapping = await Permission.find().populate('licence', 'name');
    // eslint-disable-next-line no-undef
    const currentPermissionMap = new Map(currentMapping.map(p => [p.name, p.licence.name]));

    // STEP 2: Create desired state map from licenceData
    // eslint-disable-next-line no-undef
    const desiredPermissionMap = new Map();
    licenceData.forEach(licence => {
      licence.permissions.forEach(permission => {
        desiredPermissionMap.set(permission, licence.licence);
      });
    });

    // STEP 3: Detect differences (permissions that need to be moved)
    const permissionMoves = [];
    desiredPermissionMap.forEach((desiredLicence, permission) => {
      const currentLicence = currentPermissionMap.get(permission);
      if (currentLicence && currentLicence !== desiredLicence) {
        permissionMoves.push({
          permission,
          fromLicence: currentLicence,
          toLicence: desiredLicence,
        });
      }
    });

    if (permissionMoves.length === 0) {
      console.log('No permission moves needed - current state matches licence-permission.utils.js');
      return;
    }

    console.log('Detected the following permission moves:');
    permissionMoves.forEach(move => {
      console.log(
        `- Moving '${move.permission}' from '${move.fromLicence}' to '${move.toLicence}'`
      );
    });

    // STEP 4: Process each detected move
    for (const move of permissionMoves) {
      // 4.1: Get the source and target licenses
      // eslint-disable-next-line no-undef
      const [toLicence, fromLicence] = await Promise.all([
        Licence.findOne({ name: move.toLicence }),
        Licence.findOne({ name: move.fromLicence }),
      ]);

      if (!toLicence || !fromLicence) {
        console.error(`Could not find licence: ${!toLicence ? move.toLicence : move.fromLicence}`);
        continue;
      }

      // 4.2: Get the permission document
      const permission = await Permission.findOne({
        name: move.permission,
      });

      if (!permission) {
        console.error(`Could not find permission: ${move.permission}`);
        continue;
      }

      // 4.3: Update permission's licence reference
      await Permission.updateOne(
        { _id: permission._id },
        {
          $set: {
            licence: toLicence._id,
            updatedAt: new Date(),
          },
        }
      );

      // 4.4: Find affected account licenses
      const affectedAccountLicences = await AccountLicence.find({
        permission: permission._id,
        licence: fromLicence._id,
      });

      // 4.5: Update each affected account license
      for (const accountLicence of affectedAccountLicences) {
        // Update the licence reference in AccountLicence
        await AccountLicence.updateOne(
          { _id: accountLicence._id },
          {
            $set: {
              licence: toLicence._id,
              updatedAt: new Date(),
            },
          }
        );
      }

      console.log(`Successfully moved permission: ${move.permission}`);
      console.log(`From ${move.fromLicence} to ${move.toLicence}`);
      console.log(`Updated ${affectedAccountLicences.length} account licenses\n`);
    }

    console.log('Permission moves completed successfully');
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  }
};
