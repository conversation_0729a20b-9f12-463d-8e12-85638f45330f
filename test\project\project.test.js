const request = require('supertest');
const app = require('../../app/server');

// create project
describe('POST /api/projects', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.fhXReQBuyyRFCDWvv8Krubs2aQiykiy21RAnhM3s9V0';
  const projectData = {
    title: 'Team Project 1',
    standByTypes: 'testing',
    isActive: true,
  };

  it('returns 200 and message project created', async () => {
    const response = await request(app)
      .post('/api/projects')
      .set('Authorization', `Bearer ${token}`)
      .send(projectData);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Project has been created successfully',
      status: true,
    });
  });

  it('returns 422 with message Required field cannot be empty.', async () => {
    const response = await request(app)
      .post('/api/projects')
      .set('Authorization', `Bearer ${token}`)
      .send(projectData);
    expect(response.status).toBe(422);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Required field cannot be empty.',
      status: false,
    });
  });

  it('returns 400 with Project already exist', async () => {
    const response = await request(app)
      .post('/api/projects')
      .set('Authorization', `Bearer ${token}`)
      .send(projectData);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Project already exist',
      status: false,
    });
  });
  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).post('/api/projects').send(projectData);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});

// getAll project
describe('GET /api/projects', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.I513fTkTfDgrekZVxyysoPEtU5Rmtr3nM-7HB7Hj7hE';

  it('returns 200 and message get all project List', async () => {
    const response = await request(app)
      .get('/api/projects')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({ data: [{}, {}], status: true });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).get('/api/projects');
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});

//  update project
describe('PATCH /api/projects/:id', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.TzxEZlLuxA-ok9iYNrzr_hCkEkSM35ruuXE4wvD_Gs0';
  const id = '64119ddc6c1d88fde480fd47';
  const updateData = {
    title: 'New Project',
  };
  it('returns 200 and message update project', async () => {
    const response = await request(app)
      .patch(`/api/projects/${id}`)
      .set('Authorization', `Bearer ${token}`)
      .send(updateData);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({ data: {}, status: true });
  });

  it('returns 422 with message Required field cannot be empty.', async () => {
    const response = await request(app)
      .post(`/api/projects/${id}`)
      .set('Authorization', `Bearer ${token}`)
      .send(updateData);
    expect(response.status).toBe(422);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Required field cannot be empty.',
      status: false,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).patch(`/api/projects/${id}`).send(updateData);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});

//  Get projectById
describe('GET /api/projects/:id', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.TzxEZlLuxA-ok9iYNrzr_hCkEkSM35ruuXE4wvD_Gs0';
  const id = '64119ddc6c1d88fde480fd47';

  it('returns 200 and message get project by id', async () => {
    const response = await request(app)
      .get(`/api/projects/${id}`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({ data: {}, status: true });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).get(`/api/projects/${id}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });

  it('returns 400 with message Project does not exist', async () => {
    const response = await request(app)
      .get(`/api/projects/${id}`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Project does not exist',
      status: false,
    });
  });
});

//  Delete projectById
describe('DELETE /api/projects/:id', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.I513fTkTfDgrekZVxyysoPEtU5Rmtr3nM-7HB7Hj7hE';
  const id = '6411b22f1d2b2a0aea3641df';

  it('returns 200 and message delete project by id', async () => {
    const response = await request(app)
      .delete(`/api/projects/${id}`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({ data: {}, status: true });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).delete(`/api/projects/${id}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });

  it('returns 400 with message Project does not exist', async () => {
    const response = await request(app)
      .delete('/api/projects/6411b22f1d2b2a0aea3641ds')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Project does not exist',
      status: false,
    });
  });
});

// get location by project
describe('GET /api/projects/:id/locations', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.TzxEZlLuxA-ok9iYNrzr_hCkEkSM35ruuXE4wvD_Gs0';
  const id = '64119ddc6c1d88fde480fd47';

  it('returns 200 and message get location by project id', async () => {
    const response = await request(app)
      .get(`/api/projects/${id}/locations`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Location has been fetched successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).get(`/api/projects/${id}/locations`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });

  it('returns 400 with message Project does not exist', async () => {
    const response = await request(app)
      .get(`/api/projects/${id}/locations`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Project does not exist',
      status: false,
    });
  });
});

// get member by project
describe('GET /api/projects/:id/members', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.TzxEZlLuxA-ok9iYNrzr_hCkEkSM35ruuXE4wvD_Gs0';
  const id = '64119ddc6c1d88fde480fd47';

  it('returns 200 and message Member list was retireved successfully', async () => {
    const response = await request(app)
      .get(`/api/projects/${id}/members`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Member list was retireved successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).get(`/api/projects/${id}/members`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });

  it('returns 400 with message Project does not exist', async () => {
    const response = await request(app)
      .get(`/api/projects/${id}/members`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Project does not exist',
      status: false,
    });
  });
});

// get function by project
describe('GET /api/projects/:id/functions', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.TzxEZlLuxA-ok9iYNrzr_hCkEkSM35ruuXE4wvD_Gs0';
  const id = '64119ddc6c1d88fde480fd47';

  it('returns 200 and message Function list was retireved successfully', async () => {
    const response = await request(app)
      .get(`/api/projects/${id}/functions`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: [],
      message: 'Function list was retireved successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).get(`/api/projects/${id}/functions`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });

  it('returns 400 with message Project does not exist', async () => {
    const response = await request(app)
      .get(`/api/projects/${id}/functions`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Project does not exist',
      status: false,
    });
  });
});

// get assets by project
describe('GET /api/projects/:id/assets', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.TzxEZlLuxA-ok9iYNrzr_hCkEkSM35ruuXE4wvD_Gs0';
  const id = '64119ddc6c1d88fde480fd47';

  it('returns 200 and message Asset list was retireved successfully', async () => {
    const response = await request(app)
      .get(`/api/projects/${id}/assets`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: [],
      message: 'Asset list was retireved successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).get(`/api/projects/${id}/assets`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });

  it('returns 400 with message Project does not exist', async () => {
    const response = await request(app)
      .get(`/api/projects/${id}/assets`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Project does not exist',
      status: false,
    });
  });
});

// get project strings by project
describe('GET /api/projects/:id/project-strings', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.TzxEZlLuxA-ok9iYNrzr_hCkEkSM35ruuXE4wvD_Gs0';
  const id = '64119ddc6c1d88fde480fd47';

  it('returns 200 and message ProjectString has been retireved successfully', async () => {
    const response = await request(app)
      .get(`/api/projects/${id}/project-strings`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: [],
      message: 'ProjectString has been retireved successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).get(`/api/projects/${id}/project-strings`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });

  it('returns 400 with message Project does not exist', async () => {
    const response = await request(app)
      .get(`/api/projects/${id}/project-strings`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Project does not exist',
      status: false,
    });
  });
});

// get scopes by project
describe('GET /api/projects/:id/scopes', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.TzxEZlLuxA-ok9iYNrzr_hCkEkSM35ruuXE4wvD_Gs0';
  const id = '64119ddc6c1d88fde480fd47';

  it('returns 200 and message Scope has been retireved successfully', async () => {
    const response = await request(app)
      .get(`/api/projects/${id}/scopes`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: [],
      message: 'Scope has been retireved successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).get(`/api/projects/${id}/scopes`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });

  it('returns 400 with message Project does not exist', async () => {
    const response = await request(app)
      .get(`/api/projects/${id}/scopes`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Project does not exist',
      status: false,
    });
  });
});

// get activities by project
describe('GET /api/projects/:id/activities', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.TzxEZlLuxA-ok9iYNrzr_hCkEkSM35ruuXE4wvD_Gs0';
  const id = '64119ddc6c1d88fde480fd47';

  it('returns 200 and message Activity has been retireved successfully', async () => {
    const response = await request(app)
      .get(`/api/projects/${id}/activities`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: [],
      message: 'Activity has been retireved successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).get(`/api/projects/${id}/activities`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });

  it('returns 400 with message Project does not exist', async () => {
    const response = await request(app)
      .get(`/api/projects/${id}/activities`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Project does not exist',
      status: false,
    });
  });
});
