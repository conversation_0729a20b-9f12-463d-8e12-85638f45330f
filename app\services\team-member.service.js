const TeamMember = require('../models/team-members.model');

/**
 * Create TeamMember
 *
 * @param {*} team
 * @returns
 */
exports.createTeamMember = async team => {
  return await TeamMember.create(team);
};

/**
 * Update By Id
 *
 * @param {*} id
 * @param {*} team
 * @returns
 */
exports.updateTeamMember = async (id, team) => {
  return TeamMember.findByIdAndUpdate(id, { $set: team }, { new: true });
};

/**
 * Delete By Id
 *
 * @param {*} id
 * @returns
 */
exports.deleteTeamMember = async (id, deletedAt) => {
  return TeamMember.findByIdAndUpdate(id, { $set: deletedAt });
};

/**
 * Get All TeamMember
 *
 * @param {*} page
 * @param {*} perPage
 * @returns
 */
exports.getAllTeamMember = async account => {
  return TeamMember.find(
    { $and: [{ account: account.account }, { deletedAt: null }] },
    { createdAt: 0, updatedAt: 0, __v: 0 }
  );
};

/**
 * Get By Id
 *
 * @param {*} id
 * @returns
 */
exports.getTeamMemberById = async id => {
  return TeamMember.findOne(
    {
      $and: [{ _id: id }, { deletedAt: null }],
    },
    { createdAt: 0, updatedAt: 0, __v: 0 }
  );
};

/**
 * Get TeamMember By Name
 *
 * @param {*} teamName
 * @returns
 */
exports.getTeamMemberByName = async teamName => {
  return TeamMember.find({
    $and: [{ teamsWfmName: teamName }, { deletedAt: null }],
  });
};

/**
 * Get TeamMember By project id
 *
 * @param {*} projectId
 * @returns
 */
exports.getTeamMemberByProjectId = async filter => {
  return TeamMember.find(filter).populate([
    {
      path: 'project',
      select: 'title projectNumber',
    },
    {
      path: 'account',
      select: 'name',
    },
  ]);
};

/**
 *
 * @param {*} shiftId
 * @returns
 */
exports.getTeamMemberByShiftId = async shiftId => {
  return TeamMember.find(
    {
      $and: [{ shift: shiftId }, { deletedAt: null }],
    },
    { createdAt: 0, updatedAt: 0, __v: 0 }
  )
    .populate({
      path: 'teamMember',
      select: '_id callingName firstName',
      strictPopulate: false,
    })
    .populate({
      path: 'function',
      select: 'functionName',
    })
    .populate({
      path: 'member',
      populate: {
        path: 'user',
        select: 'callingName firstName lastName',
      },
    })
    .select('-createdAt -updatedAt -__v -deletedAt -createdBy -updatedBy -deletedBy');
};

/**
 *
 * @param {*} shiftId
 * @param {*} memberId
 * @returns
 */
exports.getTeamMemberByShiftAndMemberId = async (shiftId, memberId) => {
  return TeamMember.findOne(
    {
      $and: [{ shift: shiftId }, { member: memberId }, { deletedAt: null }],
    },
    { createdAt: 0, updatedAt: 0, __v: 0 }
  )
    .populate({
      path: 'teamMember',
      select: '_id callingName firstName',
      strictPopulate: false,
    })
    .select('-createdAt -updatedAt -__v -deletedAt -createdBy -updatedBy -deletedBy');
};

exports.createTeamMembers = async team => {
  return await TeamMember.create(team);
};

/**
 * Get All TeamMember
 *
 * @param {*} members
 * @returns
 */
exports.personnelData = async members => {
  return TeamMember.find({ deletedAt: null, member: { $in: members } }).populate([
    {
      path: 'member',
      populate: [
        {
          path: 'user',
          select: 'firstName lastName callingName',
        },
      ],
    },
    {
      path: 'function',
      select: 'functionName',
    },
    {
      path: 'shift',
      select: 'startDate',
      populate: {
        path: 'team',
        select: 'teamsWfmName',
      },
    },
  ]);
};

/**
 * Get member with same memberId and functionId
 * @param {Array} records - Array of objects containing memberId and functionId
 * @returns {Promise<Array>} - Array of populated team member
 */
exports.getDetailedTeamMembers = async records => {
  const results = [];

  for (const record of records) {
    const user = await TeamMember.findOne({
      member: record.memberId,
      function: record.functionId,
      deletedAt: null,
    })
      .sort({ updatedAt: -1 })
      .populate([
        {
          path: 'member',
          populate: [
            {
              path: 'user',
              select: 'firstName lastName callingName',
            },
          ],
        },
        {
          path: 'function',
          select: 'functionName',
        },
        {
          path: 'shift',
          select: 'startDate',
          populate: {
            path: 'team',
            select: 'teamsWfmName',
          },
        },
      ])
      .lean();

    // Only push if a user was found
    if (user) {
      results.push(user);
    }
  }

  return results;
};

/**
 * Batch Update TeamMember isWorking and status
 *
 * @param {*} teamMembers
 * @returns
 */
exports.batchUpdateTeamMembers = async teamMembersData => {
  const bulkOperations = teamMembersData.map(device => ({
    updateOne: {
      filter: { _id: device.teamMemberId },
      update: {
        $set: {
          isWorking: device.isWorking,
          status: device.status,
        },
      },
    },
  }));

  return await TeamMember.bulkWrite(bulkOperations);
};

/**
 * Get TeamMember By project id
 *
 * @param {*} projectId
 * @returns
 */
exports.getTeamMemberByProjectIds = async filter => {
  return TeamMember.find(filter).populate([
    {
      path: 'project',
      select: 'title projectNumber',
      strictPopulate: false,
    },
    {
      path: 'account',
      select: 'name',
      strictPopulate: false,
    },
    {
      path: 'function',
      select: 'functionName',
      strictPopulate: false,
    },
    {
      path: 'member',
      populate: {
        path: 'user',
        select: 'callingName firstName lastName',
      },
      strictPopulate: false,
    },
  ]);
};

/**
 * Get TeamMember by filterdata
 *
 * @param {*} filter
 * @returns
 */
exports.getTeamMemberByFilterData = async filter => {
  return TeamMember.findOne(filter);
};
