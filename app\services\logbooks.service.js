const Logbook = require('../models/logbooks.model');

exports.createLogbook = async data => {
  return await Logbook.create(data);
};

exports.getLogbookById = async logbookId => {
  return await Logbook.findById(logbookId);
};

exports.getAllLogbooks = async (filters, sortOrder, sortBy) => {
  return await Logbook.find(filters)
    .populate([
      {
        path: 'project',
        select: { _id: 1, title: 1, projectNumber: 1 },
        strictPopulate: false,
      },
      {
        path: 'createdBy',
        select: { _id: 1, firstName: 1, lastName: 1, callingName: 1, profileImage: 1 },
      },
    ])
    .select({ __v: 0, updatedAt: 0 })
    .sort({ [sortBy]: sortOrder });
};

exports.updateLogbookById = async (logbookId, data) => {
  return await Logbook.findByIdAndUpdate(logbookId, data);
};
