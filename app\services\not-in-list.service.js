const NotInList = require('../models/not-in-list.model');

/**
 * Add Not in List members
 *
 * @param {*} requestData
 * @returns
 */
exports.addMemberAndFunctionToNotInList = async requestData => {
  return await NotInList.create(requestData);
};

/**
 * Get Not in List members
 *
 * @param {*} filter
 * @returns
 */
exports.getNotInListMembers = async filter => {
  return await NotInList.find(filter);
};

/**
 * Get Not in List members
 *
 * @param {*} filter
 * @returns
 */
exports.getNotInListMember = async id => {
  return await NotInList.findById(id);
};

/**
 * Delete All Not in List members
 *
 * @param {*} shiftId
 * @returns
 */
exports.updateNotInList = async (filter, requestData) => {
  return await NotInList.findByIdAndUpdate(filter, { $set: requestData }, { new: true });
};

/**
 * Delete All Not in List members
 *
 * @param {*} shiftId
 * @returns
 */
exports.deleteNotInListByShiftId = async shiftId => {
  return await NotInList.deleteMany({ shift: shiftId });
};

/**
 * Update All Not in List members
 *
 * @param {*} filter
 * @param {*} requestData
 * @returns
 */
exports.updateNotInListByFilter = async (filter, requestData) => {
  return await NotInList.updateMany(filter, { $set: requestData }, { new: true });
};

/**
 * Delete Not in List members
 *
 * @param {*} id
 * @param {*} requestData
 * @returns
 */
exports.deleteNotInListMembers = async (id, requestData) => {
  return await NotInList.findByIdAndUpdate(id, { $set: requestData }, { $new: true });
};
