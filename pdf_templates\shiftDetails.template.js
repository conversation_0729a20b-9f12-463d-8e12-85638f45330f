const commonUtils = require('../app/utils/common.utils');
const commonFunctionsUtils = require('../app/utils/common-function.utils');

/**
 * Generate shift details pdf
 *
 * @param {*} templateData
 * @returns
 */
exports.shiftTemplate = async templateData => {
  const { companyPrimaryColor, requestData } = templateData;
  const projectTitle = await commonUtils.alterStringFromRequestString(
    requestData.project.projectNumber
      ? `${requestData.project.projectNumber} - ${requestData.project.title}`
      : requestData.project.title ?? 'N/A'
  );

  const startDate = await commonFunctionsUtils.formatDateTime(requestData.startDate);

  const team = await commonUtils.alterStringFromRequestString(requestData.team.teamsWfmName);
  const status = await commonUtils.alterStringFromRequestString(requestData.status);
  const getTableRows = await this.generateTableRows(requestData.teamMembers);
  let getActivityTableRows;

  requestData.shiftActivities = await commonFunctionsUtils.calculateDurations(
    requestData.shiftActivities,
    requestData.startDate
  );

  let duration = await commonFunctionsUtils.sumDurations(requestData.shiftActivities);

  if (requestData.shiftActivities.length > 0) {
    getActivityTableRows = await this.generateActivityTableRows(requestData.shiftActivities);
  }

  return `
  <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Shift Details</title>
        <style>
            html {
                box-sizing: border-box;
            }

            *,
            *::before,
            *::after {
                box-sizing: inherit;
            }

            :root {
                --primary-color: ${companyPrimaryColor};
                --primary-font-color: #323232;
                --info-title-color: #4D5464;
                --info-desc-color: #333843;
                --status-color: #009A38;
                --circle-bg-color: #D9D9D9;
                --black-color: #000000;
                --info-label-color: #FFFFFF;
                --table-border: #E0E6F5;
                --table-header-border: #E0E6F51A;
                --white-color: #FFFFFF;
                --pdf-bg-color: #F6F7FF;
            }

            body {
                margin: 0;
                padding: 0;
                display: flex;
                justify-content: center;
                font-family: Arial, sans-serif;
                width: 100%;
                height: 100%;
                color: var(--primary-font-color);
                font-size: 12px;
                font-weight: 500;
            }

            .main {
                width: 100%;
            }

            .pdf-header {
                display: flex;
                width: 100%;
            }

            .pdf-header-title {
                font-size: 20px;
                padding: 0;
                margin: 0;
                margin-bottom: 10px;
                font-weight: 600;
            }

            .space-container {
                height: 20px;
            }

            .custom-table {
                width: 100%;
                border-spacing: 0px;
                border-radius: 4px;
            }

            .custom-table tr {
                page-break-inside: avoid;
            }

            .custom-table th {
                text-align: left;
                padding-left: 10px;
                background-color: var(--primary-color);
                color: var(--white-color);
            }

            .custom-table td {
                padding-left: 10px;
            }

            .custom-table th:first-child {
                border-right: 1px solid var(--table-border);
                border-top-left-radius: 4px;
            }

            .custom-table td:first-child {
                border-right: 1px solid var(--table-border);
                border-bottom: 1px solid var(--table-border);
                border-left: 1px solid var(--table-border);
            }

            .custom-table th:not(:first-child):not(:last-child) {
                border-right: 1px solid var(--table-border);
            }

            .custom-table td:not(:first-child):not(:last-child) {
                border-right: 1px solid var(--table-border);
                border-bottom: 1px solid var(--table-border);
            }

            .custom-table th:last-child {
                border-right: 0px;
                border-top-right-radius: 4px;
            }

            .custom-table td:last-child {
                border-right: 1px solid var(--table-border);
                border-bottom: 1px solid var(--table-border);
            }

            .custom-table tr:last-child td:first-child {
                border-bottom-left-radius: 4px;
            }

            .custom-table tr:last-child td:last-child {
                border-bottom-right-radius: 4px;
            }

            .safety-table-header {
                padding: 10px;
                font-size: 12px;
                font-weight: 600;
            }

            .safety-first-table-desc {
                padding: 15px;
                font-size: 12px;
                font-weight: 500;
            }

            .text-line {
                display: flex;
                align-items: center;
                margin-top: 20px;
            }

            .text-line p {
                margin: 0;
                padding-right: 10px;
                white-space: nowrap;
                font-size: 15px;
                font-weight: 600;
                color: var(--primary-color);
            }

            .text-line hr {
                flex-grow: 1;
                border: none;
                border-top: 1px solid var(--primary-color);
                margin: 0;
                margin-left: 5px;

            }

            .flex-div-container {
                display: flex;
                flex-direction: column;
                gap: 5px;
            }

            .safety-main-table-desc {
                vertical-align: top;
                padding: 15px;
                font-size: 12px;
            }
        </style>
    </head>

    <body>
        <div class="main">
            <div class="pdf-header">
                <p class="pdf-header-title">Shift Details</p>
            </div>
            <table class="custom-table">
                <tr>
                    <th class="safety-table-header">Project</th>
                    <th class="safety-table-header">Start Time</th>
                    <th class="safety-table-header">Shift Duration(hrs)</th>
                    <th class="safety-table-header">Team</th>
                    <th class="safety-table-header">Status</th>
                </tr>
                <tr>
                    <td class="safety-first-table-desc">${projectTitle ?? 'N/A'}</td>
                    <td class="safety-first-table-desc">${startDate ?? 'N/A'}</td>
                    <td class="safety-first-table-desc">${duration.hours} hours${
    duration.minutes > 0 ? ', ' + duration.minutes + ' minutes' : ''
  }</td>
                    <td class="safety-first-table-desc">${team ?? 'N/A'}</td>
                    <td class="safety-first-table-desc">${status ?? 'N/A'}</td>
                </tr>
            </table>
            <div class="text-line">
                <p>Team Members</p>
                <hr>
            </div>
            ${
              getTableRows
                ? `<div class="space-container"></div>
            <table class="custom-table">
                <tr>
                    <th class="safety-table-header" style="text-align: center; width: 50%;">Member</th>
                    <th class="safety-table-header" style="text-align: center;">Function</th>
                </tr>
                ${getTableRows} 
            </table>
            `
                : ''
            }
            
            ${
              getActivityTableRows
                ? `<div class="text-line">
                <p>Shift Details</p>
                <hr>
            </div><div class="space-container"></div>
            <table class="custom-table">
                <thead>
                    <tr>
                        <th class="safety-table-header" style="width: max-conetent; height: 50px;">No</th>
                        <th class="safety-table-header" style="width: max-conetent; height: 50px;">Location</th>
                        <th class="safety-table-header" style="width: max-conetent; height: 50px;">Activity</th>
                        <th class="safety-table-header" style="width: 90px; height: 50px;">End Time</th>
                        <th class="safety-table-header" style="width: max-conetent; height: 50px;">Duration</th>
                        <th class="safety-table-header" style="width:auto;">Description</th>
                    </tr>
                </thead>
                <tbody>
                    ${getActivityTableRows.rows}
                </tbody>
            </table>`
                : ''
            }
            
        </div>
    </body>

    </html>
  `;
};

/**
 * Get Team Members Table Rows
 *
 * @param {*} tableData
 * @returns
 */
exports.generateTableRows = async tableData => {
  let rows = '';
  for (let data of tableData) {
    rows += `
          <tr class="content-text">
              <td class="safety-main-table-desc">${
                data.member.user
                  ? await commonUtils.alterStringFromRequestString(
                      data.member.user?.callingName !== null &&
                        data.member.user?.callingName !== undefined &&
                        data.member.user?.callingName !== ''
                        ? data.member.user?.callingName
                        : data.member.user?.firstName ?? 'N/A'
                    )
                  : 'N/A'
              } ${
      data.member.user
        ? await commonUtils.alterStringFromRequestString(data.member.user.lastName)
        : 'NA'
    }${
      data?.member?.function && !commonUtils.isValidId(data?.member?.function)
        ? `<svg 
            xmlns="http://www.w3.org/2000/svg" 
            width="15" 
            height="15" 
            style="float: right; 
            display: inline-block; 
            vertical-align: top; margin: 0 0 0 auto;"> 
                <image width="15" height="15" href="${process.env.IMAGE_URL}/shift/AnobDQtM9XoO1je_notinlist.png" alt="tick icon" /> 
            </svg>`
        : ''
    }</td>
              <td class="safety-main-table-desc">${
                data.function ? data.function.functionName : 'N/A'
              }</td>
          </tr>
          `;
  }
  return rows;
};

/**
 * Get Activity Table Rows
 *
 * @param {*} tableData
 * @returns
 */
exports.generateActivityTableRows = async tableData => {
  let count = 0;
  let totalHours = 0;
  let totalMinutes = 0;
  let rows = '';

  for (let data of tableData) {
    count++;
    let {
      location = 'N/A',
      activity = 'N/A',
      endTime = 'N/A',
      duration = 'N/A',
      comments = 'N/A',
    } = data;
    rows += `
      <tr class="content-text">
        <td class="safety-main-table-desc">${count}</td>
        <td class="safety-main-table-desc">
          ${
            location !== 'N/A' ? await commonUtils.alterStringFromRequestString(location) : location
          }
        </td>
        <td class="safety-main-table-desc">
          ${
            activity !== 'N/A' ? await commonUtils.alterStringFromRequestString(activity) : activity
          }
        </td>
        <td class="safety-main-table-desc">${await commonFunctionsUtils.formatDateTime(
          endTime
        )}</td>
        <td class="safety-main-table-desc">
     ${duration.hours ? duration.hours : 0} hours${
      duration.minutes > 0 ? `, ${duration.minutes} minutes` : ''
    }  
        </td>
        <td class="safety-main-table-desc">
          ${comments === '' ? 'N/A' : comments}
        </td>
      </tr>`;
  }

  return { rows, duration: { totalHours, totalMinutes } };
};
