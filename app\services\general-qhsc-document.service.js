const generalQhscDocument = require('../models/general-qhsc-document.model');

/**
 * Get All Documents
 *
 * @param {*} document
 * @returns
 */
exports.getAllDocuments = async (filter, page, perPage, sort) => {
  return await generalQhscDocument
    .find(filter)
    .skip(page * perPage)
    .limit(perPage)
    .sort(sort);
};

/**
 * Create Document
 *
 * @param {*} document
 * @returns
 */
exports.createDocument = async document => {
  return await generalQhscDocument.create(document);
};

/**
 * Get Document
 *
 * @param {*} document
 * @returns
 */
exports.getDocument = async filter => {
  return await generalQhscDocument.findOne(filter);
};

/**
 * Update Document
 *
 * @param {*} document
 * @returns
 */
exports.updateDocument = async (id, requestData) => {
  return await generalQhscDocument.findByIdAndUpdate(id, { $set: requestData });
};

/** Delete Document
 *
 * @param {*} document
 * @returns
 */
exports.deleteDocument = async (id, deletedAt) => {
  return await generalQhscDocument.findByIdAndUpdate(id, { $set: deletedAt });
};
