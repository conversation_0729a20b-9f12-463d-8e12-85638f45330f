const Job = require('../models/job.model');

/**
 * Create job
 *
 * @param {*} job
 * @returns
 */
exports.createJob = async jobData => {
  return await Job.create(jobData);
};

/**
 * Update job
 *
 * @param {*} job
 * @returns
 */
exports.updateJob = async (id, jobData) => {
  return Job.findByIdAndUpdate(id, { $set: jobData });
};

/**
 * find job data
 *
 * @param {*} job
 * @returns
 */
exports.getJobById = async id => {
  return Job.findById(id, {
    _id: 0,
    jobId: 0,
    status: 0,
    type: 0,
    createdAt: 0,
    updatedAt: 0,
    __v: 0,
  });
};
