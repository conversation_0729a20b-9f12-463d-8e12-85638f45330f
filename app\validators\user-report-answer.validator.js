const { body, validateParamIds, constantUtils, commonUtils } = require('./parent.validator');

const userReportAnswerValidationRule = () => {
  return [
    body('reportQuestion').notEmpty().withMessage(constantUtils.REPORT_QUESTION_REQUIRED),
    body('report').notEmpty().withMessage(constantUtils.REPORT_REQUIRED),
    body('userReport').notEmpty().withMessage(constantUtils.USER_REPORT_REQUIRED),
    body('reportQuestionAnswers')
      .isArray()
      .notEmpty()
      .withMessage(constantUtils.REPORT_QUESTION_ANSWERS_REQUIRED),
  ];
};

const updateUserReportAnswerValidationRule = () => {
  return [
    body('answerTitleId').custom(value => {
      if (value) {
        return commonUtils.validateId(value, constantUtils.INVALID_ANSWER_TITLE_ID);
      }
      return true;
    }),
  ];
};

const updateIsPrintableValidationRule = () => {
  return [
    body('userProjectReport').notEmpty().withMessage(constantUtils.USER_PROJECT_REPORT_REQUIRED),
    body('reportQuestionAnswer')
      .notEmpty()
      .withMessage(constantUtils.REPORT_QUESTION_ANSWERS_REQUIRED),
    body('answerTitleId').notEmpty().withMessage(constantUtils.ANSWER_TITLE_ID_REQUIRED),
    body('isPrintable').notEmpty().withMessage(constantUtils.ISPRINTABLE_REQUIRED),
  ];
};

module.exports = {
  userReportAnswerValidationRule,
  updateUserReportAnswerValidationRule,
  updateIsPrintableValidationRule,
  validateParamIds,
};
