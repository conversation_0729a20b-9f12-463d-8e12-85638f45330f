const express = require('express');
const routes = express.Router();

const { verifyToken, authAccount, deletedAt } = require('../middlewares/auth.middleware');

const validator = require('../validators/dpr.validator');

const { validate } = require('../middlewares/validate.middleware');

// controller
const dprController = require('../controllers/dpr.controller');

// post
routes.post(
  '',
  verifyToken,
  authAccount,
  validator.dprCreateValidationRule(),
  validate,
  dprController.createDpr
);

routes.get('/:dprId/:dprVersion/export-pdf', verifyToken, authAccount, dprController.exportDprPdf);

// patch
routes.patch(
  '/:dprId',
  verifyToken,
  authAccount,
  validator.dprUpdateValidationRule(),
  validate,
  dprController.updateDpr
);

routes.patch('/:dprId/reload', verifyToken, authAccount, dprController.updateReload);

// delete
routes.delete('/:dprId', verifyToken, authAccount, deletedAt, dprController.deleteDpr);
routes.delete('/hard/:dprId', verifyToken, authAccount, dprController.hardDeleteDpr);

//get
routes.get('/dpr-members/:dprId', verifyToken, authAccount, validate, dprController.getDprMembers);
routes.get('/dpr-details/:dprId', verifyToken, authAccount, validate, dprController.getDprDetails);
routes.get('', verifyToken, authAccount, validate, dprController.getAllDprs);
routes.get('/excel-export', verifyToken, authAccount, validate, dprController.getDprExcel);

module.exports = routes;
