/* models */
const User = require('../models/user.model');
const Role = require('../models/role.model');

/**
 * Prepare and insert the roles data in collection
 *
 * @returns
 */
exports.up = async () => {
  //check the roles exist
  const roleCount = await Role.find().countDocuments();

  if (roleCount > 0) {
    return true;
  }

  // get the superadmin id
  const superAdmin = await User.find({ role: { $in: ['superadmin'] } });
  let defaultUserId = superAdmin[0]._id;

  const adminUser = await User.find({ email: '<EMAIL>' });
  let account = adminUser[0].account;

  // prepare roles data
  let roles = [
    {
      title: 'Project Manager',
      allProject: true,
      account,
      createdBy: defaultUserId,
      updatedBy: defaultUserId,
    },
    {
      title: 'Team Lead',
      allProject: true,
      account,
      createdBy: defaultUserId,
      updatedBy: defaultUserId,
    },
    {
      title: 'Techician',
      allProject: true,
      account,
      createdBy: defaultUserId,
      updatedBy: defaultUserId,
    },
  ];

  // insert the pre-define roles
  await Role.insertMany(roles);
};
