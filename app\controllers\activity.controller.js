require('dotenv').config();

// services
const activityService = require('../services/activity.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');

/**
 * Create Activity
 *
 * @param {*} req
 * @param {*} res
 */
exports.createActivity = async (req, res) => {
  try {
    req.body.account = req.userData.account;

    const { name, project } = req.body;
    const filterData = {
      name,
      project,
      account: req.userData.account.toString(),
      isDefault: false,
    };
    const exist = await activityService.getActivityByProjectIdAndName(filterData);

    if (exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.ACTIVITY_EXIST));
    }
    const activityData = await activityService.createActivity(req.body);

    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.CREATE_ACTIVITY, activityData));
  } catch (err) {
    const errCode = err.code ?? 500;
    res.status(errCode).json(responseUtils.errorResponse(err.message));
  }
};

/**
 * Update Activity
 *
 * @param {*} req
 * @param {*} res
 */
exports.updateActivity = async (req, res) => {
  try {
    const id = req.params.id;
    const exist = await activityService.getActivityById(id);

    if (exist.length == 0) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_ACTIVITY));
    }

    req.body.account = req.userData.account;

    const activityData = await activityService.updateActivity(id, req.body);

    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.UPDATE_ACTIVITY, activityData));
  } catch (err) {
    const errCode = err.code ?? 500;
    res.status(errCode).json(responseUtils.errorResponse(err.message));
  }
};

/**
 * Delete Activity
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.deleteActivity = async (req, res) => {
  try {
    const id = req.params.id;
    const exist = await activityService.getActivityById(id);

    if (exist.length == 0) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_ACTIVITY));
    }
    const activityData = await activityService.deleteActivity(id, req.deletedAt);

    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.DELETE_ACTIVITY, activityData));
  } catch (err) {
    res.status(500).json(responseUtils.errorResponse(err.message));
  }
};
