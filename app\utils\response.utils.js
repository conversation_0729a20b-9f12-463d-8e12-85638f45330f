/**
 * Success Response
 *
 * @param {*} msg
 * @param {*} data
 * @returns
 */
exports.successResponse = (msg, data) => {
  return {
    status: true,
    message: msg,
    data: data,
  };
};

/**
 * Error Response
 *
 * @param {*} msg
 * @param {*} data
 * @returns
 */
exports.errorResponse = (msg, data) => {
  return {
    status: false,
    message: msg || 'Something went wrong.',
    data: data,
  };
};

exports.throwCustomErrorWithStatus = (msg, status) => {
  const error = new Error(msg);
  error.statusCode = status;
  throw error;
};
