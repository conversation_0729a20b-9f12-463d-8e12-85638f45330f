// Services
const currencyUnitService = require('../services/currency-unit.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const commonFunction = require('../utils/common-function.utils');

/**
 * Create CurrencyUnit
 *
 * @param {*} req
 * @param {*} res
 */
exports.createCurrencyUnit = async (req, res) => {
  try {
    const requestData = req.body;
    const response = await currencyUnitService.createCurrencyUnit(requestData);
    if (response) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.CREATE_CURRENCY_UNIT, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get CurrencyUnit
 *
 * @param {*} req
 * @param {*} res
 */
exports.getCurrencyUnit = async (req, res) => {
  try {
    let { account } = req.userData;
    let page = req.query.page ? req.query.page : '';
    let perPage = req.query.perPage ? req.query.perPage : '';
    let sort = req.query.sort && req.query.sort === 'asc' ? 1 : -1;

    let filterData = {
      account: account,
      deletedAt: null,
    };

    const response = await currencyUnitService.getCurrencyUnit(filterData, page, perPage, sort);
    res.status(200).json(responseUtils.successResponse(constantUtils.GET_CURRENCY_UNIT, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Update CurrencyUnit
 *
 * @param {*} req
 * @param {*} res
 */
exports.updateCurrencyUnit = async (req, res) => {
  try {
    let { id } = req.params;
    const requestData = req.body;
    const response = await currencyUnitService.getCurrencyUnitById(id);
    if (!response) {
      return res
        .status(404)
        .json(responseUtils.errorResponse(constantUtils.CURRENCY_UNIT_NOT_FOUND));
    }

    const responseUpdate = await currencyUnitService.updateCurrencyUnit(id, requestData);
    if (responseUpdate) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }

    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.UPDATE_CURRENCY_UNIT, responseUpdate));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Delete CurrencyUnit
 *
 * @param {*} req
 * @param {*} res
 */
exports.deleteCurrencyUnit = async (req, res) => {
  try {
    let { id } = req.params;
    const response = await currencyUnitService.getCurrencyUnitById(id);
    if (!response) {
      return res
        .status(404)
        .json(responseUtils.errorResponse(constantUtils.CURRENCY_UNIT_NOT_FOUND));
    }
    const responseDelete = await currencyUnitService.deleteCurrencyUnit(id, req.deletedAt);
    if (responseDelete) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.DELETE_CURRENCY_UNIT, responseDelete));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Check if default currency unit exist
 *
 * @param {*} account
 * @returns
 */
exports.checkDefaultCurrency = async (requestData, id = null) => {
  let { account, isDefault } = requestData;
  let currencyUnit = id !== null ? await currencyUnitService.getCurrencyUnitById(id) : null;

  if (currencyUnit !== null && currencyUnit.isDefault.toString() === isDefault.toString()) {
    return true;
  }

  const currencyData = await currencyUnitService.getCurrencyUnit(
    { isDefault: true, account, deletedAt: null },
    '',
    '',
    -1
  );

  if (currencyData.length > 0) {
    throw new Error(constantUtils.DEFAULT_CURRENCY_UNIT_EXIST);
  }
  return true;
};

exports.commonUpdateSyncApiManage = async account => {
  await commonFunction.updateSyncApiManage({
    syncApis: ['equipmentConfig'],
    account,
  });
};
