// library
const express = require('express');
const routes = express.Router();

// middleware
const {
  verifyToken,
  authAccount,
  deletedAt,
  defaultCreatedDetails,
  updatedBy,
} = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');
const {
  createEquipmentTypeValidationRule,
  updateEquipmentTypeValidationRule,
} = require('../validators/equipment-type.validator');

// controller
const equipmentTypeController = require('../controllers/equipment-type.controller');

//Create
routes.post(
  '',
  verifyToken,
  authAccount,
  createEquipmentTypeValidationRule(),
  validate,
  defaultCreatedDetails,
  equipmentTypeController.createEquipmentType
);

// Get EquipmentType
routes.get('', verifyToken, authAccount, validate, equipmentTypeController.getEquipmentType);
routes.get(
  '/images',
  verifyToken,
  authAccount,
  validate,
  equipmentTypeController.getEquipmentTypes
);

// Get EquipmentType by Id
routes.get(
  '/:id',
  verifyToken,
  authAccount,
  validate,
  equipmentTypeController.getEquipmentTypeById
);

// Update EquipmentType
routes.patch(
  '/:id',
  verifyToken,
  authAccount,
  updateEquipmentTypeValidationRule(),
  validate,
  updatedBy,
  equipmentTypeController.updateEquipmentType
);

// Delete EquipmentType
routes.delete(
  '/:id',
  verifyToken,
  authAccount,
  deletedAt,
  validate,
  equipmentTypeController.deleteEquipmentType
);

module.exports = routes;
