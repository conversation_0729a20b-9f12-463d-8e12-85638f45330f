const ContractualDetail = require('../models/contractual-detail.model');
const constantUtils = require('../utils/constants.utils');

/**
 * Update existing contractual details documents to add:
 * - isPrimary: true and isSecondary: false to each item in the identityProof array
 * - secondaryPassport: '' (empty string) to the root of each document
 *
 * @returns
 */
exports.up = async () => {
  try {
    console.log(constantUtils.START_PROCESSING);

    // Find all contractual details documents
    const contractualDetails = await ContractualDetail.find({});
    console.log(`Found ${contractualDetails.length} contractual details documents`);

    let updatedCount = 0;

    // Update each document
    for (const doc of contractualDetails) {
      // Convert Mongoose document to plain JavaScript object
      const docObj = doc.toObject();

      const updateData = {
        secondaryPassport: '',
      };

      // Always update identityProof items with isPrimary and isSecondary
      if (docObj.identityProof && docObj.identityProof.length > 0) {
        // Map through identityProof array to add isPrimary and isSecondary
        updateData.identityProof = docObj.identityProof.map(item => {
          // Always set isPrimary to true and isSecondary to false
          return {
            ...item,
            isPrimary: true,
            isSecondary: false,
          };
        });
      }

      await ContractualDetail.findByIdAndUpdate(doc._id, { $set: updateData });
      updatedCount++;
    }

    console.log(`Successfully updated ${updatedCount} contractual details documents`);
    console.log(constantUtils.END_PROCESS);
    return true;
  } catch (error) {
    console.error('Error updating contractual details:', error);
    return false;
  }
};
