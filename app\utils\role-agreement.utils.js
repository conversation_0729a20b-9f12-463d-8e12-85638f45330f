const accountLicenceService = require('../services/account-licence.service');
const RoleAgreement = require('../models/role-agreement.model');
const permissionService = require('../services/permission.service');

/**
 * Modify Role Agreement
 *
 * @param {*} account
 * @param {*} user
 * @param {*} role
 */
exports.modifyRoleAgreement = async (account, user, role) => {
  try {
    const filterData = {
      account: account,
      isApproved: true,
      isRejected: false,
    };
    const accountLicenceData = await accountLicenceService.getLicenceByAccountId(filterData);
    for (let ele of accountLicenceData) {
      const isAdminRole = role.title === global.constant.ADMIN_ROLE;

      await RoleAgreement.create({
        role: role._id,
        accountLicence: ele._id,
        account: account,
        agreement: {
          create: isAdminRole,
          read: isAdminRole,
          update: isAdminRole,
          delete: isAdminRole,
        },
        createdBy: user,
        updatedBy: user,
      });
    }
  } catch (error) {
    throw new Error(error.message);
  }
};

/**
 * Crewing Role Agreement
 *
 * @param {*} account
 * @param {*} user
 * @param {*} role
 * @param {*} alPermission
 */
exports.crewingRoleAgreement = async (account, user, role, alPermission) => {
  try {
    const filterData = {
      account,
      isApproved: true,
      isRejected: false,
    };
    const accountLicenceData = await accountLicenceService.getLicenceByAccountId(filterData);

    for (const ele of accountLicenceData) {
      const isPermitted = ele.permission.toString() === alPermission.permission._id.toString();
      await RoleAgreement.create({
        role: role._id,
        accountLicence: ele._id,
        account,
        agreement: {
          create: isPermitted,
          read: isPermitted,
          update: isPermitted,
          delete: isPermitted,
        },
        createdBy: user,
        updatedBy: user,
      });
    }
  } catch (error) {
    throw new Error(error.message);
  }
};

/**
 * Add Admin Role Agreement
 *
 * @param {*} account
 * @param {*} user
 * @param {*} role
 */
exports.addAdminRoleAgreement = async (account, user, role) => {
  try {
    const filterData = {
      account: account,
      isApproved: true,
      isRequested: true,
    };
    const accountLicenceData = await accountLicenceService.getLicenceByAccountId(filterData);
    for (let ele of accountLicenceData) {
      const isAdminRole = role.title === global.constant.ADMIN_ROLE;
      let agreement = {
        create: isAdminRole,
        read: isAdminRole,
        update: isAdminRole,
        delete: isAdminRole,
      };

      // check permission
      const getPermission = await permissionService.getPermissionByFilter({ _id: ele.permission });

      // assign default agreement for equipment to project manager
      if (
        global.constant.PROJECT_MANAGER_ROLE === role.title &&
        getPermission.name === global.constant.EQUIPMENT_PERMISSON
      ) {
        agreement.create = true;
        agreement.read = true;
        agreement.update = true;
        agreement.delete = true;
      }

      // assign default agreement for equipment & warehouse to warehouse manager
      if (
        global.constant.WAREHOUSE_MANAGER_ROLE === role.title &&
        [global.constant.EQUIPMENT_PERMISSON, global.constant.WAREHOUSE_PERMISSON].includes(
          getPermission.name
        )
      ) {
        agreement.create = true;
        agreement.read = true;
        agreement.update = true;
        agreement.delete = true;
      }

      await RoleAgreement.create({
        role: role._id,
        accountLicence: ele._id,
        account: account,
        agreement,
        createdBy: user,
        updatedBy: user,
      });
    }
  } catch (error) {
    throw new Error(error.message);
  }
};
