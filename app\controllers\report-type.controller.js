require('dotenv').config();

// services
const reportService = require('../services/report-type.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const { toObjectId } = require('../utils/common.utils');

/**
 * Get all reports
 *
 * @param {*} req
 * @param {*} res
 */
exports.filterReports = async (req, res) => {
  try {
    const { project } = req.query;
    let response;
    let page = req.query.page ? req.query.page : '';
    let perPage = req.query.perPage ? req.query.perPage : '';

    if (project.toLowerCase() === 'all') {
      response = await reportService.getAllReports(req.userData.account, page, perPage);
    } else {
      const filter = {
        project: toObjectId(project),
        account: req.userData.account,
      };
      response = await reportService.getReportByProjectId(filter, page, perPage);
    }
    res.status(200).json(responseUtils.successResponse(constantUtils.ALL_REPORT_LIST, response));
  } catch (error) {
    const errCode = error.code ?? 500;
    res.status(errCode).json(responseUtils.errorResponse(error.message));
  }
};
