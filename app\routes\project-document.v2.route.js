// library
const express = require('express');
const routes = express.Router();

// middleware
const { verifyToken, authAccount } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const projectDocumentController = require('../controllers/project-document.controller');

// Get Project Document List
routes.get('', verifyToken, authAccount, validate, projectDocumentController.getProjectDocumentsV2);

module.exports = routes;
