/* models */
const ToolboxTalk = require('../models/toolbox-talk.model');
// Utils
const aggregateComponentUtils = require('../utils/aggregate-component.utils');

/**
 * Create Data
 *
 * @param {*} requestData
 * @returns
 */
exports.createToolboxTalk = async requestData => ToolboxTalk.create(requestData);

exports.getToolboxTalks = async (filter, type, page, perPage) => {
  let aggregateFunction = [{ $match: filter }, { $sort: { createdAt: -1 } }];
  if (type === 'list') {
    aggregateFunction.push(
      {
        $skip: parseInt(page) * parseInt(perPage),
      },
      {
        $limit: parseInt(perPage),
      }
    );
  }
  aggregateFunction.push(
    {
      $lookup: {
        from: 'users',
        localField: 'hostedBy',
        foreignField: '_id',
        pipeline: [{ $project: { callingName: 1, firstName: 1, lastName: 1 } }],
        as: 'createdBy',
      },
    },
    {
      $unwind: '$createdBy',
    },
    {
      $lookup: {
        from: 'projects',
        localField: 'project',
        foreignField: '_id',
        pipeline: [{ $project: { _id: 1, title: 1, projectNumber: 1 } }],
        as: 'project',
      },
    },
    {
      $unwind: '$project',
    },
    {
      $lookup: {
        from: 'locations',
        localField: 'location',
        foreignField: '_id',
        pipeline: [
          { $project: { _id: 1, title: 1, deletedAt: 1, deletedBy: 1 } },
          {
            $lookup: {
              from: 'users',
              localField: 'deletedBy',
              foreignField: '_id',
              as: 'deletedBy',
              pipeline: [{ $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1 } }],
            },
          },
          {
            $unwind: { path: '$deletedBy', preserveNullAndEmptyArrays: true },
          },
        ],
        as: 'location',
      },
    },
    {
      $unwind: '$location',
    },
    {
      $lookup: {
        from: 'teams',
        localField: 'team',
        foreignField: '_id',
        pipeline: [{ $project: { _id: 1, teamsWfmName: 1 } }],
        as: 'team',
      },
    },
    {
      $unwind: '$team',
    }
  );

  if (type === 'list') {
    aggregateFunction.push(
      {
        $lookup: {
          from: 'members',
          localField: 'project._id',
          foreignField: 'project',
          pipeline: [
            {
              $group: {
                _id: null,
                total: { $sum: 1 },
              },
            },
          ],
          as: 'totalMembers',
        },
      },
      {
        $unwind: {
          path: '$totalMembers',
          preserveNullAndEmptyArrays: true,
        },
      }
    );
  } else {
    aggregateFunction.push(
      {
        $lookup: {
          from: 'users',
          localField: 'memberSignature.user',
          foreignField: '_id',
          pipeline: [{ $project: { callingName: 1, firstName: 1, lastName: 1 } }],
          as: 'userDetails',
        },
      },
      {
        $addFields: {
          memberSignature: {
            $map: {
              input: '$memberSignature',
              as: 'ms',
              in: {
                $mergeObjects: [
                  '$$ms',
                  {
                    user: {
                      $arrayElemAt: [
                        {
                          $filter: {
                            input: '$userDetails',
                            as: 'ud',
                            cond: { $eq: ['$$ud._id', '$$ms.user'] },
                          },
                        },
                        0,
                      ],
                    },
                  },
                ],
              },
            },
          },
        },
      }
    );
  }

  let projectElements = {
    _id: 1,
    project: 1,
    location: 1,
    team: 1,
    note: 1,
    createdBy: 1,
    createdAt: 1,
  };

  if (type === 'list') {
    projectElements.totalMembers = { $ifNull: ['$totalMembers.total', 0] };
    projectElements.memberSigned = { $size: '$memberSignature' };
  } else {
    projectElements.photos = 1;
    projectElements.createdBySignature = 1;
    projectElements.memberSignature = 1;
  }

  aggregateFunction.push({
    $project: projectElements,
  });
  return await ToolboxTalk.aggregate(aggregateFunction);
};

/**
 * Delete Toolbox Talk
 *
 * @param {*} filter
 * @returns
 */
exports.deleteToolboxTalk = async (id, deletedAt) => {
  return ToolboxTalk.findByIdAndUpdate(id, { $set: deletedAt }, { new: true });
};

/**
 * Update Data
 *
 * @param {*} id
 * @param {*} requestData
 * @returns
 */
exports.updateToolboxTalk = async (id, requestData) => {
  return ToolboxTalk.findByIdAndUpdate(id, { $set: requestData }, { new: true });
};

/**
 * Get By Id
 *
 * @param {*} filter
 * @returns
 */
exports.getToolboxTalkById = async filter => {
  return ToolboxTalk.findById(filter);
};

/**
 * Get Toolbox Talk PDF Details
 *
 * @param {*} filter
 * @returns
 */
exports.getToolboxTalkPDFDetails = async filter => {
  let pipeline = [
    {
      $match: filter,
    },
    aggregateComponentUtils.aggregateLookup('project'),
    aggregateComponentUtils.aggregateUnwind('project'),
    aggregateComponentUtils.aggregateLookup('team'),
    aggregateComponentUtils.aggregateUnwind('team'),
    aggregateComponentUtils.aggregateLookup('location'),
    aggregateComponentUtils.aggregateUnwind('location'),
    aggregateComponentUtils.aggregateLookup('hostedBy'),
    aggregateComponentUtils.aggregateUnwind('hostedBy'),
    aggregateComponentUtils.aggregateLookup('memberSignatureUser'),
    {
      $addFields: {
        memberSignature: {
          $map: {
            input: '$memberSignature',
            as: 'ms',
            in: {
              $mergeObjects: [
                '$$ms',
                {
                  user: {
                    $arrayElemAt: [
                      {
                        $filter: {
                          input: '$userDetails',
                          as: 'ud',
                          cond: { $eq: ['$$ud._id', '$$ms.user'] },
                        },
                      },
                      0,
                    ],
                  },
                },
              ],
            },
          },
        },
      },
    },
  ];

  return await ToolboxTalk.aggregate(pipeline);
};

/**
 * Get Toolbox Talk Summary
 *
 * @param {*} project
 * @param {*} account
 * @returns
 */
exports.getToolboxTalkSummary = async filter => {
  return await ToolboxTalk.find(filter);
};
