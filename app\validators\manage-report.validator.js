const { body, validateParamIds, constantUtils } = require('./parent.validator');

const createReportValidationRule = () => {
  return [
    body('title')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.EQUIPMENT_CERTIFICATE_TITLE_REQUIRED),
    body('project').notEmpty().withMessage(constantUtils.PROJECT_REQUIRED),
    body('type').notEmpty().withMessage(constantUtils.REPORT_TYPE_REQUIRED),
    body('isProgressable')
      .isBoolean()
      .notEmpty()
      .withMessage(constantUtils.ISPROGRESSABLE_REQUIRED),
    body('isPublish').isBoolean().notEmpty().withMessage(constantUtils.ISPUBLISH_REQUIRED),
    body('scope').notEmpty().withMessage(constantUtils.SCOPE_REQUIRED),
  ];
};

module.exports = {
  createReportValidationRule,
  validateParamIds,
};
