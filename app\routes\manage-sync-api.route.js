const express = require('express');
const routes = express.Router();

const { validate } = require('../middlewares/validate.middleware');
const manageSyncApiController = require('../controllers/manage-sync-api.controller');

// middleware
const { verifyToken, authAccount } = require('../middlewares/auth.middleware');

routes.post('', verifyToken, authAccount, validate, manageSyncApiController.manageSyncApi);

module.exports = routes;
