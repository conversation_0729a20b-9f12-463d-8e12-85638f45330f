const routes = require('express').Router();

const validator = require('../validators/shift.validator');

// middleware
const { verifyToken, authAccount, deletedAt } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const shiftController = require('../controllers/shift.controller');

routes.get('', verifyToken, authAccount, validate, shiftController.getAllShiftsUserWise);

routes.post(
  '',
  verifyToken,
  authAccount,
  validator.shiftValidationRule(),
  validate,
  shiftController.createShiftV2
);

// update shift
routes.patch(
  '/:id',
  verifyToken,
  deletedAt,
  validator.updateShiftValidationRule(),
  validate,
  shiftController.updateShiftV2
);

module.exports = routes;
