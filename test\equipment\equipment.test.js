require('dotenv').config();
require('../../app/utils/global-constants.utils');
const request = require('supertest');
const app = require('../../app/server');
const { DEFAULT_PASSWORD } = process.env;

describe('Equipment API Tests', () => {
  let authToken;

  // Login before running tests to get auth token
  beforeAll(async () => {
    const loginResponse = await request(app).post('/api/auths/login').send({
      email: '<EMAIL>',
      password: DEFAULT_PASSWORD,
      userAgent:
        '5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      location: 'Rotterdam',
    });

    if (loginResponse.status === 200 && loginResponse.body.data) {
      authToken = loginResponse.body.data.token;
    }
  });

  describe('GET /api/equipments/summary', () => {
    it('should return 400 without authentication token', async () => {
      const response = await request(app).get('/api/equipments/summary');

      expect(response.status).toBe(400);
    });

    it('should return equipment summary data with valid authentication', async () => {
      if (!authToken) {
        console.log('Skipping test - no auth token available');
        return;
      }

      const response = await request(app)
        .get('/api/equipments/summary')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('status', true);
      expect(response.body).toHaveProperty(
        'message',
        'Equipment summary view retrieved successfully'
      );
      expect(response.body).toHaveProperty('data');
      expect(Array.isArray(response.body.data)).toBe(true);

      // If there's data, check the structure
      if (response.body.data.length > 0) {
        const firstItem = response.body.data[0];
        expect(firstItem).toHaveProperty('equipmentCategory');
        expect(firstItem).toHaveProperty('equipmentType');
        expect(firstItem).toHaveProperty('totalStock');
        expect(firstItem).toHaveProperty('availableStock');
        expect(firstItem).toHaveProperty('totalExpiredCertificates');

        // Check data types
        expect(typeof firstItem.equipmentCategory).toBe('string');
        expect(typeof firstItem.equipmentType).toBe('string');
        expect(typeof firstItem.totalStock).toBe('number');
        expect(typeof firstItem.availableStock).toBe('number');
        expect(typeof firstItem.totalExpiredCertificates).toBe('number');
      }
    });

    it('should return empty array if no equipment data exists', async () => {
      if (!authToken) {
        console.log('Skipping test - no auth token available');
        return;
      }

      const response = await request(app)
        .get('/api/equipments/summary')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('status', true);
      expect(response.body).toHaveProperty('data');
      expect(Array.isArray(response.body.data)).toBe(true);
    });
  });
});
