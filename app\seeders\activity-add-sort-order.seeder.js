const Activity = require('../models/activity.model');

/**
 * Migrate weight field to sortOrder
 *
 * @returns
 */
exports.up = async () => {
  try {
    const result = await Activity.updateMany({ weight: { $exists: true } }, [
      { $set: { sortOrder: '$weight' } },
    ]);

    console.log(`Successfully migrated ${result.modifiedCount} records`);
    return true;
  } catch (error) {
    console.error('Error in activity-add-sort-order up seeder', error);
  }
};

/**
 * Rollback the migration
 *
 * @returns
 */
exports.down = async () => {
  try {
    const result = await Activity.updateMany({ sortOrder: { $exists: true } }, [
      { $set: { weight: '$sortOrder', sortOrder: '$$REMOVE' } },
    ]);

    console.log(`Successfully migrated ${result.modifiedCount} records`);
  } catch (error) {
    console.error('Error in activity-add-sort-order down seeder', error);
  }
};
