const ProjectString = require('../models/project-string.model');

// utils
const { toObjectId } = require('../utils/common.utils');

/**
 * Create ProjectString
 *
 * @param {*} ProjectString
 * @returns
 */
exports.createProjectString = async requestData => {
  return await ProjectString.create(requestData);
};

/**
 * Update By Id
 *
 * @param {*} id
 * @param {*} update
 * @returns
 */
exports.updateProjectString = async (id, update) => {
  return ProjectString.findByIdAndUpdate(id, update, { new: true }).populate([
    {
      path: 'fromLocation',
      model: 'location',
      select: 'title',
    },
    {
      path: 'toLocation',
      model: 'location',
      select: 'title',
    },
    {
      path: 'project',
      select: 'title projectNumber',
    },
    {
      path: 'account',
      select: 'name',
    },
  ]);
};

/**
 * Delete By Id
 *
 * @param {*} id
 * @param {*} deletedAt
 * @returns
 */
exports.deleteProjectString = async (id, deletedAt) => {
  return ProjectString.findByIdAndUpdate(id, { $set: deletedAt });
};

/**
 * Get ProjectString By Id
 *
 * @param {*} id
 * @returns
 */
exports.getProjectStringById = async id => {
  return ProjectString.find({ _id: toObjectId(id) });
};

/**
 * Get ProjectString By projectId
 *
 * @param {*} projectId
 * @returns
 */
exports.getProjectStringByProjectId = async (
  filter = {},
  page = null,
  perPage = null,
  sort = { createdAt: -1 }
) => {
  return await ProjectString.find(filter)
    .collation({ locale: 'en', strength: 2 })
    .sort(sort)
    .limit(perPage)
    .skip(page * perPage)
    .populate([
      {
        path: 'fromLocation',
        model: 'location',
        select: { _id: 1, title: 1, deletedAt: 1 },
      },
      {
        path: 'toLocation',
        model: 'location',
        select: { _id: 1, title: 1, deletedAt: 1 },
      },
      {
        path: 'project',
        select: 'title projectNumber',
      },
      {
        path: 'account',
        select: 'name',
      },
    ]);
};

exports.getProjectStringByProjectIdAndName = async filter => ProjectString.find(filter);

/**
 * Delete All Project's Project String
 *
 * @param {*} id
 * @param {*} deletedAt
 * @returns
 */
exports.deleteAllProjectString = async (projectId, deletedAt) => {
  return ProjectString.updateMany(
    { project: projectId },
    {
      $set: deletedAt,
    }
  );
};

exports.getDefaultProjectString = async filter => {
  return await ProjectString.findOne(filter);
};
