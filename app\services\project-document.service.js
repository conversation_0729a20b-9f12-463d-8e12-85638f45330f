/* models */
const ProjectDocument = require('../models/project-document.model');
const Member = require('../models/member.model');

/**
 * Create Data
 *
 * @param {*} requestData
 * @returns
 */
exports.createProjectDocument = async requestData => ProjectDocument.create(requestData);

/**
 * Format uploaded document name
 *
 * @param {*} requestData
 * @returns
 */
exports.formatUploadedDocument = async requestData => {
  if (requestData.document) {
    let ext = requestData.document.name.split('.').pop();
    let nowDate = new Date();
    let newDate = `${nowDate.getDate()}-${nowDate.getMonth() + 1}-${nowDate.getFullYear()}`;
    let documentName = `${requestData.type}-${requestData.title}-${requestData.documentNumber}-${newDate}.${ext}`;
    documentName = documentName.replace(/\s/g, '-');
    requestData.document.name = documentName;
    requestData.document.date = nowDate;
  }
  return requestData;
};

/**
 * Get Project Documents
 *
 * @param {*} filter
 * @param {*} page
 * @param {*} perPage
 * @param {*} sort
 * @param {*} active
 * @returns
 */
exports.getProjectDocuments = async (filter, page, perPage, sort, active) => {
  const projectDocument = await ProjectDocument.find(filter, { __v: 0, deletedAt: 0, deletedBy: 0 })
    .sort({ createdAt: sort ?? -1 })
    .limit(perPage)
    .skip(page * perPage)
    .populate(this.populateProjectDocumentFields())
    .lean();

  // Check active document
  const newProjectDocument = projectDocument.map(doc => {
    if (active === null) {
      return doc;
    } else {
      let document = doc.document
        .map(item => {
          if (item.isActive === active) {
            return item;
          }
          return null;
        })
        .filter(item => item !== null);
      return { ...doc, document };
    }
  });
  return newProjectDocument;
};

/**
 * Get Single Project Document
 *
 * @param {*} filter
 * @returns
 */
exports.getSingleProjectDocumentByFilter = async filter => {
  return await ProjectDocument.findOne(filter, { __v: 0, deletedAt: 0, deletedBy: 0 }).populate(
    this.populateProjectDocumentFields()
  );
};

/**
 * Populate Project Document Fields
 *
 * @returns
 */
exports.populateProjectDocumentFields = () => {
  return [
    {
      path: 'project',
      select: { _id: 1, title: 1, projectNumber: 1 },
      strictPopulate: false,
    },
    {
      path: 'account',
      select: { _id: 1, name: 1 },
      strictPopulate: false,
    },
    {
      path: 'createdBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
    {
      path: 'updatedBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
    {
      path: 'docReviews.user',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
  ];
};

/**
 * Update Project Document
 *
 * @param {*} id
 * @param {*} requestData
 * @returns
 */
exports.updateProjectDocument = async (id, requestData) => {
  return await ProjectDocument.findByIdAndUpdate(id, { $set: requestData }, { new: true }).populate(
    this.populateProjectDocumentFields()
  );
};

/**
 * Add Document In Project Document
 *
 * @param {*} id
 * @param {*} requestData
 * @returns
 */
exports.addProjectDocument = async (id, requestData) => {
  return await ProjectDocument.findByIdAndUpdate(id, { $push: requestData }, { new: true });
};

/**
 * Update Document In Project Document
 *
 * @param {*} id
 * @param {*} documentId
 * @param {*} requestData
 * @returns
 */
exports.updateDocumentInProjectDocument = async (id, documentId, requestData) => {
  return await ProjectDocument.findOneAndUpdate(
    { _id: id, 'document._id': documentId },
    { $set: requestData },
    { new: true }
  );
};

/**
 * Delete Project Document
 *
 * @param {*} id
 * @param {*} requestData
 * @returns
 */
exports.deleteProjectDocument = async (id, requestData) => {
  return await ProjectDocument.findByIdAndUpdate(id, { $set: requestData }, { new: true });
};

/**
 * Get Project Documents Summary
 *
 * @param {*} filter
 * @returns
 */
exports.getProjectDocumentsSummary = async filter => {
  const validTypes = await ProjectDocument.distinct('type');

  return ProjectDocument.aggregate([
    {
      $match: {
        ...filter,
        type: { $in: validTypes },
      },
    },
    {
      $unwind: {
        path: '$document',
        preserveNullAndEmptyArrays: false,
      },
    },
    {
      $match: {
        'document.isActive': true,
      },
    },
    {
      $addFields: {
        'document.createdAt': '$createdAt',
        'document.documentNumber': '$documentNumber',
        'document.title': '$title',
      },
    },
    {
      $group: {
        _id: '$type',
        documents: { $push: '$document' },
      },
    },
    {
      $project: {
        type: '$_id',
        documents: 1,
        _id: 0,
      },
    },
  ]);
};

/**
 * Get Project Members
 *
 * @param {*} filter
 * @returns
 */
exports.getProjectMembers = async filter => {
  return await Member.find(filter)
    .populate({
      path: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    })
    .lean();
};

/**
 * Create Reviews For Project Members
 *
 * @param {*} projectId
 * @param {*} accountId
 * @returns
 */
exports.createReviewsForProjectMembers = async (projectId, accountId) => {
  // Get all project members
  const members = await this.getProjectMembers({
    project: projectId,
    account: accountId,
    deletedAt: null,
  });

  // Create review entries for each member
  return members.map(member => ({
    user: member.user._id,
    isAcknowledged: false,
    acknowledgedAt: null,
  }));
};

/**
 * Update reviews for project members
 *
 * @param {*} projectId
 * @param {*} accountId
 * @returns
 */
exports.updateReviewsForProjectMembers = async docReviewData => {
  return docReviewData.map(member => ({
    user: member.user,
    isAcknowledged: false,
    acknowledgedAt: null,
  }));
};

/**
 * Get Non-Acknowledged Documents
 *
 * @param {*} filter
 * @returns
 */
exports.getNonAcknowledgedDocuments = async filter => {
  const documents = await ProjectDocument.find({
    account: filter.account,
    deletedAt: null,
    type: 'safety_notification',
    'document.isActive': true,
    docReviews: {
      $elemMatch: {
        user: filter.userId,
        isAcknowledged: false,
      },
    },
  })
    .populate(this.populateProjectDocumentFields())
    .select({
      docReviews: 0,
      updatedBy: 0,
      deletedBy: 0,
      deletedAt: 0,
      __v: 0,
    })
    .lean();

  // Filter document array to only include active documents
  return documents.map(doc => ({
    ...doc,
    document: doc.document.filter(d => d.isActive === true),
  }));
};

/**
 * Acknowledge Document
 *
 * @param {*} documentId
 * @param {*} userId
 * @returns
 */
exports.acknowledgeDocument = async (documentId, userId) => {
  return await ProjectDocument.findOneAndUpdate(
    {
      _id: documentId,
      'docReviews.user': userId,
    },
    {
      'docReviews.$.isAcknowledged': true,
      'docReviews.$.acknowledgedAt': new Date(),
    }
  );
};
