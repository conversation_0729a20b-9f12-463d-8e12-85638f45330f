/**
 * Change Equipment Names
 *
 * @param {*} requestData
 */
exports.updateCertificateNames = async requestData => {
  requestData.certificateType.forEach((certificate, index) => {
    certificate.name = `${requestData.equipmentNumber}-${certificate.title}${
      requestData.certificateType.length > 1 && certificate.endDate != null ? index + 1 + '-' : ''
    }${
      certificate.endDate != null ? certificate.endDate.split('/').join('') : ''
    }.${certificate.name.split('.').pop()}`;
  });
};

/**
 * Transform Request Data from key value pair to object
 *
 * @param {*} reqBody
 * @returns
 */
exports.transformReqData = async reqBody => {
  let reqData = Object.keys(reqBody)
    .filter(key => !isNaN(key))
    .map(key => reqBody[key]);
  reqData.updatedBy = reqBody.updatedBy;

  return reqData;
};
