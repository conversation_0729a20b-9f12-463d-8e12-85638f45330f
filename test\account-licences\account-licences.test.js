// account-licences

const request = require('supertest');
const app = require('../../app/server');

// create account-licences
describe('POST /api/account-licences/request', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.I513fTkTfDgrekZVxyysoPEtU5Rmtr3nM-7HB7Hj7hE';
  const accountLicencesData = {
    licence: '64180943efe5fa4f6aa1febc',
    permission: ['64181992f24c107fe051b9ea', '64181a270d096caae25f0bf7'],
    isRequested: true,
  };
  it('returns 200 and message Request has been created successfully', async () => {
    const response = await request(app)
      .post('/api/account-licences/request')
      .set('Authorization', `Bearer ${token}`)
      .send(accountLicencesData);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Request has been created successfully',
      status: true,
    });
  });

  it('returns 422 with message Required field cannot be empty.', async () => {
    const response = await request(app)
      .post('/api/account-licences/request')
      .set('Authorization', `Bearer ${token}`)
      .send(accountLicencesData);
    expect(response.status).toBe(422);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Required field cannot be empty.',
      status: false,
    });
  });

  it('returns 404 with message Account already has this permission', async () => {
    const response = await request(app)
      .post('/api/account-licences/request')
      .set('Authorization', `Bearer ${token}`)
      .send(accountLicencesData);
    expect(response.status).toBe(404);
    expect(response.body).toEqual({
      message: 'Account already has this permission',
      status: false,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app)
      .post('/api/account-licences/request')
      .send(accountLicencesData);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});

// getAll account-licences
describe('GET /api/account-licences', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.I513fTkTfDgrekZVxyysoPEtU5Rmtr3nM-7HB7Hj7hE';

  it('returns 200 and message Records has been retireved successfully', async () => {
    const response = await request(app)
      .get('/api/account-licences')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: [{}, {}],
      message: 'Records has been retireved successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).get('/api/account-licences');
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});

// getAll pending-requests account-licences
describe('GET /api/account-licences/pending-requests', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.I513fTkTfDgrekZVxyysoPEtU5Rmtr3nM-7HB7Hj7hE';

  it('returns 200 and message Records has been retireved successfully', async () => {
    const response = await request(app)
      .get('/api/account-licences/pending-requests')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: [{}, {}],
      message: 'Records has been retireved successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).get('/api/account-licences/pending-requests');

    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});

//  update account-licences
describe('PATCH /api/account-licences/:id/respond', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.I513fTkTfDgrekZVxyysoPEtU5Rmtr3nM-7HB7Hj7hE';
  const id = '64181da7a8f52f6f4183a628';
  const updateData = {
    action: 'reject',
    reason: 'testing',
  };
  it('returns 200 and message account-licences has been updated successfully', async () => {
    const response = await request(app)
      .patch(`/api/account-licences/${id}/respond`)
      .set('Authorization', `Bearer ${token}`)
      .send(updateData);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'account-licences has been updated successfully',
      status: true,
    });
  });

  it('returns 422 with message Required field cannot be empty.', async () => {
    const response = await request(app)
      .post(`/api/account-licences/${id}/respond`)
      .set('Authorization', `Bearer ${token}`)
      .send();
    expect(response.status).toBe(422);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Required field cannot be empty.',
      status: false,
    });
  });

  it('returns 404 with message account-licences does not exist', async () => {
    const response = await request(app)
      .post('/api/account-licences/6412a388268e9b25cef57a30/respond')
      .set('Authorization', `Bearer ${token}`)
      .send(updateData);
    expect(response.status).toBe(404);
    expect(response.body).toMatchObject({
      message: 'account-licences does not exist',
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app)
      .patch(`/api/account-licences/${id}/respond`)
      .send(updateData);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});
