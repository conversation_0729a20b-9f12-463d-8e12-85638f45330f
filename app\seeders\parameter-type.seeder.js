/* models */
const ParameterType = require('../models/parameter-type.model');

// Utils
const commonUtils = require('../utils/common.utils');
const constantsUtils = require('../utils/constants.utils');

/* Command to run script
-> npm run seeder parameter-type
parameter-type = seeder file name
*/

/**
 * Prepare and insert parameter-type data in collection
 *
 * @returns
 */
exports.up = async () => {
  //check the parameter-type exist
  const collectionExist = await commonUtils.checkCollectionExists('parameter-types');
  if (collectionExist) {
    console.log(constantsUtils.PARAMETER_ALREADY_EXIST);
    return true;
  }

  // prepare the parameter-type data
  let insertData = [
    {
      name: 'Dropdown',
      uniqueKey: 'dropdown',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      name: 'Short Answer',
      uniqueKey: 'shortAnswer',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      name: 'Date',
      uniqueKey: 'date',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      name: 'Number',
      uniqueKey: 'number',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      name: 'Range',
      uniqueKey: 'range',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      name: 'Check Box',
      uniqueKey: 'checkbox',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      name: 'Image',
      uniqueKey: 'image',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      name: 'Signature',
      uniqueKey: 'signature',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      name: 'Boolean',
      uniqueKey: 'boolean',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      name: 'Date Time',
      uniqueKey: 'dateTime',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  // insert the pre-define data
  return await ParameterType.insertMany(insertData);
};
