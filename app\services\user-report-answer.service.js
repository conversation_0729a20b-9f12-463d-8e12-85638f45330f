/* models */
const UserReportAnswer = require('../models/user-report-answer.model');

// utils
const { toObjectId } = require('../utils/common.utils');

/**
 * Create User Report Answer
 *
 * @param {*} requestData
 * @returns
 */
exports.createUserReportAnswer = async (requestData, session = null) => {
  if (session) {
    return UserReportAnswer.create([requestData], { session });
  } else {
    return UserReportAnswer.create(requestData);
  }
};

exports.updateUserReportAnswerByTitle = async (
  answerId,
  answerTitleId,
  answer = null,
  isPrintable = null
) => {
  let updateData = {};

  if (answer !== null) {
    updateData['answers.$.answer'] = answer;
  }

  if (isPrintable !== null) {
    updateData['answers.$.isPrintable'] = isPrintable;
  }

  return UserReportAnswer.findOneAndUpdate(
    { _id: answerId, 'answers.answerTitleId': answerTitleId },
    { $set: updateData },
    { new: true }
  );
};

exports.updateUserReportAnswerByFilter = async (filter, update) => {
  return UserReportAnswer.findOneAndUpdate(filter, update, { new: true });
};

exports.updateUserReportAnswer = async (id, update, session = null) => {
  return UserReportAnswer.findByIdAndUpdate(id, update, { new: true, session });
};

exports.updateUserReportAnswerManyByFilter = async (filter, update) => {
  return UserReportAnswer.updateMany(filter, update);
};

exports.getSingleUserReportAnswer = async filter => UserReportAnswer.findOne(filter);

/**
 * Unassign Answer Title
 *
 * @param {*} userReportAnswerId
 * @param {*} answerTitleId
 * @returns
 */
exports.unAssignAnswerTitle = async (userReportAnswerId, submittedAnswerTitleId, answerTitleId) => {
  return UserReportAnswer.findOneAndUpdate(
    {
      _id: toObjectId(userReportAnswerId),
      answers: {
        $elemMatch: {
          _id: toObjectId(submittedAnswerTitleId),
          answerTitleId: toObjectId(answerTitleId),
        },
      },
    },
    {
      $set: { 'answers.$.isActive': false },
    },
    { new: true }
  );
};

/**
 * Assign Answer Title

 * @param {*} userReportAnswerId 
 * @param {*} submittedAnserTitleId 
 * @param {*} userAnserTitleNewId 
 * @returns 
 */
exports.assignAnswerTitle = async (
  userReportAnswerId,
  submittedAnswerTitleId,
  userAnswerTitleNewId,
  answer,
  answerId,
  reportQuestion,
  reportQuestionAnswer
) => {
  return await UserReportAnswer.updateOne(
    {
      _id: toObjectId(userReportAnswerId),
      answers: {
        $elemMatch: {
          _id: toObjectId(answerId),
          answerTitleId: toObjectId(submittedAnswerTitleId),
        },
      },
    },
    {
      $set: {
        reportQuestion,
        reportQuestionAnswer,
        'answers.$.answerTitleId': userAnswerTitleNewId,
        'answers.$.answer': answer,
        'answers.$.isActive': true,
      },
    }
  );
};

/**
 * Get Active Answer Title
 *
 * @param {*} userReportAnswerId
 * @param {*} userAnserTitleOldId
 * @returns
 */
exports.getActiveAnswerTitle = async (
  userAnswerTitleNewId,
  reportQuestion,
  reportQuestionAnswer,
  createdBy
) => {
  return UserReportAnswer.findOne({
    reportQuestion,
    reportQuestionAnswer,
    answers: {
      $elemMatch: {
        answerTitleId: toObjectId(userAnswerTitleNewId),
        isActive: true,
      },
    },
    createdBy,
  });
};

exports.getUserReportAnswers = async filter => UserReportAnswer.find(filter);

exports.updateUserReportAnswers = async (id, update) => {
  return UserReportAnswer.findOneAndUpdate(id, update, { new: true });
};

exports.assignUnassignMigrationQuery = async () => {
  return UserReportAnswer.updateMany(
    {
      'answers.isActive': { $exists: false },
    },
    {
      $set: { 'answers.$[].isActive': true },
    }
  );
};
