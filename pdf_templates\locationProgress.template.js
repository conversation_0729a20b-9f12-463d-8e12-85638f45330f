exports.locationProgress = async templateData => {
  return `
  <!DOCTYPE html>
  <html lang="en">
  
  <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Location Progress</title>
      <style>
          html {
              box-sizing: border-box;
          }
  
          *,
          *::before,
          *::after {
              box-sizing: inherit;
          }
  
          :root {
              --primary-color: ${templateData.companyPrimaryColor};
              --primary-font-color: #323232;
              --info-title-color: #4D5464;
              --info-desc-color: #333843;
              --status-color-green: #009A38;
              --status-color-red: #9D0202;
              --circle-bg-color: #D9D9D9;
              --black-color: #000000;
              --info-label-color: #FFFFFF;
              --table-border: #E0E6F5;
              --table-header-border: #E0E6F51A;
              --white-color: #FFFFFF;
              --pdf-bg-color: #F6F7FF;
          }
  
          body {
              margin: 0;
              padding: 0;
              display: flex;
              justify-content: center;
              font-family: sans-serif;
              width: 100%;
              height: 100%;
              color: var(--primary-font-color);
              font-size: 12px;
              font-weight: 500;
          }
  
          .main {
              width: 100%;
             
          }
  
          .pdf-header {
              display: flex;
              width: 100%;
          }
  
          .pdf-header-title {
              font-size: 20px;
              padding: 0;
              margin: 0;
              margin-bottom: 10px;
              font-weight: 600;
          }
  
          .space-container {
              height: 20px;
          }
  
          .custom-table {
              width: 100%;
              border-spacing: 0px;
              border-radius: 4px;
          }
  
          .custom-table tr {
              page-break-inside: avoid;
          }
  
          .custom-table th {
              text-align: left;
              padding-left: 10px;
              background-color: var(--primary-color);
              color: var(--white-color);
          }
  
          .custom-table td {
              padding-left: 10px;
          }
  
          .custom-table th:first-child {
              border-right: 1px solid var(--table-border);
              border-top-left-radius: 4px;
          }
  
          .custom-table td:first-child {
              border-right: 1px solid var(--table-border);
              border-bottom: 1px solid var(--table-border);
              border-left: 1px solid var(--table-border);
          }
  
          .custom-table th:not(:first-child):not(:last-child) {
              border-right: 1px solid var(--table-border);
          }
  
          .custom-table td:not(:first-child):not(:last-child) {
              border-right: 1px solid var(--table-border);
              border-bottom: 1px solid var(--table-border);
          }
  
          .custom-table th:last-child {
              border-right: 0px;
              border-top-right-radius: 4px;
          }
  
          .custom-table td:last-child {
              border-right: 1px solid var(--table-border);
              border-bottom: 1px solid var(--table-border);
          }
  
          .custom-table tr:last-child td:first-child {
              border-bottom-left-radius: 4px;
          }
  
          .custom-table tr:last-child td:last-child {
              border-bottom-right-radius: 4px;
          }
  
          .safety-table-header {
              padding: 10px;
              font-size: 12px;
              font-weight: 600;
          }
  
          .safety-first-table-desc {
              padding: 14px;
              font-size: 12px;
              font-weight: 500;
          }
  
          .text-line {
              display: flex;
              align-items: center;
              margin-top: 20px;
          }
  
          .text-line p {
              margin: 0;
              padding-right: 10px;
              white-space: nowrap;
              font-size: 15px;
              font-weight: 600;
              color: var(--primary-color);
          }
  
          .text-line hr {
              flex-grow: 1;
              border: none;
              border-top: 1px solid var(--primary-color);
              margin: 0;
              margin-left: 5px;
  
          }
  
          .Overall-container {
              display: flex;
              border: 1px solid var(--table-border);
              border-radius: 4px;
          }
  
          .Overall-title {
              width: 50%;
              background-color: var(--primary-color);
              color: var(--white-color);
              padding: 10px;
              border-top-left-radius: 4px;
              border-bottom-left-radius: 4px;
          }
  
          .Overall-percentage {
              width: 50%;
              text-align: right;
              padding: 10px;
              font-weight: 600;
          }
      </style>
  </head>
  
  <body>
      <div class="main">
          <div class="pdf-header">
              <p class="pdf-header-title">Location Progress (Tower 1)</p>
          </div>
          <div class="text-line">
              <p>Location based report</p>
              <hr>
          </div>
          <div class="space-container"></div>
          <table class="custom-table">
              <tr>
                  <th class="safety-table-header">No.</th>
                  <th class="safety-table-header">Report Name</th>
                  <th class="safety-table-header">Location/ Asset</th>
                  <th class="safety-table-header">Scope</th>
                  <th class="safety-table-header">Status</th>
                  <th class="safety-table-header">Comp. Date</th>
                  <th class="safety-table-header">Comp. Per%</th>
              </tr>
              <tr>
                  <td class="safety-first-table-desc">1.1</td>
                  <td class="safety-first-table-desc">Report LA</td>
                  <td class="safety-first-table-desc">Location A</td>
                  <td class="safety-first-table-desc">Scope 1 A</td>
                  <td class="safety-first-table-desc" style="color: var(--status-color-green);">Open</td>
                  <td class="safety-first-table-desc">12-07-2024 11:02:05</td>
                  <td class="safety-first-table-desc">50%</td>
              </tr>
              <tr>
                  <td class="safety-first-table-desc">1.2</td>
                  <td class="safety-first-table-desc">Report LB</td>
                  <td class="safety-first-table-desc">Location B</td>
                  <td class="safety-first-table-desc">Scope 1 B</td>
                  <td class="safety-first-table-desc" style="color: var(--status-color-red);">Close</td>
                  <td class="safety-first-table-desc">24-07-2024 11:02:05</td>
                  <td class="safety-first-table-desc">30%</td>
              </tr>
          </table>
          <div class="text-line">
              <p>Asset Based Report</p>
              <hr>
          </div>
          <div class="space-container"></div>
          <table class="custom-table">
              <tr>
                  <th class="safety-table-header">No.</th>
                  <th class="safety-table-header">Report Name</th>
                  <th class="safety-table-header">Location/ Asset</th>
                  <th class="safety-table-header">Scope</th>
                  <th class="safety-table-header">Status</th>
                  <th class="safety-table-header">Comp. Date</th>
                  <th class="safety-table-header">Comp. Per%</th>
              </tr>
              <tr>
                  <td class="safety-first-table-desc">1.1</td>
                  <td class="safety-first-table-desc">Report LA</td>
                  <td class="safety-first-table-desc">Location A - Location B</td>
                  <td class="safety-first-table-desc">Scope 1 A</td>
                  <td class="safety-first-table-desc" style="color: var(--status-color-green);">Open</td>
                  <td class="safety-first-table-desc">12-07-2024 11:02:05</td>
                  <td class="safety-first-table-desc">50%</td>
              </tr>
              <tr>
                  <td class="safety-first-table-desc">1.2</td>
                  <td class="safety-first-table-desc">Report LB</td>
                  <td class="safety-first-table-desc">Location A - Location B</td>
                  <td class="safety-first-table-desc">Scope 1 B</td>
                  <td class="safety-first-table-desc" style="color: var(--status-color-red);">Close</td>
                  <td class="safety-first-table-desc">24-07-2024 11:02:05</td>
                  <td class="safety-first-table-desc">30%</td>
              </tr>
          </table>
  
          <div class="text-line">
              <p>Multiple Asset based Report</p>
              <hr>
          </div>
          <div class="space-container"></div>
          <table class="custom-table">
              <tr>
                  <th class="safety-table-header">No.</th>
                  <th class="safety-table-header">Report Name</th>
                  <th class="safety-table-header">Location/ Asset</th>
                  <th class="safety-table-header">Scope</th>
                  <th class="safety-table-header">Status</th>
                  <th class="safety-table-header">Comp. Date</th>
                  <th class="safety-table-header">Comp. Per%</th>
              </tr>
              <tr>
                  <td class="safety-first-table-desc">1.1</td>
                  <td class="safety-first-table-desc">Report LA</td>
                  <td class="safety-first-table-desc">Location A - Location B</td>
                  <td class="safety-first-table-desc">Scope 1 A</td>
                  <td class="safety-first-table-desc" style="color: var(--status-color-green);">Open</td>
                  <td class="safety-first-table-desc">12-07-2024 11:02:05</td>
                  <td class="safety-first-table-desc">50%</td>
              </tr>
              <tr>
                  <td class="safety-first-table-desc">1.2</td>
                  <td class="safety-first-table-desc">Report LB</td>
                  <td class="safety-first-table-desc">Location A - Location B</td>
                  <td class="safety-first-table-desc">Scope 1 B</td>
                  <td class="safety-first-table-desc" style="color: var(--status-color-red);">Close</td>
                  <td class="safety-first-table-desc">24-07-2024 11:02:05</td>
                  <td class="safety-first-table-desc">30%</td>
              </tr>
          </table>
          <div class="space-container"></div>
          <div class="Overall-container">
              <span class="Overall-title">Overall Completion Per%</span>
              <span class="Overall-percentage">40.71%</span>
          </div>
          <div class="space-container"></div>
          <div class="space-container"></div>
      </div>
  </body>
  
  </html>
    `;
};
