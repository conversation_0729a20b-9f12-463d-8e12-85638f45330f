const mongoose = require('mongoose');

const EquipmentWarehouse = new mongoose.Schema(
  {
    equipment: {
      type: mongoose.Types.ObjectId,
      ref: 'equipment',
      required: true,
    },
    warehouse: {
      type: mongoose.Types.ObjectId,
      ref: 'warehouse',
      required: true,
    },
    quantity: {
      type: Number,
    },
    equipmentLocationInWarehouse: {
      type: String,
    },
    equipmentCurrentLocation: {
      type: String,
    },
    equipmentLocationFromDate: {
      type: Date,
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('equipment-warehouse', EquipmentWarehouse);
