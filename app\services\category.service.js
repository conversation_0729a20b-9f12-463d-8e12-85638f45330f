const Category = require('../models/category.model');

/**
 * Create Category
 *
 * @param {*} category
 * @returns
 */
exports.createCategory = async category => {
  return await Category.create(category);
};

/**
 * Update By Id
 *
 * @param {*} id
 * @param {*} category
 * @returns
 */
exports.updateCategory = async (id, category) => {
  return Category.findByIdAndUpdate(id, { $set: category }, { new: true });
};

/**
 * Delete By Id
 *
 * @param {*} id
 * @returns
 */
exports.deleteCategory = async (id, deletedAt) => {
  return Category.findByIdAndUpdate(id, { $set: deletedAt });
};

/**
 * Get All Category
 *
 * @param {*} page
 * @param {*} perPage
 * @returns
 */
exports.getAllCategory = async (filter, page, perPage) => {
  let category = Category.find(filter, { _id: 1, createdAt: 0, updatedAt: 0, __v: 0 });

  if (page !== '' && perPage !== '') {
    category.limit(perPage).skip(page * perPage);
  }
  return category;
};

/**
 * Get Category By Name
 *
 * @param {*} categoryName
 * @returns
 */
exports.getCategoryByName = async (categoryName, account) => {
  return Category.find({
    $and: [{ categoryName: categoryName }, { account: account }, { deletedAt: null }],
  });
};

/**
 * Get Category By Id
 *
 * @param {*} id
 * @returns
 */
exports.getCategoryById = async id => {
  return Category.findById({ _id: id }, { _id: 0, createdAt: 0, updatedAt: 0, __v: 0 });
};
