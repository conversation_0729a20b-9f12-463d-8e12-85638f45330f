const axios = require('axios');
const admZip = require('adm-zip');

exports.generateUserWiseZip = async (projectList, res, folderName) => {
  try {
    const zipFileName = `${folderName}.zip`;
    const zipFileStream = new admZip();

    const getImageAsBuffer = async imageUrl => {
      try {
        const response = await axios.get(imageUrl, {
          responseType: 'arraybuffer',
        });
        return Buffer.from(response.data, 'binary');
      } catch (error) {
        return null;
      }
    };
    let profileCertificates = projectList['trainingMatrix'];
    for (let projectKey in profileCertificates) {
      for (let functionKey in profileCertificates[projectKey]) {
        const data = profileCertificates[projectKey][functionKey]['data'];
        for (let user of Object.keys(data)) {
          const userObj = data[user];
          for (let certi of userObj.userCertificates) {
            if (projectList.requiredCertificate.length !== 0) {
              const isrequiredCertificate = projectList.requiredCertificate.find(
                item =>
                  item.function.functionName === functionKey &&
                  item.certificate._id.toString() === certi.certificateType.toString()
              );
              if (isrequiredCertificate) {
                const certFolderName = `${functionKey}/${userObj.name}/`;
                const certFileName = `${certFolderName}${certi.fileName}`;
                const fileBuffer = await getImageAsBuffer(certi.link);
                if (fileBuffer) {
                  zipFileStream.addFile(certFileName, fileBuffer, '', 0o644);
                }
              }
            } else {
              const certFolderName = `${functionKey}/${userObj.name}/`;
              const certFileName = `${certFolderName}${certi.fileName}`;
              const fileBuffer = await getImageAsBuffer(certi.link);
              if (fileBuffer) {
                zipFileStream.addFile(certFileName, fileBuffer, '', 0o644);
              }
            }
          }
        }
      }
    }

    res.setHeader('Content-Type', 'application/zip');
    res.setHeader('Content-Disposition', `attachment; filename=${zipFileName}`);
    res.setHeader('Access-Control-Expose-Headers', 'Content-Disposition');
    const zipBuffer = zipFileStream.toBuffer();
    res.send(zipBuffer);
    res.end();
  } catch (error) {
    throw new Error(error);
  }
};
