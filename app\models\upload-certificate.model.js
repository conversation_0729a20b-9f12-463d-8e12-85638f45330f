const mongoose = require('mongoose');

const uploadCertificate = new mongoose.Schema(
  {
    user: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    certificateType: {
      type: mongoose.Types.ObjectId,
      ref: 'certificate-type',
    },
    link: {
      type: String,
      required: true,
    },
    fileName: {
      type: String,
      required: true,
    },
    size: {
      type: Number,
    },
    name: {
      type: String,
      required: false,
    },
    status: {
      type: String,
      enum: ['approved', 'rejected', 'pending', 'expired'],
      default: 'pending',
    },
    startDate: {
      type: Date,
      default: null,
    },
    endDate: {
      type: Date,
      default: null,
    },
    reason: {
      type: String,
      default: null,
    },
    version: {
      type: Number,
      default: 1,
    },
    isActive: {
      type: Boolean,
      default: false,
    },
    internal: {
      type: Boolean,
      default: false,
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

uploadCertificate.index({ user: 1, account: 1, deletedAt: 1 });
uploadCertificate.index({ account: 1, status: 1, deletedAt: 1 });
uploadCertificate.index({ account: 1, user: 1, status: 1, isActive: 1, deletedAt: 1 });

module.exports = mongoose.model('upload-certificate', uploadCertificate);
