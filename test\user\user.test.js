const request = require('supertest');
const app = require('../../app/server');
const { DEFAULT_PASSWORD } = process.env;
const constantUtils = require('../utils/constants.utils');

// create user
describe('POST /api/users', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.DMcFR_oQkiuhQruGWpQxrGpY0vjFRdwHGpXad2Ks5pc';

  const userData = {
    firstName: 'Ravi',
    lastName: 'Patel',
    email: '<EMAIL>',
    password: DEFAULT_PASSWORD,
    role: ['admin'],
    contactNumber: '+***********',
    emergencyContactNumber: '+***********',
    account: '63f34946ccf1c13060d353ee',
    secondaryEmail: '',
    nationality: '',
    motherLanguage: '',
    placeOfBirth: '',
    address: 'New street',
    gender: 'male',
    country: 'India',
    certificate: ['https://dummyimage.com/640x360/fff/aaa'],
    birthdate: '',
    maritalStatus: '',
    ssn: '',
    bloodGroup: '',
    allergies: '',
    companyName: '',
    profileImage: 'https://placehold.jp/150x150.png',
    companyLogo: 'https://via.placeholder.com/150',
  };
  it('returns 201 and message "user created', async () => {
    const response = await request(app).post('/api/users ').send(userData);
    expect(response.status).toBe(200);
    expect(response.body.data).toHaveProperty('__v');
    expect(response.body.data).toHaveProperty('_id');

    expect(response.body.data).toHaveProperty('account');
    expect(response.body.data).toHaveProperty('address');
    expect(response.body.data).toHaveProperty('allergies');
    expect(response.body.data).toHaveProperty('birthdate');
    expect(response.body.data).toHaveProperty('bloodGroup');
    expect(response.body.data).toHaveProperty('certificate');
    expect(response.body.data).toHaveProperty('companyLogo');
    expect(response.body.data).toHaveProperty('companyName');
    expect(response.body.data).toHaveProperty('contactNumber');
    expect(response.body.data).toHaveProperty('country');
    expect(response.body.data).toHaveProperty('createdAt');
    expect(response.body.data).toHaveProperty('email');
    expect(response.body.data).toHaveProperty('emergencyContactNumber');
    expect(response.body.data).toHaveProperty('firstName');
    expect(response.body.data).toHaveProperty('gender');
    expect(response.body.data).toHaveProperty('isActive');
    expect(response.body.data).toHaveProperty('isDeleted');
    expect(response.body.data).toHaveProperty('lastName');

    expect(response.body.data).toHaveProperty('maritalStatus');
    expect(response.body.data).toHaveProperty('motherLanguage');
    expect(response.body.data).toHaveProperty('nationality');
    expect(response.body.data).toHaveProperty('password');
    expect(response.body.data).toHaveProperty('placeOfBirth');
    expect(response.body.data).toHaveProperty('profileImage');
    expect(response.body.data).toHaveProperty('resetExpiresIn');
    expect(response.body.data).toHaveProperty('resetToken');

    expect(response.body.data).toHaveProperty('role');
    expect(response.body.data).toHaveProperty('secondaryEmail');
    expect(response.body.data).toHaveProperty('ssn');
    expect(response.body.data).toHaveProperty('updatedAt');
  });

  it('returns 422 with message Required field cannot be empty.', async () => {
    const response = await request(app)
      .post('/api/users')
      .set('Authorization', `Bearer ${token}`)
      .send(userData);
    expect(response.status).toBe(422);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Required field cannot be empty.',
      status: false,
    });
  });

  it('returns 400 with missing field', async () => {
    const response = await request(app).post('/api/users').send({});
    expect(response.status).toBe(422);
    expect(response.body).toEqual({
      data: {
        error: [
          { firstName: 'Please enter valid first name.' },
          { lastName: 'Please enter valid last name.' },
          { email: constantUtils.INVALID_VALUE },
          { email: 'Please Enter valid Email.' },
          { contactNumber: constantUtils.INVALID_VALUE },
          { contactNumber: 'Please Enter valid Contact number.' },
          { emergencyContactNumber: constantUtils.INVALID_VALUE },
          { emergencyContactNumber: 'Please Enter valid Contact number.' },
          { password: constantUtils.INVALID_VALUE },
          {
            password: constantUtils.INVALID_PASSWORD_CHARS,
          },
          { password: constantUtils.INVALID_PASSWORD_LENGTH },
        ],
      },
      message: 'Required field cannot be empty.',
      status: false,
    });
  });

  it('returns 401 with already email exsits', async () => {
    const response = await request(app).post('/api/users').send(userData);
    expect(response.status).toBe(401);
    expect(response.body).toEqual({ message: 'Email Id already exists', status: false });
  });
});

// getAll user
describe('GET /api/users', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.DMcFR_oQkiuhQruGWpQxrGpY0vjFRdwHGpXad2Ks5pc';
  it('returns 200 and message User list was retireved successfully', async () => {
    const response = await request(app).get('/api/users ').set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: [{}],
      message: 'User list was retireved successfully',
      status: true,
    });
  });
});

// get user profile
describe('GET /api/users/profile', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.DMcFR_oQkiuhQruGWpQxrGpY0vjFRdwHGpXad2Ks5pc';
  it('returns 200 and message User fetched successfully', async () => {
    const response = await request(app)
      .get('/api/users/profile')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'User fetched successfully',
      status: true,
    });
  });

  it('returns 400 and message Please send authentication token. ', async () => {
    const response = await request(app).get('/api/users/profile');
    expect(response.status).toBe(400);
    expect(response.body).toMatchObject({
      message: 'Please send authentication token.',
      status: false,
    });
  });

  it('returns 400 and message No user exist', async () => {
    const response = await request(app)
      .get('/api/users/profile')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(400);
    expect(response.body).toMatchObject({ message: 'No user exist', status: true });
  });
});

// delete user
describe('DELETE /api/users/:id', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.SlrmRE4cUHa9FcnW7jZMiyInQPHUNo3gkHmSpzyn9N0';
  const id = '64116df2b735559311b7deca';

  it('returns 200 and message User Deleted Successfully', async () => {
    const response = await request(app)
      .delete(`/api/users/${id}`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'User Deleted Successfully',
      status: true,
    });
  });

  it('returns 400 and message User cannot access or no user exits', async () => {
    const response = await request(app)
      .delete(`/api/users/${id}`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(400);
    expect(response.body).toMatchObject({
      message: 'User cannot access or no user exits',
      status: false,
    });
  });

  it('returns 400 and message Please send authentication token. ', async () => {
    const response = await request(app).delete(`/api/users/${id}`);
    expect(response.status).toBe(400);
    expect(response.body).toMatchObject({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});

// update user
describe('PATCH /api/users/:id', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.5URUVijJoKkOE_gY7UlymEhKOzW1rrn5OaNWRNz-VV4';
  const id = '641170af8e0d16ff25a1b0d0';
  const userData = {
    contactNumber: '00',
    emergencyContactNumber: '0000',
  };
  it('returns 200 and message user updated', async () => {
    const response = await request(app)
      .patch(`/api/users/${id}`)
      .set('Authorization', `Bearer ${token}`)
      .send(userData);
    expect(response.status).toBe(200);
    expect(response.body.data).toHaveProperty('__v');
    expect(response.body.data).toHaveProperty('_id');

    expect(response.body.data).toHaveProperty('account');
    expect(response.body.data).toHaveProperty('address');
    expect(response.body.data).toHaveProperty('allergies');
    expect(response.body.data).toHaveProperty('birthdate');
    expect(response.body.data).toHaveProperty('bloodGroup');
    expect(response.body.data).toHaveProperty('certificate');
    expect(response.body.data).toHaveProperty('companyLogo');
    expect(response.body.data).toHaveProperty('companyName');
    expect(response.body.data).toHaveProperty('contactNumber');
    expect(response.body.data).toHaveProperty('country');
    expect(response.body.data).toHaveProperty('createdAt');
    expect(response.body.data).toHaveProperty('email');
    expect(response.body.data).toHaveProperty('emergencyContactNumber');
    expect(response.body.data).toHaveProperty('firstName');
    expect(response.body.data).toHaveProperty('gender');
    expect(response.body.data).toHaveProperty('isActive');
    expect(response.body.data).toHaveProperty('isDeleted');
    expect(response.body.data).toHaveProperty('lastName');

    expect(response.body.data).toHaveProperty('maritalStatus');
    expect(response.body.data).toHaveProperty('motherLanguage');
    expect(response.body.data).toHaveProperty('nationality');
    expect(response.body.data).toHaveProperty('password');
    expect(response.body.data).toHaveProperty('placeOfBirth');
    expect(response.body.data).toHaveProperty('profileImage');
    expect(response.body.data).toHaveProperty('resetExpiresIn');
    expect(response.body.data).toHaveProperty('resetToken');

    expect(response.body.data).toHaveProperty('role');
    expect(response.body.data).toHaveProperty('secondaryEmail');
    expect(response.body.data).toHaveProperty('ssn');
    expect(response.body.data).toHaveProperty('updatedAt');
  });

  it('returns 400 with missing field', async () => {
    const response = await request(app)
      .patch(`/api/users/${id}`)
      .set('Authorization', `Bearer ${token}`)
      .send({});
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      data: {
        error: [
          { firstName: 'Please enter valid first name.' },
          { lastName: 'Please enter valid last name.' },
          { email: constantUtils.INVALID_VALUE },
          { email: 'Please Enter valid Email.' },
          { contactNumber: constantUtils.INVALID_VALUE },
          { contactNumber: 'Please Enter valid Contact number.' },
          { emergencyContactNumber: constantUtils.INVALID_VALUE },
          { emergencyContactNumber: 'Please Enter valid Contact number.' },
          { password: constantUtils.INVALID_VALUE },
          {
            password: constantUtils.INVALID_PASSWORD_CHARS,
          },
          { password: constantUtils.INVALID_PASSWORD_LENGTH },
        ],
      },
      message: 'Required field cannot be empty.',
      status: false,
    });
  });

  it('returns 401 with already email exsits', async () => {
    const response = await request(app)
      .patch(`/api/users/${id}`)
      .set('Authorization', `Bearer ${token}`)
      .send(userData);
    expect(response.status).toBe(401);
    expect(response.body).toEqual({ message: 'Email Id already exists', status: false });
  });
});

// get userbyid
describe('GET /api/users/:id', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.5URUVijJoKkOE_gY7UlymEhKOzW1rrn5OaNWRNz-VV4';
  const id = '641170af8e0d16ff25a1b0d0';
  it('returns 200 and message User fetched successfully', async () => {
    const response = await request(app)
      .get(`/api/users/${id}`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'User fetched successfully',
      status: true,
    });
  });

  it('returns 400 and message Please send authentication token. ', async () => {
    const response = await request(app).get(`/api/users/${id}`);
    expect(response.status).toBe(400);
    expect(response.body).toMatchObject({
      message: 'Please send authentication token.',
      status: false,
    });
  });

  it('returns 400 and message No user exist', async () => {
    const response = await request(app)
      .get(`/api/users/${id}`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(400);
    expect(response.body).toMatchObject({ message: 'No user exist', status: true });
  });
});

// get userbyid
describe('GET /api/users/my-profile', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.5URUVijJoKkOE_gY7UlymEhKOzW1rrn5OaNWRNz-VV4';
  it('returns 200 and message User fetched successfully', async () => {
    const response = await request(app)
      .get('/api/users/my-profile')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'User fetched successfully',
      status: true,
    });
  });

  it('returns 400 and message Please send authentication token. ', async () => {
    const response = await request(app).get('/api/users/my-profile');
    expect(response.status).toBe(400);
    expect(response.body).toMatchObject({
      message: 'Please send authentication token.',
      status: false,
    });
  });

  it('returns 400 and message No user exist', async () => {
    const response = await request(app)
      .get('/api/users/my-profile')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(400);
    expect(response.body).toMatchObject({ message: 'No user exist', status: true });
  });
});

// update user
describe('PATCH /api/users/change-status', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.75GU0lUJmVgpSMLfuigjXCT1i7GwX7rZfw8GbNlSmmI';
  it('returns 200 and message Status changed', async () => {
    const response = await request(app)
      .patch('/api/users/change-status')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).to({ message: 'Status changed', status: true });
  });
  it('returns 400 and message Please send authentication token. ', async () => {
    const response = await request(app).patch('/api/users/change-status');
    expect(response.status).toBe(400);
    expect(response.body).toMatchObject({
      message: 'Please send authentication token.',
      status: false,
    });
  });
  it('returns 400 and message No user exist', async () => {
    const response = await request(app)
      .patch('/api/users/change-status')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(400);
    expect(response.body).to({ message: 'No user exist', status: true });
  });
});
