/* models */
const reportDocument = require('../models/report-document.model');

/**
 * Create Report Document
 *
 * @param {*} requestData
 * @returns
 */
exports.createReportDocument = async requestData => reportDocument.create(requestData);

/**
 * update Report Document
 *
 * @param {*} filter
 * @returns
 */
exports.updateReportDocument = async (filter, reqData) => {
  return reportDocument.findOneAndUpdate(filter, { $set: reqData }, { new: true });
};

/**
 * get Report Document
 *
 * @param {*} filter
 * @returns
 */
exports.getReportDocument = async filter => {
  return reportDocument.findOne(filter);
};

/**
 * get Report Document By user Project Report
 *
 * @param {*} filter
 * @returns
 */
exports.getReportDocumentByReportProject = async filter => {
  return reportDocument.find(filter);
};
