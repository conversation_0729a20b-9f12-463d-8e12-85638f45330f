/* models */
const Location = require('../models/location.model');
const aggregateComponent = require('../utils/aggregate-component.utils');
const { toObjectId } = require('../utils/common.utils');

/**
 * Create User
 *
 * @param {*} location
 * @returns
 */
exports.createLocation = async location => {
  return await Location.create(location);
};

/**
 * Get all locations
 *
 * @returns
 */
exports.getAllLocation = async (
  filter = {},
  page = null,
  perPage = null,
  sort = { createdAt: -1 }
) => {
  return Location.find(filter, { _id: 0, createdAt: 0, updatedAt: 0, __v: 0 })
    .collation({ locale: 'en', strength: 2 })
    .sort(sort)
    .limit(perPage)
    .skip(page * perPage)
    .select('_id')
    .populate([
      {
        path: 'account account',
        select: { name: 1, _id: 1 },
        strictPopulate: false,
      },
      {
        path: 'project project',
        select: { title: 1, _id: 1 },
        strictPopulate: false,
      },
    ]);
};

/**
 * Update By Id
 *
 * @param {*} id
 * @param {*} location
 * @returns
 */
exports.updateLocation = async (id, location) => {
  return Location.findByIdAndUpdate(id, { $set: location }, { new: true }).populate([
    {
      path: 'account account',
      select: { name: 1, _id: 1 },
      strictPopulate: false,
    },
    {
      path: 'project project',
      select: { title: 1, _id: 1 },
      strictPopulate: false,
    },
  ]);
};

/**
 * Delete By Id
 *
 * @param {*} id
 * @param {*} deletedAt
 * @returns
 */
exports.deleteLocation = async (id, deletedAt) => {
  return Location.findByIdAndUpdate(id, { $set: deletedAt });
};

/**
 * Get Location By Name
 *
 * @param {*} locationName
 * @returns
 */
exports.getLocationByName = async filter => {
  let filterAndData = [
    { account: filter.account },
    { project: filter.project },
    { title: filter.title },
    { deletedAt: null },
  ];
  filterAndData = filter?._id ? [...filterAndData, { _id: filter._id }] : filterAndData;
  filterAndData = filter?.isDefault
    ? [...filterAndData, { isDefault: filter.isDefault }]
    : filterAndData;
  return Location.findOne({
    $and: filterAndData,
  });
};
/**
 * Get Location By Id
 *
 * @param {*} id
 * @returns
 */
exports.getLocationById = async id => {
  return Location.findById(id, { _id: 0, createdAt: 0, updatedAt: 0, __v: 0 })
    .select('_id')
    .populate([
      {
        path: 'account account',
        select: { name: 1, _id: 1 },
        strictPopulate: false,
      },
      {
        path: 'project project',
        select: { title: 1, _id: 1 },
        strictPopulate: false,
      },
    ]);
};

/**
 * Get individual location
 *
 * @param {*} id
 * @returns
 */
exports.getLocation = async id => {
  return Location.findOne(
    {
      $and: [{ _id: id }, { deletedAt: null }],
    },
    { _id: 0, createdAt: 0, updatedAt: 0, __v: 0 }
  )
    .select('_id')
    .populate([
      {
        path: 'account account',
        select: { name: 1, _id: 1 },
        strictPopulate: false,
      },
      {
        path: 'project project',
        select: { title: 1, _id: 1 },
        strictPopulate: false,
      },
    ]);
};

/**
 * Get all location by account
 *
 * @param {*} filter
 * @returns
 */
exports.getAllLocationByAccountId = async filter => {
  return Location.find(filter, { _id: 0, createdAt: 0, updatedAt: 0, __v: 0 })
    .select('_id')
    .populate([
      {
        path: 'account account',
        select: { name: 1, _id: 1 },
        strictPopulate: false,
      },
      {
        path: 'project project',
        select: { title: 1, _id: 1 },
        strictPopulate: false,
      },
    ])
    .sort({ createdAt: -1 });
};

exports.getAllData = async filter => {
  filter.deletedAt = null;
  filter.isDefault = false;

  return Location.find(filter, { createdAt: 0, updatedAt: 0, __v: 0 })
    .populate([
      {
        path: 'account account',
        select: { name: 1, _id: 1 },
        strictPopulate: false,
      },
      {
        path: 'project project',
        select: { title: 1, _id: 1 },
        strictPopulate: false,
      },
    ])
    .sort({ createdAt: -1 });
};

/**
 * Delete All Project Locations
 *
 * @param {*} projectId
 * @param {*} deletedAt
 * @returns
 */
exports.deleteAllProjectLocations = async (projectId, deletedAt) => {
  return await Location.updateMany(
    { project: projectId },
    {
      $set: deletedAt,
    }
  );
};

exports.insertManyLocations = async locations => {
  return await Location.insertMany(locations);
};

exports.getDefaultLocation = async filter => {
  return await Location.find(filter);
};

exports.pushReportInLocation = async (id, report) => {
  return Location.findByIdAndUpdate(id, { $push: { reports: report } });
};

exports.pullReportInLocation = async (id, report) => {
  return Location.findByIdAndUpdate(id, { $pull: { reports: report } });
};

exports.getLocationsReport = async locationId => {
  let pipeline = [
    {
      $match: { _id: toObjectId(locationId) },
    },
    {
      $lookup: {
        from: 'reports',
        localField: 'reports',
        foreignField: '_id',
        as: 'reports',
        pipeline: [
          {
            $sort: { sortOrder: 1 },
          },
          {
            $match: { deletedAt: null, isPublish: true, isProgressable: true },
          },
          {
            $project: {
              _id: 1,
              title: 1,
              type: 1,
              scopes: 1,
            },
          },
          {
            $lookup: {
              from: 'scopes',
              localField: '_id',
              foreignField: 'reports',
              as: 'scopes',
              pipeline: [
                {
                  $project: {
                    _id: 1,
                    name: 1,
                  },
                },
              ],
            },
          },
          {
            $lookup: {
              from: 'report-questions',
              localField: '_id',
              foreignField: 'report',
              as: 'questions',
              pipeline: [
                {
                  $match: { deletedAt: null },
                },
                { $group: { _id: null, totalDuration: { $sum: '$duration' } } },
              ],
            },
          },
          {
            $addFields: {
              scopes: '$scopes',
              totalDuration: '$questions.totalDuration',
            },
          },
        ],
      },
    },
    {
      $project: {
        _id: 1,
        title: 1,
        reports: {
          _id: 1,
          title: 1,
          type: 1,
          scopes: 1,
          totalDuration: 1,
        },
      },
    },
  ];

  return Location.aggregate(pipeline);
};

exports.getAllLocationByProjectForProjectTracker = async filter => {
  let pipeline = [
    {
      $match: filter,
    },
    {
      $lookup: {
        from: 'reports',
        localField: 'reports',
        foreignField: '_id',
        as: 'reports',
        pipeline: [
          {
            $match: { deletedAt: null, isPublish: true, isProgressable: true },
          },
          {
            $project: {
              _id: 1,
              title: 1,
              type: 1,
              createdAt: 1,
              scopes: 1,
            },
          },
        ],
      },
    },
    { $unwind: '$reports' },
    {
      $lookup: {
        from: 'scopes',
        localField: 'reports._id',
        foreignField: 'reports',
        as: 'scopes',
        pipeline: [
          {
            $project: {
              _id: 1,
              name: 1,
            },
          },
        ],
      },
    },
    { $unwind: '$scopes' },
    {
      $lookup: {
        from: 'report-questions',
        localField: 'reports._id',
        foreignField: 'report',
        as: 'questions',
        pipeline: [
          {
            $match: { deletedAt: null },
          },
          { $group: { _id: null, totalDuration: { $sum: '$duration' } } },
        ],
      },
    },
    {
      $unwind: '$questions',
    },
    {
      $addFields: {
        'reports.scopes': '$scopes',
        'reports.totalDuration': '$questions.totalDuration',
      },
    },
    {
      $group: {
        _id: '$_id',
        title: { $first: '$title' },
        reports: { $push: '$reports' },
        createdAt: { $first: '$createdAt' },
      },
    },
    {
      $sort: { createdAt: 1 },
    },
    {
      $project: {
        _id: 1,
        title: 1,
        reports: {
          _id: 1,
          title: 1,
          type: 1,
          scopes: 1,
          totalDuration: 1,
        },
      },
    },
  ];

  return Location.aggregate(pipeline);
};

/**
 * Get Location Which Has Assets
 *
 * @param {*} filter
 * @returns
 */
exports.getLocationWhichHasAssets = async filter => {
  let pipeline = [
    {
      $match: filter,
    },
    aggregateComponent.aggregateLookup('project'),
    aggregateComponent.aggregateUnwind('project'),
    aggregateComponent.aggregateLookup('account'),
    aggregateComponent.aggregateUnwind('account'),
    {
      $lookup: {
        from: 'assets',
        as: 'assets',
        let: { locationId: '$_id' },
        pipeline: [
          {
            $match: {
              $expr: {
                $or: [
                  { $eq: ['$toLocation', '$$locationId'] },
                  { $eq: ['$fromLocation', '$$locationId'] },
                ],
              },
              deletedAt: null,
            },
          },
          {
            $project: {
              _id: 1,
              cableName: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$assets',
    },
    {
      $group: {
        _id: '$_id',
        title: { $first: '$title' },
        project: { $first: '$project' },
        account: { $first: '$account' },
        assets: { $addToSet: '$assets' },
        createdAt: { $first: '$createdAt' },
      },
    },
    {
      $sort: { createdAt: 1 },
    },
  ];

  return Location.aggregate(pipeline);
};
