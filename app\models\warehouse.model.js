const mongoose = require('mongoose');

const Warehouse = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
    },
    street: {
      type: String,
      required: true,
    },
    city: {
      type: String,
      required: true,
    },
    state: {
      type: String,
      required: true,
    },
    country: {
      type: String,
      required: true,
    },
    zipCode: {
      type: String,
      required: true,
    },
    contactNumber: {
      type: Object,
      default: { number: '', code: '', in: '' },
      required: true,
    },
    email: {
      type: String,
      required: true,
    },
    image: {
      type: String,
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

Warehouse.index({ account: 1, deletedAt: 1 });
Warehouse.index({ account: 1, deletedAt: 1, isActive: 1 });
Warehouse.index({ name: 1 });
Warehouse.index({ isActive: 1 });

module.exports = mongoose.model('warehouse', Warehouse);
