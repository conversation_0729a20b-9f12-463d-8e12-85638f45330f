const EquipmentQuantityType = require('../models/equipment-quantity-type.model');

// Model Enums
exports.modelEnums = {
  BUY: 'buy',
  RENTAL: 'rental',
  UNIQUE: 'unique',
  MULTIPLE: 'multiple',
};

/**
 * Create EquipmentQuantityType
 *
 * @param {*} requestData
 * @returns
 */
exports.createEquipmentQuantityType = async requestData => {
  return await EquipmentQuantityType.create(requestData);
};

/**
 * Filter EquipmentQuantityType
 *
 * @param {*} filter
 * @param {*} perPage
 * @param {*} page
 * @param {*} sort
 * @returns
 */
exports.getEquipmentQuantityType = async (filter, page, perPage, sort) => {
  return await EquipmentQuantityType.find(filter, {
    deletedAt: 0,
    deletedBy: 0,
    __v: 0,
  })
    .sort({ createdAt: sort ?? -1 })
    .limit(perPage)
    .skip(page * perPage)
    .populate([
      {
        path: 'account',
        select: { _id: 1, name: 1 },
        strictPopulate: false,
      },
      {
        path: 'createdBy',
        model: 'user',
        select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      },
      {
        path: 'updatedBy',
        model: 'user',
        select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      },
      {
        path: 'deletedBy',
        model: 'user',
        select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      },
    ]);
};

/**
 * Get EquipmentQuantityType by Id
 *
 * @param {*} id
 * @returns
 */
exports.getEquipmentQuantityTypeById = async id => {
  return await EquipmentQuantityType.findOne({ _id: id, deletedAt: null, isActive: true }).populate(
    [
      {
        path: 'account',
        select: { _id: 1, name: 1 },
        strictPopulate: false,
      },
      {
        path: 'createdBy',
        model: 'user',
        select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      },
      {
        path: 'updatedBy',
        model: 'user',
        select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      },
      {
        path: 'deletedBy',
        model: 'user',
        select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      },
    ]
  );
};

/**
 * Update EquipmentQuantityType
 *
 * @param {*} id
 * @param {*} requestData
 * @returns
 */
exports.updateEquipmentQuantityType = async (id, requestData) => {
  return await EquipmentQuantityType.findByIdAndUpdate(id, { $set: requestData }, { new: true });
};

/**
 * Delete EquipmentQuantityType
 *
 * @param {*} id
 * @param {*} deletedAt
 * @returns
 */
exports.deleteEquipmentQuantityType = async (id, deletedAt) => {
  return await EquipmentQuantityType.findByIdAndUpdate(id, { $set: deletedAt }, { new: true });
};
